globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(ssr)/./src/app/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Documents\\augment-projects\\infapapp\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\infapapp\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\infapapp\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\infapapp\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\infapapp\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\infapapp\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\infapapp\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\infapapp\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\infapapp\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\infapapp\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\infapapp\\node_modules\\next\\dist\\client\\components\\static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\infapapp\\node_modules\\next\\dist\\esm\\client\\components\\static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\infapapp\\src\\app\\page.tsx":{"id":"(app-pages-browser)/./src/app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\infapapp\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Cairo\",\"arguments\":[{\"subsets\":[\"arabic\",\"latin\"],\"weight\":[\"300\",\"400\",\"600\",\"700\"],\"variable\":\"--font-cairo\"}],\"variableName\":\"cairo\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Cairo\",\"arguments\":[{\"subsets\":[\"arabic\",\"latin\"],\"weight\":[\"300\",\"400\",\"600\",\"700\"],\"variable\":\"--font-cairo\"}],\"variableName\":\"cairo\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\infapapp\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Documents\\augment-projects\\infapapp\\src\\app\\page":[],"C:\\Users\\<USER>\\Documents\\augment-projects\\infapapp\\src\\app\\layout":["static/css/app/layout.css"]}}