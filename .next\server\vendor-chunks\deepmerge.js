"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/deepmerge";
exports.ids = ["vendor-chunks/deepmerge"];
exports.modules = {

/***/ "(ssr)/./node_modules/deepmerge/dist/es.js":
/*!*******************************************!*\
  !*** ./node_modules/deepmerge/dist/es.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nvar isMergeableObject = function isMergeableObject(value) {\n    return isNonNullObject(value) && !isSpecial(value);\n};\nfunction isNonNullObject(value) {\n    return !!value && typeof value === \"object\";\n}\nfunction isSpecial(value) {\n    var stringValue = Object.prototype.toString.call(value);\n    return stringValue === \"[object RegExp]\" || stringValue === \"[object Date]\" || isReactElement(value);\n}\n// see https://github.com/facebook/react/blob/b5ac963fb791d1298e7f396236383bc955f916c1/src/isomorphic/classic/element/ReactElement.js#L21-L25\nvar canUseSymbol = typeof Symbol === \"function\" && Symbol.for;\nvar REACT_ELEMENT_TYPE = canUseSymbol ? Symbol.for(\"react.element\") : 0xeac7;\nfunction isReactElement(value) {\n    return value.$$typeof === REACT_ELEMENT_TYPE;\n}\nfunction emptyTarget(val) {\n    return Array.isArray(val) ? [] : {};\n}\nfunction cloneUnlessOtherwiseSpecified(value, options) {\n    return options.clone !== false && options.isMergeableObject(value) ? deepmerge(emptyTarget(value), value, options) : value;\n}\nfunction defaultArrayMerge(target, source, options) {\n    return target.concat(source).map(function(element) {\n        return cloneUnlessOtherwiseSpecified(element, options);\n    });\n}\nfunction mergeObject(target, source, options) {\n    var destination = {};\n    if (options.isMergeableObject(target)) {\n        Object.keys(target).forEach(function(key) {\n            destination[key] = cloneUnlessOtherwiseSpecified(target[key], options);\n        });\n    }\n    Object.keys(source).forEach(function(key) {\n        if (!options.isMergeableObject(source[key]) || !target[key]) {\n            destination[key] = cloneUnlessOtherwiseSpecified(source[key], options);\n        } else {\n            destination[key] = deepmerge(target[key], source[key], options);\n        }\n    });\n    return destination;\n}\nfunction deepmerge(target, source, options) {\n    options = options || {};\n    options.arrayMerge = options.arrayMerge || defaultArrayMerge;\n    options.isMergeableObject = options.isMergeableObject || isMergeableObject;\n    var sourceIsArray = Array.isArray(source);\n    var targetIsArray = Array.isArray(target);\n    var sourceAndTargetTypesMatch = sourceIsArray === targetIsArray;\n    if (!sourceAndTargetTypesMatch) {\n        return cloneUnlessOtherwiseSpecified(source, options);\n    } else if (sourceIsArray) {\n        return options.arrayMerge(target, source, options);\n    } else {\n        return mergeObject(target, source, options);\n    }\n}\ndeepmerge.all = function deepmergeAll(array, options) {\n    if (!Array.isArray(array)) {\n        throw new Error(\"first argument should be an array\");\n    }\n    return array.reduce(function(prev, next) {\n        return deepmerge(prev, next, options);\n    }, {});\n};\nvar deepmerge_1 = deepmerge;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (deepmerge_1);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/deepmerge/dist/es.js\n");

/***/ })

};
;