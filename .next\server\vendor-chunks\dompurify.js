"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/dompurify";
exports.ids = ["vendor-chunks/dompurify"];
exports.modules = {

/***/ "(ssr)/./node_modules/dompurify/dist/purify.es.js":
/*!**************************************************!*\
  !*** ./node_modules/dompurify/dist/purify.es.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ purify)\n/* harmony export */ });\n/*! @license DOMPurify 2.5.8 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/2.5.8/LICENSE */ function _typeof(obj) {\n    \"@babel/helpers - typeof\";\n    return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function(obj) {\n        return typeof obj;\n    } : function(obj) {\n        return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    }, _typeof(obj);\n}\nfunction _setPrototypeOf(o, p) {\n    _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n        o.__proto__ = p;\n        return o;\n    };\n    return _setPrototypeOf(o, p);\n}\nfunction _isNativeReflectConstruct() {\n    if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n    if (Reflect.construct.sham) return false;\n    if (typeof Proxy === \"function\") return true;\n    try {\n        Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {}));\n        return true;\n    } catch (e) {\n        return false;\n    }\n}\nfunction _construct(Parent, args, Class) {\n    if (_isNativeReflectConstruct()) {\n        _construct = Reflect.construct;\n    } else {\n        _construct = function _construct(Parent, args, Class) {\n            var a = [\n                null\n            ];\n            a.push.apply(a, args);\n            var Constructor = Function.bind.apply(Parent, a);\n            var instance = new Constructor();\n            if (Class) _setPrototypeOf(instance, Class.prototype);\n            return instance;\n        };\n    }\n    return _construct.apply(null, arguments);\n}\nfunction _toConsumableArray(arr) {\n    return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _arrayWithoutHoles(arr) {\n    if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _iterableToArray(iter) {\n    if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n    if (!o) return;\n    if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n    var n = Object.prototype.toString.call(o).slice(8, -1);\n    if (n === \"Object\" && o.constructor) n = o.constructor.name;\n    if (n === \"Map\" || n === \"Set\") return Array.from(o);\n    if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n    if (len == null || len > arr.length) len = arr.length;\n    for(var i = 0, arr2 = new Array(len); i < len; i++)arr2[i] = arr[i];\n    return arr2;\n}\nfunction _nonIterableSpread() {\n    throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nvar hasOwnProperty = Object.hasOwnProperty, setPrototypeOf = Object.setPrototypeOf, isFrozen = Object.isFrozen, getPrototypeOf = Object.getPrototypeOf, getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\nvar freeze = Object.freeze, seal = Object.seal, create = Object.create; // eslint-disable-line import/no-mutable-exports\nvar _ref = typeof Reflect !== \"undefined\" && Reflect, apply = _ref.apply, construct = _ref.construct;\nif (!apply) {\n    apply = function apply(fun, thisValue, args) {\n        return fun.apply(thisValue, args);\n    };\n}\nif (!freeze) {\n    freeze = function freeze(x) {\n        return x;\n    };\n}\nif (!seal) {\n    seal = function seal(x) {\n        return x;\n    };\n}\nif (!construct) {\n    construct = function construct(Func, args) {\n        return _construct(Func, _toConsumableArray(args));\n    };\n}\nvar arrayForEach = unapply(Array.prototype.forEach);\nvar arrayPop = unapply(Array.prototype.pop);\nvar arrayPush = unapply(Array.prototype.push);\nvar stringToLowerCase = unapply(String.prototype.toLowerCase);\nvar stringToString = unapply(String.prototype.toString);\nvar stringMatch = unapply(String.prototype.match);\nvar stringReplace = unapply(String.prototype.replace);\nvar stringIndexOf = unapply(String.prototype.indexOf);\nvar stringTrim = unapply(String.prototype.trim);\nvar regExpTest = unapply(RegExp.prototype.test);\nvar typeErrorCreate = unconstruct(TypeError);\nfunction unapply(func) {\n    return function(thisArg) {\n        for(var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n            args[_key - 1] = arguments[_key];\n        }\n        return apply(func, thisArg, args);\n    };\n}\nfunction unconstruct(func) {\n    return function() {\n        for(var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++){\n            args[_key2] = arguments[_key2];\n        }\n        return construct(func, args);\n    };\n}\n/* Add properties to a lookup table */ function addToSet(set, array, transformCaseFunc) {\n    var _transformCaseFunc;\n    transformCaseFunc = (_transformCaseFunc = transformCaseFunc) !== null && _transformCaseFunc !== void 0 ? _transformCaseFunc : stringToLowerCase;\n    if (setPrototypeOf) {\n        // Make 'in' and truthy checks like Boolean(set.constructor)\n        // independent of any properties defined on Object.prototype.\n        // Prevent prototype setters from intercepting set as a this value.\n        setPrototypeOf(set, null);\n    }\n    var l = array.length;\n    while(l--){\n        var element = array[l];\n        if (typeof element === \"string\") {\n            var lcElement = transformCaseFunc(element);\n            if (lcElement !== element) {\n                // Config presets (e.g. tags.js, attrs.js) are immutable.\n                if (!isFrozen(array)) {\n                    array[l] = lcElement;\n                }\n                element = lcElement;\n            }\n        }\n        set[element] = true;\n    }\n    return set;\n}\n/* Shallow clone an object */ function clone(object) {\n    var newObject = create(null);\n    var property;\n    for(property in object){\n        if (apply(hasOwnProperty, object, [\n            property\n        ]) === true) {\n            newObject[property] = object[property];\n        }\n    }\n    return newObject;\n}\n/* IE10 doesn't support __lookupGetter__ so lets'\n * simulate it. It also automatically checks\n * if the prop is function or getter and behaves\n * accordingly. */ function lookupGetter(object, prop) {\n    while(object !== null){\n        var desc = getOwnPropertyDescriptor(object, prop);\n        if (desc) {\n            if (desc.get) {\n                return unapply(desc.get);\n            }\n            if (typeof desc.value === \"function\") {\n                return unapply(desc.value);\n            }\n        }\n        object = getPrototypeOf(object);\n    }\n    function fallbackValue(element) {\n        console.warn(\"fallback value for\", element);\n        return null;\n    }\n    return fallbackValue;\n}\nvar html$1 = freeze([\n    \"a\",\n    \"abbr\",\n    \"acronym\",\n    \"address\",\n    \"area\",\n    \"article\",\n    \"aside\",\n    \"audio\",\n    \"b\",\n    \"bdi\",\n    \"bdo\",\n    \"big\",\n    \"blink\",\n    \"blockquote\",\n    \"body\",\n    \"br\",\n    \"button\",\n    \"canvas\",\n    \"caption\",\n    \"center\",\n    \"cite\",\n    \"code\",\n    \"col\",\n    \"colgroup\",\n    \"content\",\n    \"data\",\n    \"datalist\",\n    \"dd\",\n    \"decorator\",\n    \"del\",\n    \"details\",\n    \"dfn\",\n    \"dialog\",\n    \"dir\",\n    \"div\",\n    \"dl\",\n    \"dt\",\n    \"element\",\n    \"em\",\n    \"fieldset\",\n    \"figcaption\",\n    \"figure\",\n    \"font\",\n    \"footer\",\n    \"form\",\n    \"h1\",\n    \"h2\",\n    \"h3\",\n    \"h4\",\n    \"h5\",\n    \"h6\",\n    \"head\",\n    \"header\",\n    \"hgroup\",\n    \"hr\",\n    \"html\",\n    \"i\",\n    \"img\",\n    \"input\",\n    \"ins\",\n    \"kbd\",\n    \"label\",\n    \"legend\",\n    \"li\",\n    \"main\",\n    \"map\",\n    \"mark\",\n    \"marquee\",\n    \"menu\",\n    \"menuitem\",\n    \"meter\",\n    \"nav\",\n    \"nobr\",\n    \"ol\",\n    \"optgroup\",\n    \"option\",\n    \"output\",\n    \"p\",\n    \"picture\",\n    \"pre\",\n    \"progress\",\n    \"q\",\n    \"rp\",\n    \"rt\",\n    \"ruby\",\n    \"s\",\n    \"samp\",\n    \"section\",\n    \"select\",\n    \"shadow\",\n    \"small\",\n    \"source\",\n    \"spacer\",\n    \"span\",\n    \"strike\",\n    \"strong\",\n    \"style\",\n    \"sub\",\n    \"summary\",\n    \"sup\",\n    \"table\",\n    \"tbody\",\n    \"td\",\n    \"template\",\n    \"textarea\",\n    \"tfoot\",\n    \"th\",\n    \"thead\",\n    \"time\",\n    \"tr\",\n    \"track\",\n    \"tt\",\n    \"u\",\n    \"ul\",\n    \"var\",\n    \"video\",\n    \"wbr\"\n]);\n// SVG\nvar svg$1 = freeze([\n    \"svg\",\n    \"a\",\n    \"altglyph\",\n    \"altglyphdef\",\n    \"altglyphitem\",\n    \"animatecolor\",\n    \"animatemotion\",\n    \"animatetransform\",\n    \"circle\",\n    \"clippath\",\n    \"defs\",\n    \"desc\",\n    \"ellipse\",\n    \"filter\",\n    \"font\",\n    \"g\",\n    \"glyph\",\n    \"glyphref\",\n    \"hkern\",\n    \"image\",\n    \"line\",\n    \"lineargradient\",\n    \"marker\",\n    \"mask\",\n    \"metadata\",\n    \"mpath\",\n    \"path\",\n    \"pattern\",\n    \"polygon\",\n    \"polyline\",\n    \"radialgradient\",\n    \"rect\",\n    \"stop\",\n    \"style\",\n    \"switch\",\n    \"symbol\",\n    \"text\",\n    \"textpath\",\n    \"title\",\n    \"tref\",\n    \"tspan\",\n    \"view\",\n    \"vkern\"\n]);\nvar svgFilters = freeze([\n    \"feBlend\",\n    \"feColorMatrix\",\n    \"feComponentTransfer\",\n    \"feComposite\",\n    \"feConvolveMatrix\",\n    \"feDiffuseLighting\",\n    \"feDisplacementMap\",\n    \"feDistantLight\",\n    \"feFlood\",\n    \"feFuncA\",\n    \"feFuncB\",\n    \"feFuncG\",\n    \"feFuncR\",\n    \"feGaussianBlur\",\n    \"feImage\",\n    \"feMerge\",\n    \"feMergeNode\",\n    \"feMorphology\",\n    \"feOffset\",\n    \"fePointLight\",\n    \"feSpecularLighting\",\n    \"feSpotLight\",\n    \"feTile\",\n    \"feTurbulence\"\n]);\n// List of SVG elements that are disallowed by default.\n// We still need to know them so that we can do namespace\n// checks properly in case one wants to add them to\n// allow-list.\nvar svgDisallowed = freeze([\n    \"animate\",\n    \"color-profile\",\n    \"cursor\",\n    \"discard\",\n    \"fedropshadow\",\n    \"font-face\",\n    \"font-face-format\",\n    \"font-face-name\",\n    \"font-face-src\",\n    \"font-face-uri\",\n    \"foreignobject\",\n    \"hatch\",\n    \"hatchpath\",\n    \"mesh\",\n    \"meshgradient\",\n    \"meshpatch\",\n    \"meshrow\",\n    \"missing-glyph\",\n    \"script\",\n    \"set\",\n    \"solidcolor\",\n    \"unknown\",\n    \"use\"\n]);\nvar mathMl$1 = freeze([\n    \"math\",\n    \"menclose\",\n    \"merror\",\n    \"mfenced\",\n    \"mfrac\",\n    \"mglyph\",\n    \"mi\",\n    \"mlabeledtr\",\n    \"mmultiscripts\",\n    \"mn\",\n    \"mo\",\n    \"mover\",\n    \"mpadded\",\n    \"mphantom\",\n    \"mroot\",\n    \"mrow\",\n    \"ms\",\n    \"mspace\",\n    \"msqrt\",\n    \"mstyle\",\n    \"msub\",\n    \"msup\",\n    \"msubsup\",\n    \"mtable\",\n    \"mtd\",\n    \"mtext\",\n    \"mtr\",\n    \"munder\",\n    \"munderover\"\n]);\n// Similarly to SVG, we want to know all MathML elements,\n// even those that we disallow by default.\nvar mathMlDisallowed = freeze([\n    \"maction\",\n    \"maligngroup\",\n    \"malignmark\",\n    \"mlongdiv\",\n    \"mscarries\",\n    \"mscarry\",\n    \"msgroup\",\n    \"mstack\",\n    \"msline\",\n    \"msrow\",\n    \"semantics\",\n    \"annotation\",\n    \"annotation-xml\",\n    \"mprescripts\",\n    \"none\"\n]);\nvar text = freeze([\n    \"#text\"\n]);\nvar html = freeze([\n    \"accept\",\n    \"action\",\n    \"align\",\n    \"alt\",\n    \"autocapitalize\",\n    \"autocomplete\",\n    \"autopictureinpicture\",\n    \"autoplay\",\n    \"background\",\n    \"bgcolor\",\n    \"border\",\n    \"capture\",\n    \"cellpadding\",\n    \"cellspacing\",\n    \"checked\",\n    \"cite\",\n    \"class\",\n    \"clear\",\n    \"color\",\n    \"cols\",\n    \"colspan\",\n    \"controls\",\n    \"controlslist\",\n    \"coords\",\n    \"crossorigin\",\n    \"datetime\",\n    \"decoding\",\n    \"default\",\n    \"dir\",\n    \"disabled\",\n    \"disablepictureinpicture\",\n    \"disableremoteplayback\",\n    \"download\",\n    \"draggable\",\n    \"enctype\",\n    \"enterkeyhint\",\n    \"face\",\n    \"for\",\n    \"headers\",\n    \"height\",\n    \"hidden\",\n    \"high\",\n    \"href\",\n    \"hreflang\",\n    \"id\",\n    \"inputmode\",\n    \"integrity\",\n    \"ismap\",\n    \"kind\",\n    \"label\",\n    \"lang\",\n    \"list\",\n    \"loading\",\n    \"loop\",\n    \"low\",\n    \"max\",\n    \"maxlength\",\n    \"media\",\n    \"method\",\n    \"min\",\n    \"minlength\",\n    \"multiple\",\n    \"muted\",\n    \"name\",\n    \"nonce\",\n    \"noshade\",\n    \"novalidate\",\n    \"nowrap\",\n    \"open\",\n    \"optimum\",\n    \"pattern\",\n    \"placeholder\",\n    \"playsinline\",\n    \"poster\",\n    \"preload\",\n    \"pubdate\",\n    \"radiogroup\",\n    \"readonly\",\n    \"rel\",\n    \"required\",\n    \"rev\",\n    \"reversed\",\n    \"role\",\n    \"rows\",\n    \"rowspan\",\n    \"spellcheck\",\n    \"scope\",\n    \"selected\",\n    \"shape\",\n    \"size\",\n    \"sizes\",\n    \"span\",\n    \"srclang\",\n    \"start\",\n    \"src\",\n    \"srcset\",\n    \"step\",\n    \"style\",\n    \"summary\",\n    \"tabindex\",\n    \"title\",\n    \"translate\",\n    \"type\",\n    \"usemap\",\n    \"valign\",\n    \"value\",\n    \"width\",\n    \"xmlns\",\n    \"slot\"\n]);\nvar svg = freeze([\n    \"accent-height\",\n    \"accumulate\",\n    \"additive\",\n    \"alignment-baseline\",\n    \"ascent\",\n    \"attributename\",\n    \"attributetype\",\n    \"azimuth\",\n    \"basefrequency\",\n    \"baseline-shift\",\n    \"begin\",\n    \"bias\",\n    \"by\",\n    \"class\",\n    \"clip\",\n    \"clippathunits\",\n    \"clip-path\",\n    \"clip-rule\",\n    \"color\",\n    \"color-interpolation\",\n    \"color-interpolation-filters\",\n    \"color-profile\",\n    \"color-rendering\",\n    \"cx\",\n    \"cy\",\n    \"d\",\n    \"dx\",\n    \"dy\",\n    \"diffuseconstant\",\n    \"direction\",\n    \"display\",\n    \"divisor\",\n    \"dur\",\n    \"edgemode\",\n    \"elevation\",\n    \"end\",\n    \"fill\",\n    \"fill-opacity\",\n    \"fill-rule\",\n    \"filter\",\n    \"filterunits\",\n    \"flood-color\",\n    \"flood-opacity\",\n    \"font-family\",\n    \"font-size\",\n    \"font-size-adjust\",\n    \"font-stretch\",\n    \"font-style\",\n    \"font-variant\",\n    \"font-weight\",\n    \"fx\",\n    \"fy\",\n    \"g1\",\n    \"g2\",\n    \"glyph-name\",\n    \"glyphref\",\n    \"gradientunits\",\n    \"gradienttransform\",\n    \"height\",\n    \"href\",\n    \"id\",\n    \"image-rendering\",\n    \"in\",\n    \"in2\",\n    \"k\",\n    \"k1\",\n    \"k2\",\n    \"k3\",\n    \"k4\",\n    \"kerning\",\n    \"keypoints\",\n    \"keysplines\",\n    \"keytimes\",\n    \"lang\",\n    \"lengthadjust\",\n    \"letter-spacing\",\n    \"kernelmatrix\",\n    \"kernelunitlength\",\n    \"lighting-color\",\n    \"local\",\n    \"marker-end\",\n    \"marker-mid\",\n    \"marker-start\",\n    \"markerheight\",\n    \"markerunits\",\n    \"markerwidth\",\n    \"maskcontentunits\",\n    \"maskunits\",\n    \"max\",\n    \"mask\",\n    \"media\",\n    \"method\",\n    \"mode\",\n    \"min\",\n    \"name\",\n    \"numoctaves\",\n    \"offset\",\n    \"operator\",\n    \"opacity\",\n    \"order\",\n    \"orient\",\n    \"orientation\",\n    \"origin\",\n    \"overflow\",\n    \"paint-order\",\n    \"path\",\n    \"pathlength\",\n    \"patterncontentunits\",\n    \"patterntransform\",\n    \"patternunits\",\n    \"points\",\n    \"preservealpha\",\n    \"preserveaspectratio\",\n    \"primitiveunits\",\n    \"r\",\n    \"rx\",\n    \"ry\",\n    \"radius\",\n    \"refx\",\n    \"refy\",\n    \"repeatcount\",\n    \"repeatdur\",\n    \"restart\",\n    \"result\",\n    \"rotate\",\n    \"scale\",\n    \"seed\",\n    \"shape-rendering\",\n    \"specularconstant\",\n    \"specularexponent\",\n    \"spreadmethod\",\n    \"startoffset\",\n    \"stddeviation\",\n    \"stitchtiles\",\n    \"stop-color\",\n    \"stop-opacity\",\n    \"stroke-dasharray\",\n    \"stroke-dashoffset\",\n    \"stroke-linecap\",\n    \"stroke-linejoin\",\n    \"stroke-miterlimit\",\n    \"stroke-opacity\",\n    \"stroke\",\n    \"stroke-width\",\n    \"style\",\n    \"surfacescale\",\n    \"systemlanguage\",\n    \"tabindex\",\n    \"targetx\",\n    \"targety\",\n    \"transform\",\n    \"transform-origin\",\n    \"text-anchor\",\n    \"text-decoration\",\n    \"text-rendering\",\n    \"textlength\",\n    \"type\",\n    \"u1\",\n    \"u2\",\n    \"unicode\",\n    \"values\",\n    \"viewbox\",\n    \"visibility\",\n    \"version\",\n    \"vert-adv-y\",\n    \"vert-origin-x\",\n    \"vert-origin-y\",\n    \"width\",\n    \"word-spacing\",\n    \"wrap\",\n    \"writing-mode\",\n    \"xchannelselector\",\n    \"ychannelselector\",\n    \"x\",\n    \"x1\",\n    \"x2\",\n    \"xmlns\",\n    \"y\",\n    \"y1\",\n    \"y2\",\n    \"z\",\n    \"zoomandpan\"\n]);\nvar mathMl = freeze([\n    \"accent\",\n    \"accentunder\",\n    \"align\",\n    \"bevelled\",\n    \"close\",\n    \"columnsalign\",\n    \"columnlines\",\n    \"columnspan\",\n    \"denomalign\",\n    \"depth\",\n    \"dir\",\n    \"display\",\n    \"displaystyle\",\n    \"encoding\",\n    \"fence\",\n    \"frame\",\n    \"height\",\n    \"href\",\n    \"id\",\n    \"largeop\",\n    \"length\",\n    \"linethickness\",\n    \"lspace\",\n    \"lquote\",\n    \"mathbackground\",\n    \"mathcolor\",\n    \"mathsize\",\n    \"mathvariant\",\n    \"maxsize\",\n    \"minsize\",\n    \"movablelimits\",\n    \"notation\",\n    \"numalign\",\n    \"open\",\n    \"rowalign\",\n    \"rowlines\",\n    \"rowspacing\",\n    \"rowspan\",\n    \"rspace\",\n    \"rquote\",\n    \"scriptlevel\",\n    \"scriptminsize\",\n    \"scriptsizemultiplier\",\n    \"selection\",\n    \"separator\",\n    \"separators\",\n    \"stretchy\",\n    \"subscriptshift\",\n    \"supscriptshift\",\n    \"symmetric\",\n    \"voffset\",\n    \"width\",\n    \"xmlns\"\n]);\nvar xml = freeze([\n    \"xlink:href\",\n    \"xml:id\",\n    \"xlink:title\",\n    \"xml:space\",\n    \"xmlns:xlink\"\n]);\n// eslint-disable-next-line unicorn/better-regex\nvar MUSTACHE_EXPR = seal(/\\{\\{[\\w\\W]*|[\\w\\W]*\\}\\}/gm); // Specify template detection regex for SAFE_FOR_TEMPLATES mode\nvar ERB_EXPR = seal(/<%[\\w\\W]*|[\\w\\W]*%>/gm);\nvar TMPLIT_EXPR = seal(/\\${[\\w\\W]*}/gm);\nvar DATA_ATTR = seal(/^data-[\\-\\w.\\u00B7-\\uFFFF]+$/); // eslint-disable-line no-useless-escape\nvar ARIA_ATTR = seal(/^aria-[\\-\\w]+$/); // eslint-disable-line no-useless-escape\nvar IS_ALLOWED_URI = seal(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\\-]+(?:[^a-z+.\\-:]|$))/i // eslint-disable-line no-useless-escape\n);\nvar IS_SCRIPT_OR_DATA = seal(/^(?:\\w+script|data):/i);\nvar ATTR_WHITESPACE = seal(/[\\u0000-\\u0020\\u00A0\\u1680\\u180E\\u2000-\\u2029\\u205F\\u3000]/g // eslint-disable-line no-control-regex\n);\nvar DOCTYPE_NAME = seal(/^html$/i);\nvar CUSTOM_ELEMENT = seal(/^[a-z][.\\w]*(-[.\\w]+)+$/i);\nvar getGlobal = function getGlobal() {\n    return  true ? null : 0;\n};\n/**\n * Creates a no-op policy for internal use only.\n * Don't export this function outside this module!\n * @param {?TrustedTypePolicyFactory} trustedTypes The policy factory.\n * @param {Document} document The document object (to determine policy name suffix)\n * @return {?TrustedTypePolicy} The policy created (or null, if Trusted Types\n * are not supported).\n */ var _createTrustedTypesPolicy = function _createTrustedTypesPolicy(trustedTypes, document) {\n    if (_typeof(trustedTypes) !== \"object\" || typeof trustedTypes.createPolicy !== \"function\") {\n        return null;\n    }\n    // Allow the callers to control the unique policy name\n    // by adding a data-tt-policy-suffix to the script element with the DOMPurify.\n    // Policy creation with duplicate names throws in Trusted Types.\n    var suffix = null;\n    var ATTR_NAME = \"data-tt-policy-suffix\";\n    if (document.currentScript && document.currentScript.hasAttribute(ATTR_NAME)) {\n        suffix = document.currentScript.getAttribute(ATTR_NAME);\n    }\n    var policyName = \"dompurify\" + (suffix ? \"#\" + suffix : \"\");\n    try {\n        return trustedTypes.createPolicy(policyName, {\n            createHTML: function createHTML(html) {\n                return html;\n            },\n            createScriptURL: function createScriptURL(scriptUrl) {\n                return scriptUrl;\n            }\n        });\n    } catch (_) {\n        // Policy creation failed (most likely another DOMPurify script has\n        // already run). Skip creating the policy, as this will only cause errors\n        // if TT are enforced.\n        console.warn(\"TrustedTypes policy \" + policyName + \" could not be created.\");\n        return null;\n    }\n};\nfunction createDOMPurify() {\n    var window1 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : getGlobal();\n    var DOMPurify = function DOMPurify(root) {\n        return createDOMPurify(root);\n    };\n    /**\n   * Version label, exposed for easier checks\n   * if DOMPurify is up to date or not\n   */ DOMPurify.version = \"2.5.8\";\n    /**\n   * Array of elements that DOMPurify removed during sanitation.\n   * Empty if nothing was removed.\n   */ DOMPurify.removed = [];\n    if (!window1 || !window1.document || window1.document.nodeType !== 9) {\n        // Not running in a browser, provide a factory function\n        // so that you can pass your own Window\n        DOMPurify.isSupported = false;\n        return DOMPurify;\n    }\n    var originalDocument = window1.document;\n    var document = window1.document;\n    var DocumentFragment = window1.DocumentFragment, HTMLTemplateElement = window1.HTMLTemplateElement, Node = window1.Node, Element = window1.Element, NodeFilter = window1.NodeFilter, _window$NamedNodeMap = window1.NamedNodeMap, NamedNodeMap = _window$NamedNodeMap === void 0 ? window1.NamedNodeMap || window1.MozNamedAttrMap : _window$NamedNodeMap, HTMLFormElement = window1.HTMLFormElement, DOMParser = window1.DOMParser, trustedTypes = window1.trustedTypes;\n    var ElementPrototype = Element.prototype;\n    var cloneNode = lookupGetter(ElementPrototype, \"cloneNode\");\n    var getNextSibling = lookupGetter(ElementPrototype, \"nextSibling\");\n    var getChildNodes = lookupGetter(ElementPrototype, \"childNodes\");\n    var getParentNode = lookupGetter(ElementPrototype, \"parentNode\");\n    // As per issue #47, the web-components registry is inherited by a\n    // new document created via createHTMLDocument. As per the spec\n    // (http://w3c.github.io/webcomponents/spec/custom/#creating-and-passing-registries)\n    // a new empty registry is used when creating a template contents owner\n    // document, so we use that as our parent document to ensure nothing\n    // is inherited.\n    if (typeof HTMLTemplateElement === \"function\") {\n        var template = document.createElement(\"template\");\n        if (template.content && template.content.ownerDocument) {\n            document = template.content.ownerDocument;\n        }\n    }\n    var trustedTypesPolicy = _createTrustedTypesPolicy(trustedTypes, originalDocument);\n    var emptyHTML = trustedTypesPolicy ? trustedTypesPolicy.createHTML(\"\") : \"\";\n    var _document = document, implementation = _document.implementation, createNodeIterator = _document.createNodeIterator, createDocumentFragment = _document.createDocumentFragment, getElementsByTagName = _document.getElementsByTagName;\n    var importNode = originalDocument.importNode;\n    var documentMode = {};\n    try {\n        documentMode = clone(document).documentMode ? document.documentMode : {};\n    } catch (_) {}\n    var hooks = {};\n    /**\n   * Expose whether this browser supports running the full DOMPurify.\n   */ DOMPurify.isSupported = typeof getParentNode === \"function\" && implementation && implementation.createHTMLDocument !== undefined && documentMode !== 9;\n    var MUSTACHE_EXPR$1 = MUSTACHE_EXPR, ERB_EXPR$1 = ERB_EXPR, TMPLIT_EXPR$1 = TMPLIT_EXPR, DATA_ATTR$1 = DATA_ATTR, ARIA_ATTR$1 = ARIA_ATTR, IS_SCRIPT_OR_DATA$1 = IS_SCRIPT_OR_DATA, ATTR_WHITESPACE$1 = ATTR_WHITESPACE, CUSTOM_ELEMENT$1 = CUSTOM_ELEMENT;\n    var IS_ALLOWED_URI$1 = IS_ALLOWED_URI;\n    /**\n   * We consider the elements and attributes below to be safe. Ideally\n   * don't add any new ones but feel free to remove unwanted ones.\n   */ /* allowed element names */ var ALLOWED_TAGS = null;\n    var DEFAULT_ALLOWED_TAGS = addToSet({}, [].concat(_toConsumableArray(html$1), _toConsumableArray(svg$1), _toConsumableArray(svgFilters), _toConsumableArray(mathMl$1), _toConsumableArray(text)));\n    /* Allowed attribute names */ var ALLOWED_ATTR = null;\n    var DEFAULT_ALLOWED_ATTR = addToSet({}, [].concat(_toConsumableArray(html), _toConsumableArray(svg), _toConsumableArray(mathMl), _toConsumableArray(xml)));\n    /*\n   * Configure how DOMPUrify should handle custom elements and their attributes as well as customized built-in elements.\n   * @property {RegExp|Function|null} tagNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any custom elements)\n   * @property {RegExp|Function|null} attributeNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any attributes not on the allow list)\n   * @property {boolean} allowCustomizedBuiltInElements allow custom elements derived from built-ins if they pass CUSTOM_ELEMENT_HANDLING.tagNameCheck. Default: `false`.\n   */ var CUSTOM_ELEMENT_HANDLING = Object.seal(Object.create(null, {\n        tagNameCheck: {\n            writable: true,\n            configurable: false,\n            enumerable: true,\n            value: null\n        },\n        attributeNameCheck: {\n            writable: true,\n            configurable: false,\n            enumerable: true,\n            value: null\n        },\n        allowCustomizedBuiltInElements: {\n            writable: true,\n            configurable: false,\n            enumerable: true,\n            value: false\n        }\n    }));\n    /* Explicitly forbidden tags (overrides ALLOWED_TAGS/ADD_TAGS) */ var FORBID_TAGS = null;\n    /* Explicitly forbidden attributes (overrides ALLOWED_ATTR/ADD_ATTR) */ var FORBID_ATTR = null;\n    /* Decide if ARIA attributes are okay */ var ALLOW_ARIA_ATTR = true;\n    /* Decide if custom data attributes are okay */ var ALLOW_DATA_ATTR = true;\n    /* Decide if unknown protocols are okay */ var ALLOW_UNKNOWN_PROTOCOLS = false;\n    /* Decide if self-closing tags in attributes are allowed.\n   * Usually removed due to a mXSS issue in jQuery 3.0 */ var ALLOW_SELF_CLOSE_IN_ATTR = true;\n    /* Output should be safe for common template engines.\n   * This means, DOMPurify removes data attributes, mustaches and ERB\n   */ var SAFE_FOR_TEMPLATES = false;\n    /* Output should be safe even for XML used within HTML and alike.\n   * This means, DOMPurify removes comments when containing risky content.\n   */ var SAFE_FOR_XML = true;\n    /* Decide if document with <html>... should be returned */ var WHOLE_DOCUMENT = false;\n    /* Track whether config is already set on this instance of DOMPurify. */ var SET_CONFIG = false;\n    /* Decide if all elements (e.g. style, script) must be children of\n   * document.body. By default, browsers might move them to document.head */ var FORCE_BODY = false;\n    /* Decide if a DOM `HTMLBodyElement` should be returned, instead of a html\n   * string (or a TrustedHTML object if Trusted Types are supported).\n   * If `WHOLE_DOCUMENT` is enabled a `HTMLHtmlElement` will be returned instead\n   */ var RETURN_DOM = false;\n    /* Decide if a DOM `DocumentFragment` should be returned, instead of a html\n   * string  (or a TrustedHTML object if Trusted Types are supported) */ var RETURN_DOM_FRAGMENT = false;\n    /* Try to return a Trusted Type object instead of a string, return a string in\n   * case Trusted Types are not supported  */ var RETURN_TRUSTED_TYPE = false;\n    /* Output should be free from DOM clobbering attacks?\n   * This sanitizes markups named with colliding, clobberable built-in DOM APIs.\n   */ var SANITIZE_DOM = true;\n    /* Achieve full DOM Clobbering protection by isolating the namespace of named\n   * properties and JS variables, mitigating attacks that abuse the HTML/DOM spec rules.\n   *\n   * HTML/DOM spec rules that enable DOM Clobbering:\n   *   - Named Access on Window (§7.3.3)\n   *   - DOM Tree Accessors (§3.1.5)\n   *   - Form Element Parent-Child Relations (§4.10.3)\n   *   - Iframe srcdoc / Nested WindowProxies (§4.8.5)\n   *   - HTMLCollection (§4.2.10.2)\n   *\n   * Namespace isolation is implemented by prefixing `id` and `name` attributes\n   * with a constant string, i.e., `user-content-`\n   */ var SANITIZE_NAMED_PROPS = false;\n    var SANITIZE_NAMED_PROPS_PREFIX = \"user-content-\";\n    /* Keep element content when removing element? */ var KEEP_CONTENT = true;\n    /* If a `Node` is passed to sanitize(), then performs sanitization in-place instead\n   * of importing it into a new Document and returning a sanitized copy */ var IN_PLACE = false;\n    /* Allow usage of profiles like html, svg and mathMl */ var USE_PROFILES = {};\n    /* Tags to ignore content of when KEEP_CONTENT is true */ var FORBID_CONTENTS = null;\n    var DEFAULT_FORBID_CONTENTS = addToSet({}, [\n        \"annotation-xml\",\n        \"audio\",\n        \"colgroup\",\n        \"desc\",\n        \"foreignobject\",\n        \"head\",\n        \"iframe\",\n        \"math\",\n        \"mi\",\n        \"mn\",\n        \"mo\",\n        \"ms\",\n        \"mtext\",\n        \"noembed\",\n        \"noframes\",\n        \"noscript\",\n        \"plaintext\",\n        \"script\",\n        \"style\",\n        \"svg\",\n        \"template\",\n        \"thead\",\n        \"title\",\n        \"video\",\n        \"xmp\"\n    ]);\n    /* Tags that are safe for data: URIs */ var DATA_URI_TAGS = null;\n    var DEFAULT_DATA_URI_TAGS = addToSet({}, [\n        \"audio\",\n        \"video\",\n        \"img\",\n        \"source\",\n        \"image\",\n        \"track\"\n    ]);\n    /* Attributes safe for values like \"javascript:\" */ var URI_SAFE_ATTRIBUTES = null;\n    var DEFAULT_URI_SAFE_ATTRIBUTES = addToSet({}, [\n        \"alt\",\n        \"class\",\n        \"for\",\n        \"id\",\n        \"label\",\n        \"name\",\n        \"pattern\",\n        \"placeholder\",\n        \"role\",\n        \"summary\",\n        \"title\",\n        \"value\",\n        \"style\",\n        \"xmlns\"\n    ]);\n    var MATHML_NAMESPACE = \"http://www.w3.org/1998/Math/MathML\";\n    var SVG_NAMESPACE = \"http://www.w3.org/2000/svg\";\n    var HTML_NAMESPACE = \"http://www.w3.org/1999/xhtml\";\n    /* Document namespace */ var NAMESPACE = HTML_NAMESPACE;\n    var IS_EMPTY_INPUT = false;\n    /* Allowed XHTML+XML namespaces */ var ALLOWED_NAMESPACES = null;\n    var DEFAULT_ALLOWED_NAMESPACES = addToSet({}, [\n        MATHML_NAMESPACE,\n        SVG_NAMESPACE,\n        HTML_NAMESPACE\n    ], stringToString);\n    /* Parsing of strict XHTML documents */ var PARSER_MEDIA_TYPE;\n    var SUPPORTED_PARSER_MEDIA_TYPES = [\n        \"application/xhtml+xml\",\n        \"text/html\"\n    ];\n    var DEFAULT_PARSER_MEDIA_TYPE = \"text/html\";\n    var transformCaseFunc;\n    /* Keep a reference to config to pass to hooks */ var CONFIG = null;\n    /* Ideally, do not touch anything below this line */ /* ______________________________________________ */ var formElement = document.createElement(\"form\");\n    var isRegexOrFunction = function isRegexOrFunction(testValue) {\n        return testValue instanceof RegExp || testValue instanceof Function;\n    };\n    /**\n   * _parseConfig\n   *\n   * @param  {Object} cfg optional config literal\n   */ // eslint-disable-next-line complexity\n    var _parseConfig = function _parseConfig(cfg) {\n        if (CONFIG && CONFIG === cfg) {\n            return;\n        }\n        /* Shield configuration object from tampering */ if (!cfg || _typeof(cfg) !== \"object\") {\n            cfg = {};\n        }\n        /* Shield configuration object from prototype pollution */ cfg = clone(cfg);\n        PARSER_MEDIA_TYPE = // eslint-disable-next-line unicorn/prefer-includes\n        SUPPORTED_PARSER_MEDIA_TYPES.indexOf(cfg.PARSER_MEDIA_TYPE) === -1 ? PARSER_MEDIA_TYPE = DEFAULT_PARSER_MEDIA_TYPE : PARSER_MEDIA_TYPE = cfg.PARSER_MEDIA_TYPE;\n        // HTML tags and attributes are not case-sensitive, converting to lowercase. Keeping XHTML as is.\n        transformCaseFunc = PARSER_MEDIA_TYPE === \"application/xhtml+xml\" ? stringToString : stringToLowerCase;\n        /* Set configuration parameters */ ALLOWED_TAGS = \"ALLOWED_TAGS\" in cfg ? addToSet({}, cfg.ALLOWED_TAGS, transformCaseFunc) : DEFAULT_ALLOWED_TAGS;\n        ALLOWED_ATTR = \"ALLOWED_ATTR\" in cfg ? addToSet({}, cfg.ALLOWED_ATTR, transformCaseFunc) : DEFAULT_ALLOWED_ATTR;\n        ALLOWED_NAMESPACES = \"ALLOWED_NAMESPACES\" in cfg ? addToSet({}, cfg.ALLOWED_NAMESPACES, stringToString) : DEFAULT_ALLOWED_NAMESPACES;\n        URI_SAFE_ATTRIBUTES = \"ADD_URI_SAFE_ATTR\" in cfg ? addToSet(clone(DEFAULT_URI_SAFE_ATTRIBUTES), // eslint-disable-line indent\n        cfg.ADD_URI_SAFE_ATTR, // eslint-disable-line indent\n        transformCaseFunc // eslint-disable-line indent\n        ) // eslint-disable-line indent\n         : DEFAULT_URI_SAFE_ATTRIBUTES;\n        DATA_URI_TAGS = \"ADD_DATA_URI_TAGS\" in cfg ? addToSet(clone(DEFAULT_DATA_URI_TAGS), // eslint-disable-line indent\n        cfg.ADD_DATA_URI_TAGS, // eslint-disable-line indent\n        transformCaseFunc // eslint-disable-line indent\n        ) // eslint-disable-line indent\n         : DEFAULT_DATA_URI_TAGS;\n        FORBID_CONTENTS = \"FORBID_CONTENTS\" in cfg ? addToSet({}, cfg.FORBID_CONTENTS, transformCaseFunc) : DEFAULT_FORBID_CONTENTS;\n        FORBID_TAGS = \"FORBID_TAGS\" in cfg ? addToSet({}, cfg.FORBID_TAGS, transformCaseFunc) : {};\n        FORBID_ATTR = \"FORBID_ATTR\" in cfg ? addToSet({}, cfg.FORBID_ATTR, transformCaseFunc) : {};\n        USE_PROFILES = \"USE_PROFILES\" in cfg ? cfg.USE_PROFILES : false;\n        ALLOW_ARIA_ATTR = cfg.ALLOW_ARIA_ATTR !== false; // Default true\n        ALLOW_DATA_ATTR = cfg.ALLOW_DATA_ATTR !== false; // Default true\n        ALLOW_UNKNOWN_PROTOCOLS = cfg.ALLOW_UNKNOWN_PROTOCOLS || false; // Default false\n        ALLOW_SELF_CLOSE_IN_ATTR = cfg.ALLOW_SELF_CLOSE_IN_ATTR !== false; // Default true\n        SAFE_FOR_TEMPLATES = cfg.SAFE_FOR_TEMPLATES || false; // Default false\n        SAFE_FOR_XML = cfg.SAFE_FOR_XML !== false; // Default true\n        WHOLE_DOCUMENT = cfg.WHOLE_DOCUMENT || false; // Default false\n        RETURN_DOM = cfg.RETURN_DOM || false; // Default false\n        RETURN_DOM_FRAGMENT = cfg.RETURN_DOM_FRAGMENT || false; // Default false\n        RETURN_TRUSTED_TYPE = cfg.RETURN_TRUSTED_TYPE || false; // Default false\n        FORCE_BODY = cfg.FORCE_BODY || false; // Default false\n        SANITIZE_DOM = cfg.SANITIZE_DOM !== false; // Default true\n        SANITIZE_NAMED_PROPS = cfg.SANITIZE_NAMED_PROPS || false; // Default false\n        KEEP_CONTENT = cfg.KEEP_CONTENT !== false; // Default true\n        IN_PLACE = cfg.IN_PLACE || false; // Default false\n        IS_ALLOWED_URI$1 = cfg.ALLOWED_URI_REGEXP || IS_ALLOWED_URI$1;\n        NAMESPACE = cfg.NAMESPACE || HTML_NAMESPACE;\n        CUSTOM_ELEMENT_HANDLING = cfg.CUSTOM_ELEMENT_HANDLING || {};\n        if (cfg.CUSTOM_ELEMENT_HANDLING && isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck)) {\n            CUSTOM_ELEMENT_HANDLING.tagNameCheck = cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck;\n        }\n        if (cfg.CUSTOM_ELEMENT_HANDLING && isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)) {\n            CUSTOM_ELEMENT_HANDLING.attributeNameCheck = cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck;\n        }\n        if (cfg.CUSTOM_ELEMENT_HANDLING && typeof cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements === \"boolean\") {\n            CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements = cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements;\n        }\n        if (SAFE_FOR_TEMPLATES) {\n            ALLOW_DATA_ATTR = false;\n        }\n        if (RETURN_DOM_FRAGMENT) {\n            RETURN_DOM = true;\n        }\n        /* Parse profile info */ if (USE_PROFILES) {\n            ALLOWED_TAGS = addToSet({}, _toConsumableArray(text));\n            ALLOWED_ATTR = [];\n            if (USE_PROFILES.html === true) {\n                addToSet(ALLOWED_TAGS, html$1);\n                addToSet(ALLOWED_ATTR, html);\n            }\n            if (USE_PROFILES.svg === true) {\n                addToSet(ALLOWED_TAGS, svg$1);\n                addToSet(ALLOWED_ATTR, svg);\n                addToSet(ALLOWED_ATTR, xml);\n            }\n            if (USE_PROFILES.svgFilters === true) {\n                addToSet(ALLOWED_TAGS, svgFilters);\n                addToSet(ALLOWED_ATTR, svg);\n                addToSet(ALLOWED_ATTR, xml);\n            }\n            if (USE_PROFILES.mathMl === true) {\n                addToSet(ALLOWED_TAGS, mathMl$1);\n                addToSet(ALLOWED_ATTR, mathMl);\n                addToSet(ALLOWED_ATTR, xml);\n            }\n        }\n        /* Merge configuration parameters */ if (cfg.ADD_TAGS) {\n            if (ALLOWED_TAGS === DEFAULT_ALLOWED_TAGS) {\n                ALLOWED_TAGS = clone(ALLOWED_TAGS);\n            }\n            addToSet(ALLOWED_TAGS, cfg.ADD_TAGS, transformCaseFunc);\n        }\n        if (cfg.ADD_ATTR) {\n            if (ALLOWED_ATTR === DEFAULT_ALLOWED_ATTR) {\n                ALLOWED_ATTR = clone(ALLOWED_ATTR);\n            }\n            addToSet(ALLOWED_ATTR, cfg.ADD_ATTR, transformCaseFunc);\n        }\n        if (cfg.ADD_URI_SAFE_ATTR) {\n            addToSet(URI_SAFE_ATTRIBUTES, cfg.ADD_URI_SAFE_ATTR, transformCaseFunc);\n        }\n        if (cfg.FORBID_CONTENTS) {\n            if (FORBID_CONTENTS === DEFAULT_FORBID_CONTENTS) {\n                FORBID_CONTENTS = clone(FORBID_CONTENTS);\n            }\n            addToSet(FORBID_CONTENTS, cfg.FORBID_CONTENTS, transformCaseFunc);\n        }\n        /* Add #text in case KEEP_CONTENT is set to true */ if (KEEP_CONTENT) {\n            ALLOWED_TAGS[\"#text\"] = true;\n        }\n        /* Add html, head and body to ALLOWED_TAGS in case WHOLE_DOCUMENT is true */ if (WHOLE_DOCUMENT) {\n            addToSet(ALLOWED_TAGS, [\n                \"html\",\n                \"head\",\n                \"body\"\n            ]);\n        }\n        /* Add tbody to ALLOWED_TAGS in case tables are permitted, see #286, #365 */ if (ALLOWED_TAGS.table) {\n            addToSet(ALLOWED_TAGS, [\n                \"tbody\"\n            ]);\n            delete FORBID_TAGS.tbody;\n        }\n        // Prevent further manipulation of configuration.\n        // Not available in IE8, Safari 5, etc.\n        if (freeze) {\n            freeze(cfg);\n        }\n        CONFIG = cfg;\n    };\n    var MATHML_TEXT_INTEGRATION_POINTS = addToSet({}, [\n        \"mi\",\n        \"mo\",\n        \"mn\",\n        \"ms\",\n        \"mtext\"\n    ]);\n    var HTML_INTEGRATION_POINTS = addToSet({}, [\n        \"annotation-xml\"\n    ]);\n    // Certain elements are allowed in both SVG and HTML\n    // namespace. We need to specify them explicitly\n    // so that they don't get erroneously deleted from\n    // HTML namespace.\n    var COMMON_SVG_AND_HTML_ELEMENTS = addToSet({}, [\n        \"title\",\n        \"style\",\n        \"font\",\n        \"a\",\n        \"script\"\n    ]);\n    /* Keep track of all possible SVG and MathML tags\n   * so that we can perform the namespace checks\n   * correctly. */ var ALL_SVG_TAGS = addToSet({}, svg$1);\n    addToSet(ALL_SVG_TAGS, svgFilters);\n    addToSet(ALL_SVG_TAGS, svgDisallowed);\n    var ALL_MATHML_TAGS = addToSet({}, mathMl$1);\n    addToSet(ALL_MATHML_TAGS, mathMlDisallowed);\n    /**\n   *\n   *\n   * @param  {Element} element a DOM element whose namespace is being checked\n   * @returns {boolean} Return false if the element has a\n   *  namespace that a spec-compliant parser would never\n   *  return. Return true otherwise.\n   */ var _checkValidNamespace = function _checkValidNamespace(element) {\n        var parent = getParentNode(element);\n        // In JSDOM, if we're inside shadow DOM, then parentNode\n        // can be null. We just simulate parent in this case.\n        if (!parent || !parent.tagName) {\n            parent = {\n                namespaceURI: NAMESPACE,\n                tagName: \"template\"\n            };\n        }\n        var tagName = stringToLowerCase(element.tagName);\n        var parentTagName = stringToLowerCase(parent.tagName);\n        if (!ALLOWED_NAMESPACES[element.namespaceURI]) {\n            return false;\n        }\n        if (element.namespaceURI === SVG_NAMESPACE) {\n            // The only way to switch from HTML namespace to SVG\n            // is via <svg>. If it happens via any other tag, then\n            // it should be killed.\n            if (parent.namespaceURI === HTML_NAMESPACE) {\n                return tagName === \"svg\";\n            }\n            // The only way to switch from MathML to SVG is via`\n            // svg if parent is either <annotation-xml> or MathML\n            // text integration points.\n            if (parent.namespaceURI === MATHML_NAMESPACE) {\n                return tagName === \"svg\" && (parentTagName === \"annotation-xml\" || MATHML_TEXT_INTEGRATION_POINTS[parentTagName]);\n            }\n            // We only allow elements that are defined in SVG\n            // spec. All others are disallowed in SVG namespace.\n            return Boolean(ALL_SVG_TAGS[tagName]);\n        }\n        if (element.namespaceURI === MATHML_NAMESPACE) {\n            // The only way to switch from HTML namespace to MathML\n            // is via <math>. If it happens via any other tag, then\n            // it should be killed.\n            if (parent.namespaceURI === HTML_NAMESPACE) {\n                return tagName === \"math\";\n            }\n            // The only way to switch from SVG to MathML is via\n            // <math> and HTML integration points\n            if (parent.namespaceURI === SVG_NAMESPACE) {\n                return tagName === \"math\" && HTML_INTEGRATION_POINTS[parentTagName];\n            }\n            // We only allow elements that are defined in MathML\n            // spec. All others are disallowed in MathML namespace.\n            return Boolean(ALL_MATHML_TAGS[tagName]);\n        }\n        if (element.namespaceURI === HTML_NAMESPACE) {\n            // The only way to switch from SVG to HTML is via\n            // HTML integration points, and from MathML to HTML\n            // is via MathML text integration points\n            if (parent.namespaceURI === SVG_NAMESPACE && !HTML_INTEGRATION_POINTS[parentTagName]) {\n                return false;\n            }\n            if (parent.namespaceURI === MATHML_NAMESPACE && !MATHML_TEXT_INTEGRATION_POINTS[parentTagName]) {\n                return false;\n            }\n            // We disallow tags that are specific for MathML\n            // or SVG and should never appear in HTML namespace\n            return !ALL_MATHML_TAGS[tagName] && (COMMON_SVG_AND_HTML_ELEMENTS[tagName] || !ALL_SVG_TAGS[tagName]);\n        }\n        // For XHTML and XML documents that support custom namespaces\n        if (PARSER_MEDIA_TYPE === \"application/xhtml+xml\" && ALLOWED_NAMESPACES[element.namespaceURI]) {\n            return true;\n        }\n        // The code should never reach this place (this means\n        // that the element somehow got namespace that is not\n        // HTML, SVG, MathML or allowed via ALLOWED_NAMESPACES).\n        // Return false just in case.\n        return false;\n    };\n    /**\n   * _forceRemove\n   *\n   * @param  {Node} node a DOM node\n   */ var _forceRemove = function _forceRemove(node) {\n        arrayPush(DOMPurify.removed, {\n            element: node\n        });\n        try {\n            // eslint-disable-next-line unicorn/prefer-dom-node-remove\n            node.parentNode.removeChild(node);\n        } catch (_) {\n            try {\n                node.outerHTML = emptyHTML;\n            } catch (_) {\n                node.remove();\n            }\n        }\n    };\n    /**\n   * _removeAttribute\n   *\n   * @param  {String} name an Attribute name\n   * @param  {Node} node a DOM node\n   */ var _removeAttribute = function _removeAttribute(name, node) {\n        try {\n            arrayPush(DOMPurify.removed, {\n                attribute: node.getAttributeNode(name),\n                from: node\n            });\n        } catch (_) {\n            arrayPush(DOMPurify.removed, {\n                attribute: null,\n                from: node\n            });\n        }\n        node.removeAttribute(name);\n        // We void attribute values for unremovable \"is\"\" attributes\n        if (name === \"is\" && !ALLOWED_ATTR[name]) {\n            if (RETURN_DOM || RETURN_DOM_FRAGMENT) {\n                try {\n                    _forceRemove(node);\n                } catch (_) {}\n            } else {\n                try {\n                    node.setAttribute(name, \"\");\n                } catch (_) {}\n            }\n        }\n    };\n    /**\n   * _initDocument\n   *\n   * @param  {String} dirty a string of dirty markup\n   * @return {Document} a DOM, filled with the dirty markup\n   */ var _initDocument = function _initDocument(dirty) {\n        /* Create a HTML document */ var doc;\n        var leadingWhitespace;\n        if (FORCE_BODY) {\n            dirty = \"<remove></remove>\" + dirty;\n        } else {\n            /* If FORCE_BODY isn't used, leading whitespace needs to be preserved manually */ var matches = stringMatch(dirty, /^[\\r\\n\\t ]+/);\n            leadingWhitespace = matches && matches[0];\n        }\n        if (PARSER_MEDIA_TYPE === \"application/xhtml+xml\" && NAMESPACE === HTML_NAMESPACE) {\n            // Root of XHTML doc must contain xmlns declaration (see https://www.w3.org/TR/xhtml1/normative.html#strict)\n            dirty = '<html xmlns=\"http://www.w3.org/1999/xhtml\"><head></head><body>' + dirty + \"</body></html>\";\n        }\n        var dirtyPayload = trustedTypesPolicy ? trustedTypesPolicy.createHTML(dirty) : dirty;\n        /*\n     * Use the DOMParser API by default, fallback later if needs be\n     * DOMParser not work for svg when has multiple root element.\n     */ if (NAMESPACE === HTML_NAMESPACE) {\n            try {\n                doc = new DOMParser().parseFromString(dirtyPayload, PARSER_MEDIA_TYPE);\n            } catch (_) {}\n        }\n        /* Use createHTMLDocument in case DOMParser is not available */ if (!doc || !doc.documentElement) {\n            doc = implementation.createDocument(NAMESPACE, \"template\", null);\n            try {\n                doc.documentElement.innerHTML = IS_EMPTY_INPUT ? emptyHTML : dirtyPayload;\n            } catch (_) {\n            // Syntax error if dirtyPayload is invalid xml\n            }\n        }\n        var body = doc.body || doc.documentElement;\n        if (dirty && leadingWhitespace) {\n            body.insertBefore(document.createTextNode(leadingWhitespace), body.childNodes[0] || null);\n        }\n        /* Work on whole document or just its body */ if (NAMESPACE === HTML_NAMESPACE) {\n            return getElementsByTagName.call(doc, WHOLE_DOCUMENT ? \"html\" : \"body\")[0];\n        }\n        return WHOLE_DOCUMENT ? doc.documentElement : body;\n    };\n    /**\n   * _createIterator\n   *\n   * @param  {Document} root document/fragment to create iterator for\n   * @return {Iterator} iterator instance\n   */ var _createIterator = function _createIterator(root) {\n        return createNodeIterator.call(root.ownerDocument || root, root, // eslint-disable-next-line no-bitwise\n        NodeFilter.SHOW_ELEMENT | NodeFilter.SHOW_COMMENT | NodeFilter.SHOW_TEXT | NodeFilter.SHOW_PROCESSING_INSTRUCTION | NodeFilter.SHOW_CDATA_SECTION, null, false);\n    };\n    /**\n   * _isClobbered\n   *\n   * @param  {Node} elm element to check for clobbering attacks\n   * @return {Boolean} true if clobbered, false if safe\n   */ var _isClobbered = function _isClobbered(elm) {\n        return elm instanceof HTMLFormElement && (typeof elm.nodeName !== \"string\" || typeof elm.textContent !== \"string\" || typeof elm.removeChild !== \"function\" || !(elm.attributes instanceof NamedNodeMap) || typeof elm.removeAttribute !== \"function\" || typeof elm.setAttribute !== \"function\" || typeof elm.namespaceURI !== \"string\" || typeof elm.insertBefore !== \"function\" || typeof elm.hasChildNodes !== \"function\");\n    };\n    /**\n   * _isNode\n   *\n   * @param  {Node} obj object to check whether it's a DOM node\n   * @return {Boolean} true is object is a DOM node\n   */ var _isNode = function _isNode(object) {\n        return _typeof(Node) === \"object\" ? object instanceof Node : object && _typeof(object) === \"object\" && typeof object.nodeType === \"number\" && typeof object.nodeName === \"string\";\n    };\n    /**\n   * _executeHook\n   * Execute user configurable hooks\n   *\n   * @param  {String} entryPoint  Name of the hook's entry point\n   * @param  {Node} currentNode node to work on with the hook\n   * @param  {Object} data additional hook parameters\n   */ var _executeHook = function _executeHook(entryPoint, currentNode, data) {\n        if (!hooks[entryPoint]) {\n            return;\n        }\n        arrayForEach(hooks[entryPoint], function(hook) {\n            hook.call(DOMPurify, currentNode, data, CONFIG);\n        });\n    };\n    /**\n   * _sanitizeElements\n   *\n   * @protect nodeName\n   * @protect textContent\n   * @protect removeChild\n   *\n   * @param   {Node} currentNode to check for permission to exist\n   * @return  {Boolean} true if node was killed, false if left alive\n   */ var _sanitizeElements = function _sanitizeElements(currentNode) {\n        var content;\n        /* Execute a hook if present */ _executeHook(\"beforeSanitizeElements\", currentNode, null);\n        /* Check if element is clobbered or can clobber */ if (_isClobbered(currentNode)) {\n            _forceRemove(currentNode);\n            return true;\n        }\n        /* Check if tagname contains Unicode */ if (regExpTest(/[\\u0080-\\uFFFF]/, currentNode.nodeName)) {\n            _forceRemove(currentNode);\n            return true;\n        }\n        /* Now let's check the element's type and name */ var tagName = transformCaseFunc(currentNode.nodeName);\n        /* Execute a hook if present */ _executeHook(\"uponSanitizeElement\", currentNode, {\n            tagName: tagName,\n            allowedTags: ALLOWED_TAGS\n        });\n        /* Detect mXSS attempts abusing namespace confusion */ if (currentNode.hasChildNodes() && !_isNode(currentNode.firstElementChild) && (!_isNode(currentNode.content) || !_isNode(currentNode.content.firstElementChild)) && regExpTest(/<[/\\w]/g, currentNode.innerHTML) && regExpTest(/<[/\\w]/g, currentNode.textContent)) {\n            _forceRemove(currentNode);\n            return true;\n        }\n        /* Mitigate a problem with templates inside select */ if (tagName === \"select\" && regExpTest(/<template/i, currentNode.innerHTML)) {\n            _forceRemove(currentNode);\n            return true;\n        }\n        /* Remove any ocurrence of processing instructions */ if (currentNode.nodeType === 7) {\n            _forceRemove(currentNode);\n            return true;\n        }\n        /* Remove any kind of possibly harmful comments */ if (SAFE_FOR_XML && currentNode.nodeType === 8 && regExpTest(/<[/\\w]/g, currentNode.data)) {\n            _forceRemove(currentNode);\n            return true;\n        }\n        /* Remove element if anything forbids its presence */ if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n            /* Check if we have a custom element to handle */ if (!FORBID_TAGS[tagName] && _basicCustomElementTest(tagName)) {\n                if (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, tagName)) return false;\n                if (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.tagNameCheck(tagName)) return false;\n            }\n            /* Keep content except for bad-listed elements */ if (KEEP_CONTENT && !FORBID_CONTENTS[tagName]) {\n                var parentNode = getParentNode(currentNode) || currentNode.parentNode;\n                var childNodes = getChildNodes(currentNode) || currentNode.childNodes;\n                if (childNodes && parentNode) {\n                    var childCount = childNodes.length;\n                    for(var i = childCount - 1; i >= 0; --i){\n                        var childClone = cloneNode(childNodes[i], true);\n                        childClone.__removalCount = (currentNode.__removalCount || 0) + 1;\n                        parentNode.insertBefore(childClone, getNextSibling(currentNode));\n                    }\n                }\n            }\n            _forceRemove(currentNode);\n            return true;\n        }\n        /* Check whether element has a valid namespace */ if (currentNode instanceof Element && !_checkValidNamespace(currentNode)) {\n            _forceRemove(currentNode);\n            return true;\n        }\n        /* Make sure that older browsers don't get fallback-tag mXSS */ if ((tagName === \"noscript\" || tagName === \"noembed\" || tagName === \"noframes\") && regExpTest(/<\\/no(script|embed|frames)/i, currentNode.innerHTML)) {\n            _forceRemove(currentNode);\n            return true;\n        }\n        /* Sanitize element content to be template-safe */ if (SAFE_FOR_TEMPLATES && currentNode.nodeType === 3) {\n            /* Get the element's text content */ content = currentNode.textContent;\n            content = stringReplace(content, MUSTACHE_EXPR$1, \" \");\n            content = stringReplace(content, ERB_EXPR$1, \" \");\n            content = stringReplace(content, TMPLIT_EXPR$1, \" \");\n            if (currentNode.textContent !== content) {\n                arrayPush(DOMPurify.removed, {\n                    element: currentNode.cloneNode()\n                });\n                currentNode.textContent = content;\n            }\n        }\n        /* Execute a hook if present */ _executeHook(\"afterSanitizeElements\", currentNode, null);\n        return false;\n    };\n    /**\n   * _isValidAttribute\n   *\n   * @param  {string} lcTag Lowercase tag name of containing element.\n   * @param  {string} lcName Lowercase attribute name.\n   * @param  {string} value Attribute value.\n   * @return {Boolean} Returns true if `value` is valid, otherwise false.\n   */ // eslint-disable-next-line complexity\n    var _isValidAttribute = function _isValidAttribute(lcTag, lcName, value) {\n        /* Make sure attribute cannot clobber */ if (SANITIZE_DOM && (lcName === \"id\" || lcName === \"name\") && (value in document || value in formElement)) {\n            return false;\n        }\n        /* Allow valid data-* attributes: At least one character after \"-\"\n        (https://html.spec.whatwg.org/multipage/dom.html#embedding-custom-non-visible-data-with-the-data-*-attributes)\n        XML-compatible (https://html.spec.whatwg.org/multipage/infrastructure.html#xml-compatible and http://www.w3.org/TR/xml/#d0e804)\n        We don't need to check the value; it's always URI safe. */ if (ALLOW_DATA_ATTR && !FORBID_ATTR[lcName] && regExpTest(DATA_ATTR$1, lcName)) ;\n        else if (ALLOW_ARIA_ATTR && regExpTest(ARIA_ATTR$1, lcName)) ;\n        else if (!ALLOWED_ATTR[lcName] || FORBID_ATTR[lcName]) {\n            if (// First condition does a very basic check if a) it's basically a valid custom element tagname AND\n            // b) if the tagName passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n            // and c) if the attribute name passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.attributeNameCheck\n            _basicCustomElementTest(lcTag) && (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, lcTag) || CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.tagNameCheck(lcTag)) && (CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.attributeNameCheck, lcName) || CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.attributeNameCheck(lcName)) || // Alternative, second condition checks if it's an `is`-attribute, AND\n            // the value passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n            lcName === \"is\" && CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements && (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, value) || CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.tagNameCheck(value))) ;\n            else {\n                return false;\n            }\n        /* Check value is safe. First, is attr inert? If so, is safe */ } else if (URI_SAFE_ATTRIBUTES[lcName]) ;\n        else if (regExpTest(IS_ALLOWED_URI$1, stringReplace(value, ATTR_WHITESPACE$1, \"\"))) ;\n        else if ((lcName === \"src\" || lcName === \"xlink:href\" || lcName === \"href\") && lcTag !== \"script\" && stringIndexOf(value, \"data:\") === 0 && DATA_URI_TAGS[lcTag]) ;\n        else if (ALLOW_UNKNOWN_PROTOCOLS && !regExpTest(IS_SCRIPT_OR_DATA$1, stringReplace(value, ATTR_WHITESPACE$1, \"\"))) ;\n        else if (value) {\n            return false;\n        } else ;\n        return true;\n    };\n    /**\n   * _basicCustomElementCheck\n   * checks if at least one dash is included in tagName, and it's not the first char\n   * for more sophisticated checking see https://github.com/sindresorhus/validate-element-name\n   * @param {string} tagName name of the tag of the node to sanitize\n   */ var _basicCustomElementTest = function _basicCustomElementTest(tagName) {\n        return tagName !== \"annotation-xml\" && stringMatch(tagName, CUSTOM_ELEMENT$1);\n    };\n    /**\n   * _sanitizeAttributes\n   *\n   * @protect attributes\n   * @protect nodeName\n   * @protect removeAttribute\n   * @protect setAttribute\n   *\n   * @param  {Node} currentNode to sanitize\n   */ var _sanitizeAttributes = function _sanitizeAttributes(currentNode) {\n        var attr;\n        var value;\n        var lcName;\n        var l;\n        /* Execute a hook if present */ _executeHook(\"beforeSanitizeAttributes\", currentNode, null);\n        var attributes = currentNode.attributes;\n        /* Check if we have attributes; if not we might have a text node */ if (!attributes || _isClobbered(currentNode)) {\n            return;\n        }\n        var hookEvent = {\n            attrName: \"\",\n            attrValue: \"\",\n            keepAttr: true,\n            allowedAttributes: ALLOWED_ATTR\n        };\n        l = attributes.length;\n        /* Go backwards over all attributes; safely remove bad ones */ while(l--){\n            attr = attributes[l];\n            var _attr = attr, name = _attr.name, namespaceURI = _attr.namespaceURI;\n            value = name === \"value\" ? attr.value : stringTrim(attr.value);\n            lcName = transformCaseFunc(name);\n            /* Execute a hook if present */ hookEvent.attrName = lcName;\n            hookEvent.attrValue = value;\n            hookEvent.keepAttr = true;\n            hookEvent.forceKeepAttr = undefined; // Allows developers to see this is a property they can set\n            _executeHook(\"uponSanitizeAttribute\", currentNode, hookEvent);\n            value = hookEvent.attrValue;\n            /* Did the hooks approve of the attribute? */ if (hookEvent.forceKeepAttr) {\n                continue;\n            }\n            /* Remove attribute */ _removeAttribute(name, currentNode);\n            /* Did the hooks approve of the attribute? */ if (!hookEvent.keepAttr) {\n                continue;\n            }\n            /* Work around a security issue in jQuery 3.0 */ if (!ALLOW_SELF_CLOSE_IN_ATTR && regExpTest(/\\/>/i, value)) {\n                _removeAttribute(name, currentNode);\n                continue;\n            }\n            /* Sanitize attribute content to be template-safe */ if (SAFE_FOR_TEMPLATES) {\n                value = stringReplace(value, MUSTACHE_EXPR$1, \" \");\n                value = stringReplace(value, ERB_EXPR$1, \" \");\n                value = stringReplace(value, TMPLIT_EXPR$1, \" \");\n            }\n            /* Is `value` valid for this attribute? */ var lcTag = transformCaseFunc(currentNode.nodeName);\n            if (!_isValidAttribute(lcTag, lcName, value)) {\n                continue;\n            }\n            /* Full DOM Clobbering protection via namespace isolation,\n       * Prefix id and name attributes with `user-content-`\n       */ if (SANITIZE_NAMED_PROPS && (lcName === \"id\" || lcName === \"name\")) {\n                // Remove the attribute with this value\n                _removeAttribute(name, currentNode);\n                // Prefix the value and later re-create the attribute with the sanitized value\n                value = SANITIZE_NAMED_PROPS_PREFIX + value;\n            }\n            /* Work around a security issue with comments inside attributes */ if (SAFE_FOR_XML && regExpTest(/((--!?|])>)|<\\/(style|title)/i, value)) {\n                _removeAttribute(name, currentNode);\n                continue;\n            }\n            /* Handle attributes that require Trusted Types */ if (trustedTypesPolicy && _typeof(trustedTypes) === \"object\" && typeof trustedTypes.getAttributeType === \"function\") {\n                if (namespaceURI) ;\n                else {\n                    switch(trustedTypes.getAttributeType(lcTag, lcName)){\n                        case \"TrustedHTML\":\n                            {\n                                value = trustedTypesPolicy.createHTML(value);\n                                break;\n                            }\n                        case \"TrustedScriptURL\":\n                            {\n                                value = trustedTypesPolicy.createScriptURL(value);\n                                break;\n                            }\n                    }\n                }\n            }\n            /* Handle invalid data-* attribute set by try-catching it */ try {\n                if (namespaceURI) {\n                    currentNode.setAttributeNS(namespaceURI, name, value);\n                } else {\n                    /* Fallback to setAttribute() for browser-unrecognized namespaces e.g. \"x-schema\". */ currentNode.setAttribute(name, value);\n                }\n                if (_isClobbered(currentNode)) {\n                    _forceRemove(currentNode);\n                } else {\n                    arrayPop(DOMPurify.removed);\n                }\n            } catch (_) {}\n        }\n        /* Execute a hook if present */ _executeHook(\"afterSanitizeAttributes\", currentNode, null);\n    };\n    /**\n   * _sanitizeShadowDOM\n   *\n   * @param  {DocumentFragment} fragment to iterate over recursively\n   */ var _sanitizeShadowDOM = function _sanitizeShadowDOM(fragment) {\n        var shadowNode;\n        var shadowIterator = _createIterator(fragment);\n        /* Execute a hook if present */ _executeHook(\"beforeSanitizeShadowDOM\", fragment, null);\n        while(shadowNode = shadowIterator.nextNode()){\n            /* Execute a hook if present */ _executeHook(\"uponSanitizeShadowNode\", shadowNode, null);\n            /* Sanitize tags and elements */ _sanitizeElements(shadowNode);\n            /* Check attributes next */ _sanitizeAttributes(shadowNode);\n            /* Deep shadow DOM detected */ if (shadowNode.content instanceof DocumentFragment) {\n                _sanitizeShadowDOM(shadowNode.content);\n            }\n        }\n        /* Execute a hook if present */ _executeHook(\"afterSanitizeShadowDOM\", fragment, null);\n    };\n    /**\n   * Sanitize\n   * Public method providing core sanitation functionality\n   *\n   * @param {String|Node} dirty string or DOM node\n   * @param {Object} configuration object\n   */ // eslint-disable-next-line complexity\n    DOMPurify.sanitize = function(dirty) {\n        var cfg = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        var body;\n        var importedNode;\n        var currentNode;\n        var oldNode;\n        var returnNode;\n        /* Make sure we have a string to sanitize.\n      DO NOT return early, as this will return the wrong type if\n      the user has requested a DOM object rather than a string */ IS_EMPTY_INPUT = !dirty;\n        if (IS_EMPTY_INPUT) {\n            dirty = \"<!-->\";\n        }\n        /* Stringify, in case dirty is an object */ if (typeof dirty !== \"string\" && !_isNode(dirty)) {\n            if (typeof dirty.toString === \"function\") {\n                dirty = dirty.toString();\n                if (typeof dirty !== \"string\") {\n                    throw typeErrorCreate(\"dirty is not a string, aborting\");\n                }\n            } else {\n                throw typeErrorCreate(\"toString is not a function\");\n            }\n        }\n        /* Check we can run. Otherwise fall back or ignore */ if (!DOMPurify.isSupported) {\n            if (_typeof(window1.toStaticHTML) === \"object\" || typeof window1.toStaticHTML === \"function\") {\n                if (typeof dirty === \"string\") {\n                    return window1.toStaticHTML(dirty);\n                }\n                if (_isNode(dirty)) {\n                    return window1.toStaticHTML(dirty.outerHTML);\n                }\n            }\n            return dirty;\n        }\n        /* Assign config vars */ if (!SET_CONFIG) {\n            _parseConfig(cfg);\n        }\n        /* Clean up removed elements */ DOMPurify.removed = [];\n        /* Check if dirty is correctly typed for IN_PLACE */ if (typeof dirty === \"string\") {\n            IN_PLACE = false;\n        }\n        if (IN_PLACE) {\n            /* Do some early pre-sanitization to avoid unsafe root nodes */ if (dirty.nodeName) {\n                var tagName = transformCaseFunc(dirty.nodeName);\n                if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n                    throw typeErrorCreate(\"root node is forbidden and cannot be sanitized in-place\");\n                }\n            }\n        } else if (dirty instanceof Node) {\n            /* If dirty is a DOM element, append to an empty document to avoid\n         elements being stripped by the parser */ body = _initDocument(\"<!---->\");\n            importedNode = body.ownerDocument.importNode(dirty, true);\n            if (importedNode.nodeType === 1 && importedNode.nodeName === \"BODY\") {\n                /* Node is already a body, use as is */ body = importedNode;\n            } else if (importedNode.nodeName === \"HTML\") {\n                body = importedNode;\n            } else {\n                // eslint-disable-next-line unicorn/prefer-dom-node-append\n                body.appendChild(importedNode);\n            }\n        } else {\n            /* Exit directly if we have nothing to do */ if (!RETURN_DOM && !SAFE_FOR_TEMPLATES && !WHOLE_DOCUMENT && // eslint-disable-next-line unicorn/prefer-includes\n            dirty.indexOf(\"<\") === -1) {\n                return trustedTypesPolicy && RETURN_TRUSTED_TYPE ? trustedTypesPolicy.createHTML(dirty) : dirty;\n            }\n            /* Initialize the document to work on */ body = _initDocument(dirty);\n            /* Check we have a DOM node from the data */ if (!body) {\n                return RETURN_DOM ? null : RETURN_TRUSTED_TYPE ? emptyHTML : \"\";\n            }\n        }\n        /* Remove first element node (ours) if FORCE_BODY is set */ if (body && FORCE_BODY) {\n            _forceRemove(body.firstChild);\n        }\n        /* Get node iterator */ var nodeIterator = _createIterator(IN_PLACE ? dirty : body);\n        /* Now start iterating over the created document */ while(currentNode = nodeIterator.nextNode()){\n            /* Fix IE's strange behavior with manipulated textNodes #89 */ if (currentNode.nodeType === 3 && currentNode === oldNode) {\n                continue;\n            }\n            /* Sanitize tags and elements */ _sanitizeElements(currentNode);\n            /* Check attributes next */ _sanitizeAttributes(currentNode);\n            /* Shadow DOM detected, sanitize it */ if (currentNode.content instanceof DocumentFragment) {\n                _sanitizeShadowDOM(currentNode.content);\n            }\n            oldNode = currentNode;\n        }\n        oldNode = null;\n        /* If we sanitized `dirty` in-place, return it. */ if (IN_PLACE) {\n            return dirty;\n        }\n        /* Return sanitized string or DOM */ if (RETURN_DOM) {\n            if (RETURN_DOM_FRAGMENT) {\n                returnNode = createDocumentFragment.call(body.ownerDocument);\n                while(body.firstChild){\n                    // eslint-disable-next-line unicorn/prefer-dom-node-append\n                    returnNode.appendChild(body.firstChild);\n                }\n            } else {\n                returnNode = body;\n            }\n            if (ALLOWED_ATTR.shadowroot || ALLOWED_ATTR.shadowrootmod) {\n                /*\n          AdoptNode() is not used because internal state is not reset\n          (e.g. the past names map of a HTMLFormElement), this is safe\n          in theory but we would rather not risk another attack vector.\n          The state that is cloned by importNode() is explicitly defined\n          by the specs.\n        */ returnNode = importNode.call(originalDocument, returnNode, true);\n            }\n            return returnNode;\n        }\n        var serializedHTML = WHOLE_DOCUMENT ? body.outerHTML : body.innerHTML;\n        /* Serialize doctype if allowed */ if (WHOLE_DOCUMENT && ALLOWED_TAGS[\"!doctype\"] && body.ownerDocument && body.ownerDocument.doctype && body.ownerDocument.doctype.name && regExpTest(DOCTYPE_NAME, body.ownerDocument.doctype.name)) {\n            serializedHTML = \"<!DOCTYPE \" + body.ownerDocument.doctype.name + \">\\n\" + serializedHTML;\n        }\n        /* Sanitize final string template-safe */ if (SAFE_FOR_TEMPLATES) {\n            serializedHTML = stringReplace(serializedHTML, MUSTACHE_EXPR$1, \" \");\n            serializedHTML = stringReplace(serializedHTML, ERB_EXPR$1, \" \");\n            serializedHTML = stringReplace(serializedHTML, TMPLIT_EXPR$1, \" \");\n        }\n        return trustedTypesPolicy && RETURN_TRUSTED_TYPE ? trustedTypesPolicy.createHTML(serializedHTML) : serializedHTML;\n    };\n    /**\n   * Public method to set the configuration once\n   * setConfig\n   *\n   * @param {Object} cfg configuration object\n   */ DOMPurify.setConfig = function(cfg) {\n        _parseConfig(cfg);\n        SET_CONFIG = true;\n    };\n    /**\n   * Public method to remove the configuration\n   * clearConfig\n   *\n   */ DOMPurify.clearConfig = function() {\n        CONFIG = null;\n        SET_CONFIG = false;\n    };\n    /**\n   * Public method to check if an attribute value is valid.\n   * Uses last set config, if any. Otherwise, uses config defaults.\n   * isValidAttribute\n   *\n   * @param  {string} tag Tag name of containing element.\n   * @param  {string} attr Attribute name.\n   * @param  {string} value Attribute value.\n   * @return {Boolean} Returns true if `value` is valid. Otherwise, returns false.\n   */ DOMPurify.isValidAttribute = function(tag, attr, value) {\n        /* Initialize shared config vars if necessary. */ if (!CONFIG) {\n            _parseConfig({});\n        }\n        var lcTag = transformCaseFunc(tag);\n        var lcName = transformCaseFunc(attr);\n        return _isValidAttribute(lcTag, lcName, value);\n    };\n    /**\n   * AddHook\n   * Public method to add DOMPurify hooks\n   *\n   * @param {String} entryPoint entry point for the hook to add\n   * @param {Function} hookFunction function to execute\n   */ DOMPurify.addHook = function(entryPoint, hookFunction) {\n        if (typeof hookFunction !== \"function\") {\n            return;\n        }\n        hooks[entryPoint] = hooks[entryPoint] || [];\n        arrayPush(hooks[entryPoint], hookFunction);\n    };\n    /**\n   * RemoveHook\n   * Public method to remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if more are present)\n   *\n   * @param {String} entryPoint entry point for the hook to remove\n   * @return {Function} removed(popped) hook\n   */ DOMPurify.removeHook = function(entryPoint) {\n        if (hooks[entryPoint]) {\n            return arrayPop(hooks[entryPoint]);\n        }\n    };\n    /**\n   * RemoveHooks\n   * Public method to remove all DOMPurify hooks at a given entryPoint\n   *\n   * @param  {String} entryPoint entry point for the hooks to remove\n   */ DOMPurify.removeHooks = function(entryPoint) {\n        if (hooks[entryPoint]) {\n            hooks[entryPoint] = [];\n        }\n    };\n    /**\n   * RemoveAllHooks\n   * Public method to remove all DOMPurify hooks\n   *\n   */ DOMPurify.removeAllHooks = function() {\n        hooks = {};\n    };\n    return DOMPurify;\n}\nvar purify = createDOMPurify();\n //# sourceMappingURL=purify.es.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dompurify/dist/purify.es.js\n");

/***/ })

};
;