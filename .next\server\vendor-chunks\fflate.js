"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fflate";
exports.ids = ["vendor-chunks/fflate"];
exports.modules = {

/***/ "(ssr)/./node_modules/fflate/esm/index.mjs":
/*!*******************************************!*\
  !*** ./node_modules/fflate/esm/index.mjs ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AsyncCompress: () => (/* binding */ AsyncGzip),\n/* harmony export */   AsyncDecompress: () => (/* binding */ AsyncDecompress),\n/* harmony export */   AsyncDeflate: () => (/* binding */ AsyncDeflate),\n/* harmony export */   AsyncGunzip: () => (/* binding */ AsyncGunzip),\n/* harmony export */   AsyncGzip: () => (/* binding */ AsyncGzip),\n/* harmony export */   AsyncInflate: () => (/* binding */ AsyncInflate),\n/* harmony export */   AsyncUnzipInflate: () => (/* binding */ AsyncUnzipInflate),\n/* harmony export */   AsyncUnzlib: () => (/* binding */ AsyncUnzlib),\n/* harmony export */   AsyncZipDeflate: () => (/* binding */ AsyncZipDeflate),\n/* harmony export */   AsyncZlib: () => (/* binding */ AsyncZlib),\n/* harmony export */   Compress: () => (/* binding */ Gzip),\n/* harmony export */   DecodeUTF8: () => (/* binding */ DecodeUTF8),\n/* harmony export */   Decompress: () => (/* binding */ Decompress),\n/* harmony export */   Deflate: () => (/* binding */ Deflate),\n/* harmony export */   EncodeUTF8: () => (/* binding */ EncodeUTF8),\n/* harmony export */   FlateErrorCode: () => (/* binding */ FlateErrorCode),\n/* harmony export */   Gunzip: () => (/* binding */ Gunzip),\n/* harmony export */   Gzip: () => (/* binding */ Gzip),\n/* harmony export */   Inflate: () => (/* binding */ Inflate),\n/* harmony export */   Unzip: () => (/* binding */ Unzip),\n/* harmony export */   UnzipInflate: () => (/* binding */ UnzipInflate),\n/* harmony export */   UnzipPassThrough: () => (/* binding */ UnzipPassThrough),\n/* harmony export */   Unzlib: () => (/* binding */ Unzlib),\n/* harmony export */   Zip: () => (/* binding */ Zip),\n/* harmony export */   ZipDeflate: () => (/* binding */ ZipDeflate),\n/* harmony export */   ZipPassThrough: () => (/* binding */ ZipPassThrough),\n/* harmony export */   Zlib: () => (/* binding */ Zlib),\n/* harmony export */   compress: () => (/* binding */ gzip),\n/* harmony export */   compressSync: () => (/* binding */ gzipSync),\n/* harmony export */   decompress: () => (/* binding */ decompress),\n/* harmony export */   decompressSync: () => (/* binding */ decompressSync),\n/* harmony export */   deflate: () => (/* binding */ deflate),\n/* harmony export */   deflateSync: () => (/* binding */ deflateSync),\n/* harmony export */   gunzip: () => (/* binding */ gunzip),\n/* harmony export */   gunzipSync: () => (/* binding */ gunzipSync),\n/* harmony export */   gzip: () => (/* binding */ gzip),\n/* harmony export */   gzipSync: () => (/* binding */ gzipSync),\n/* harmony export */   inflate: () => (/* binding */ inflate),\n/* harmony export */   inflateSync: () => (/* binding */ inflateSync),\n/* harmony export */   strFromU8: () => (/* binding */ strFromU8),\n/* harmony export */   strToU8: () => (/* binding */ strToU8),\n/* harmony export */   unzip: () => (/* binding */ unzip),\n/* harmony export */   unzipSync: () => (/* binding */ unzipSync),\n/* harmony export */   unzlib: () => (/* binding */ unzlib),\n/* harmony export */   unzlibSync: () => (/* binding */ unzlibSync),\n/* harmony export */   zip: () => (/* binding */ zip),\n/* harmony export */   zipSync: () => (/* binding */ zipSync),\n/* harmony export */   zlib: () => (/* binding */ zlib),\n/* harmony export */   zlibSync: () => (/* binding */ zlibSync)\n/* harmony export */ });\n/* harmony import */ var module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! module */ \"module\");\n\nvar require = (0,module__WEBPACK_IMPORTED_MODULE_0__.createRequire)(\"/\");\n// DEFLATE is a complex format; to read this code, you should probably check the RFC first:\n// https://tools.ietf.org/html/rfc1951\n// You may also wish to take a look at the guide I made about this program:\n// https://gist.github.com/101arrowz/253f31eb5abc3d9275ab943003ffecad\n// Some of the following code is similar to that of UZIP.js:\n// https://github.com/photopea/UZIP.js\n// However, the vast majority of the codebase has diverged from UZIP.js to increase performance and reduce bundle size.\n// Sometimes 0 will appear where -1 would be more appropriate. This is because using a uint\n// is better for memory in most engines (I *think*).\n// Mediocre shim\nvar Worker;\nvar workerAdd = \";var __w=require('worker_threads');__w.parentPort.on('message',function(m){onmessage({data:m})}),postMessage=function(m,t){__w.parentPort.postMessage(m,t)},close=process.exit;self=global\";\ntry {\n    Worker = require(\"worker_threads\").Worker;\n} catch (e) {}\nvar wk = Worker ? function(c, _, msg, transfer, cb) {\n    var done = false;\n    var w = new Worker(c + workerAdd, {\n        eval: true\n    }).on(\"error\", function(e) {\n        return cb(e, null);\n    }).on(\"message\", function(m) {\n        return cb(null, m);\n    }).on(\"exit\", function(c) {\n        if (c && !done) cb(new Error(\"exited with code \" + c), null);\n    });\n    w.postMessage(msg, transfer);\n    w.terminate = function() {\n        done = true;\n        return Worker.prototype.terminate.call(w);\n    };\n    return w;\n} : function(_, __, ___, ____, cb) {\n    setImmediate(function() {\n        return cb(new Error(\"async operations unsupported - update to Node 12+ (or Node 10-11 with the --experimental-worker CLI flag)\"), null);\n    });\n    var NOP = function() {};\n    return {\n        terminate: NOP,\n        postMessage: NOP\n    };\n};\n// aliases for shorter compressed code (most minifers don't do this)\nvar u8 = Uint8Array, u16 = Uint16Array, i32 = Int32Array;\n// fixed length extra bits\nvar fleb = new u8([\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    1,\n    1,\n    1,\n    1,\n    2,\n    2,\n    2,\n    2,\n    3,\n    3,\n    3,\n    3,\n    4,\n    4,\n    4,\n    4,\n    5,\n    5,\n    5,\n    5,\n    0,\n    /* unused */ 0,\n    0,\n    /* impossible */ 0\n]);\n// fixed distance extra bits\nvar fdeb = new u8([\n    0,\n    0,\n    0,\n    0,\n    1,\n    1,\n    2,\n    2,\n    3,\n    3,\n    4,\n    4,\n    5,\n    5,\n    6,\n    6,\n    7,\n    7,\n    8,\n    8,\n    9,\n    9,\n    10,\n    10,\n    11,\n    11,\n    12,\n    12,\n    13,\n    13,\n    /* unused */ 0,\n    0\n]);\n// code length index map\nvar clim = new u8([\n    16,\n    17,\n    18,\n    0,\n    8,\n    7,\n    9,\n    6,\n    10,\n    5,\n    11,\n    4,\n    12,\n    3,\n    13,\n    2,\n    14,\n    1,\n    15\n]);\n// get base, reverse index map from extra bits\nvar freb = function(eb, start) {\n    var b = new u16(31);\n    for(var i = 0; i < 31; ++i){\n        b[i] = start += 1 << eb[i - 1];\n    }\n    // numbers here are at max 18 bits\n    var r = new i32(b[30]);\n    for(var i = 1; i < 30; ++i){\n        for(var j = b[i]; j < b[i + 1]; ++j){\n            r[j] = j - b[i] << 5 | i;\n        }\n    }\n    return {\n        b: b,\n        r: r\n    };\n};\nvar _a = freb(fleb, 2), fl = _a.b, revfl = _a.r;\n// we can ignore the fact that the other numbers are wrong; they never happen anyway\nfl[28] = 258, revfl[258] = 28;\nvar _b = freb(fdeb, 0), fd = _b.b, revfd = _b.r;\n// map of value to reverse (assuming 16 bits)\nvar rev = new u16(32768);\nfor(var i = 0; i < 32768; ++i){\n    // reverse table algorithm from SO\n    var x = (i & 0xAAAA) >> 1 | (i & 0x5555) << 1;\n    x = (x & 0xCCCC) >> 2 | (x & 0x3333) << 2;\n    x = (x & 0xF0F0) >> 4 | (x & 0x0F0F) << 4;\n    rev[i] = ((x & 0xFF00) >> 8 | (x & 0x00FF) << 8) >> 1;\n}\n// create huffman tree from u8 \"map\": index -> code length for code index\n// mb (max bits) must be at most 15\n// TODO: optimize/split up?\nvar hMap = function(cd, mb, r) {\n    var s = cd.length;\n    // index\n    var i = 0;\n    // u16 \"map\": index -> # of codes with bit length = index\n    var l = new u16(mb);\n    // length of cd must be 288 (total # of codes)\n    for(; i < s; ++i){\n        if (cd[i]) ++l[cd[i] - 1];\n    }\n    // u16 \"map\": index -> minimum code for bit length = index\n    var le = new u16(mb);\n    for(i = 1; i < mb; ++i){\n        le[i] = le[i - 1] + l[i - 1] << 1;\n    }\n    var co;\n    if (r) {\n        // u16 \"map\": index -> number of actual bits, symbol for code\n        co = new u16(1 << mb);\n        // bits to remove for reverser\n        var rvb = 15 - mb;\n        for(i = 0; i < s; ++i){\n            // ignore 0 lengths\n            if (cd[i]) {\n                // num encoding both symbol and bits read\n                var sv = i << 4 | cd[i];\n                // free bits\n                var r_1 = mb - cd[i];\n                // start value\n                var v = le[cd[i] - 1]++ << r_1;\n                // m is end value\n                for(var m = v | (1 << r_1) - 1; v <= m; ++v){\n                    // every 16 bit value starting with the code yields the same result\n                    co[rev[v] >> rvb] = sv;\n                }\n            }\n        }\n    } else {\n        co = new u16(s);\n        for(i = 0; i < s; ++i){\n            if (cd[i]) {\n                co[i] = rev[le[cd[i] - 1]++] >> 15 - cd[i];\n            }\n        }\n    }\n    return co;\n};\n// fixed length tree\nvar flt = new u8(288);\nfor(var i = 0; i < 144; ++i)flt[i] = 8;\nfor(var i = 144; i < 256; ++i)flt[i] = 9;\nfor(var i = 256; i < 280; ++i)flt[i] = 7;\nfor(var i = 280; i < 288; ++i)flt[i] = 8;\n// fixed distance tree\nvar fdt = new u8(32);\nfor(var i = 0; i < 32; ++i)fdt[i] = 5;\n// fixed length map\nvar flm = /*#__PURE__*/ hMap(flt, 9, 0), flrm = /*#__PURE__*/ hMap(flt, 9, 1);\n// fixed distance map\nvar fdm = /*#__PURE__*/ hMap(fdt, 5, 0), fdrm = /*#__PURE__*/ hMap(fdt, 5, 1);\n// find max of array\nvar max = function(a) {\n    var m = a[0];\n    for(var i = 1; i < a.length; ++i){\n        if (a[i] > m) m = a[i];\n    }\n    return m;\n};\n// read d, starting at bit p and mask with m\nvar bits = function(d, p, m) {\n    var o = p / 8 | 0;\n    return (d[o] | d[o + 1] << 8) >> (p & 7) & m;\n};\n// read d, starting at bit p continuing for at least 16 bits\nvar bits16 = function(d, p) {\n    var o = p / 8 | 0;\n    return (d[o] | d[o + 1] << 8 | d[o + 2] << 16) >> (p & 7);\n};\n// get end of byte\nvar shft = function(p) {\n    return (p + 7) / 8 | 0;\n};\n// typed array slice - allows garbage collector to free original reference,\n// while being more compatible than .slice\nvar slc = function(v, s, e) {\n    if (s == null || s < 0) s = 0;\n    if (e == null || e > v.length) e = v.length;\n    // can't use .constructor in case user-supplied\n    return new u8(v.subarray(s, e));\n};\n/**\n * Codes for errors generated within this library\n */ var FlateErrorCode = {\n    UnexpectedEOF: 0,\n    InvalidBlockType: 1,\n    InvalidLengthLiteral: 2,\n    InvalidDistance: 3,\n    StreamFinished: 4,\n    NoStreamHandler: 5,\n    InvalidHeader: 6,\n    NoCallback: 7,\n    InvalidUTF8: 8,\n    ExtraFieldTooLong: 9,\n    InvalidDate: 10,\n    FilenameTooLong: 11,\n    StreamFinishing: 12,\n    InvalidZipData: 13,\n    UnknownCompressionMethod: 14\n};\n// error codes\nvar ec = [\n    \"unexpected EOF\",\n    \"invalid block type\",\n    \"invalid length/literal\",\n    \"invalid distance\",\n    \"stream finished\",\n    \"no stream handler\",\n    ,\n    \"no callback\",\n    \"invalid UTF-8 data\",\n    \"extra field too long\",\n    \"date not in range 1980-2099\",\n    \"filename too long\",\n    \"stream finishing\",\n    \"invalid zip data\"\n];\n;\nvar err = function(ind, msg, nt) {\n    var e = new Error(msg || ec[ind]);\n    e.code = ind;\n    if (Error.captureStackTrace) Error.captureStackTrace(e, err);\n    if (!nt) throw e;\n    return e;\n};\n// expands raw DEFLATE data\nvar inflt = function(dat, st, buf, dict) {\n    // source length       dict length\n    var sl = dat.length, dl = dict ? dict.length : 0;\n    if (!sl || st.f && !st.l) return buf || new u8(0);\n    var noBuf = !buf;\n    // have to estimate size\n    var resize = noBuf || st.i != 2;\n    // no state\n    var noSt = st.i;\n    // Assumes roughly 33% compression ratio average\n    if (noBuf) buf = new u8(sl * 3);\n    // ensure buffer can fit at least l elements\n    var cbuf = function(l) {\n        var bl = buf.length;\n        // need to increase size to fit\n        if (l > bl) {\n            // Double or set to necessary, whichever is greater\n            var nbuf = new u8(Math.max(bl * 2, l));\n            nbuf.set(buf);\n            buf = nbuf;\n        }\n    };\n    //  last chunk         bitpos           bytes\n    var final = st.f || 0, pos = st.p || 0, bt = st.b || 0, lm = st.l, dm = st.d, lbt = st.m, dbt = st.n;\n    // total bits\n    var tbts = sl * 8;\n    do {\n        if (!lm) {\n            // BFINAL - this is only 1 when last chunk is next\n            final = bits(dat, pos, 1);\n            // type: 0 = no compression, 1 = fixed huffman, 2 = dynamic huffman\n            var type = bits(dat, pos + 1, 3);\n            pos += 3;\n            if (!type) {\n                // go to end of byte boundary\n                var s = shft(pos) + 4, l = dat[s - 4] | dat[s - 3] << 8, t = s + l;\n                if (t > sl) {\n                    if (noSt) err(0);\n                    break;\n                }\n                // ensure size\n                if (resize) cbuf(bt + l);\n                // Copy over uncompressed data\n                buf.set(dat.subarray(s, t), bt);\n                // Get new bitpos, update byte count\n                st.b = bt += l, st.p = pos = t * 8, st.f = final;\n                continue;\n            } else if (type == 1) lm = flrm, dm = fdrm, lbt = 9, dbt = 5;\n            else if (type == 2) {\n                //  literal                            lengths\n                var hLit = bits(dat, pos, 31) + 257, hcLen = bits(dat, pos + 10, 15) + 4;\n                var tl = hLit + bits(dat, pos + 5, 31) + 1;\n                pos += 14;\n                // length+distance tree\n                var ldt = new u8(tl);\n                // code length tree\n                var clt = new u8(19);\n                for(var i = 0; i < hcLen; ++i){\n                    // use index map to get real code\n                    clt[clim[i]] = bits(dat, pos + i * 3, 7);\n                }\n                pos += hcLen * 3;\n                // code lengths bits\n                var clb = max(clt), clbmsk = (1 << clb) - 1;\n                // code lengths map\n                var clm = hMap(clt, clb, 1);\n                for(var i = 0; i < tl;){\n                    var r = clm[bits(dat, pos, clbmsk)];\n                    // bits read\n                    pos += r & 15;\n                    // symbol\n                    var s = r >> 4;\n                    // code length to copy\n                    if (s < 16) {\n                        ldt[i++] = s;\n                    } else {\n                        //  copy   count\n                        var c = 0, n = 0;\n                        if (s == 16) n = 3 + bits(dat, pos, 3), pos += 2, c = ldt[i - 1];\n                        else if (s == 17) n = 3 + bits(dat, pos, 7), pos += 3;\n                        else if (s == 18) n = 11 + bits(dat, pos, 127), pos += 7;\n                        while(n--)ldt[i++] = c;\n                    }\n                }\n                //    length tree                 distance tree\n                var lt = ldt.subarray(0, hLit), dt = ldt.subarray(hLit);\n                // max length bits\n                lbt = max(lt);\n                // max dist bits\n                dbt = max(dt);\n                lm = hMap(lt, lbt, 1);\n                dm = hMap(dt, dbt, 1);\n            } else err(1);\n            if (pos > tbts) {\n                if (noSt) err(0);\n                break;\n            }\n        }\n        // Make sure the buffer can hold this + the largest possible addition\n        // Maximum chunk size (practically, theoretically infinite) is 2^17\n        if (resize) cbuf(bt + 131072);\n        var lms = (1 << lbt) - 1, dms = (1 << dbt) - 1;\n        var lpos = pos;\n        for(;; lpos = pos){\n            // bits read, code\n            var c = lm[bits16(dat, pos) & lms], sym = c >> 4;\n            pos += c & 15;\n            if (pos > tbts) {\n                if (noSt) err(0);\n                break;\n            }\n            if (!c) err(2);\n            if (sym < 256) buf[bt++] = sym;\n            else if (sym == 256) {\n                lpos = pos, lm = null;\n                break;\n            } else {\n                var add = sym - 254;\n                // no extra bits needed if less\n                if (sym > 264) {\n                    // index\n                    var i = sym - 257, b = fleb[i];\n                    add = bits(dat, pos, (1 << b) - 1) + fl[i];\n                    pos += b;\n                }\n                // dist\n                var d = dm[bits16(dat, pos) & dms], dsym = d >> 4;\n                if (!d) err(3);\n                pos += d & 15;\n                var dt = fd[dsym];\n                if (dsym > 3) {\n                    var b = fdeb[dsym];\n                    dt += bits16(dat, pos) & (1 << b) - 1, pos += b;\n                }\n                if (pos > tbts) {\n                    if (noSt) err(0);\n                    break;\n                }\n                if (resize) cbuf(bt + 131072);\n                var end = bt + add;\n                if (bt < dt) {\n                    var shift = dl - dt, dend = Math.min(dt, end);\n                    if (shift + bt < 0) err(3);\n                    for(; bt < dend; ++bt)buf[bt] = dict[shift + bt];\n                }\n                for(; bt < end; ++bt)buf[bt] = buf[bt - dt];\n            }\n        }\n        st.l = lm, st.p = lpos, st.b = bt, st.f = final;\n        if (lm) final = 1, st.m = lbt, st.d = dm, st.n = dbt;\n    }while (!final);\n    // don't reallocate for streams or user buffers\n    return bt != buf.length && noBuf ? slc(buf, 0, bt) : buf.subarray(0, bt);\n};\n// starting at p, write the minimum number of bits that can hold v to d\nvar wbits = function(d, p, v) {\n    v <<= p & 7;\n    var o = p / 8 | 0;\n    d[o] |= v;\n    d[o + 1] |= v >> 8;\n};\n// starting at p, write the minimum number of bits (>8) that can hold v to d\nvar wbits16 = function(d, p, v) {\n    v <<= p & 7;\n    var o = p / 8 | 0;\n    d[o] |= v;\n    d[o + 1] |= v >> 8;\n    d[o + 2] |= v >> 16;\n};\n// creates code lengths from a frequency table\nvar hTree = function(d, mb) {\n    // Need extra info to make a tree\n    var t = [];\n    for(var i = 0; i < d.length; ++i){\n        if (d[i]) t.push({\n            s: i,\n            f: d[i]\n        });\n    }\n    var s = t.length;\n    var t2 = t.slice();\n    if (!s) return {\n        t: et,\n        l: 0\n    };\n    if (s == 1) {\n        var v = new u8(t[0].s + 1);\n        v[t[0].s] = 1;\n        return {\n            t: v,\n            l: 1\n        };\n    }\n    t.sort(function(a, b) {\n        return a.f - b.f;\n    });\n    // after i2 reaches last ind, will be stopped\n    // freq must be greater than largest possible number of symbols\n    t.push({\n        s: -1,\n        f: 25001\n    });\n    var l = t[0], r = t[1], i0 = 0, i1 = 1, i2 = 2;\n    t[0] = {\n        s: -1,\n        f: l.f + r.f,\n        l: l,\n        r: r\n    };\n    // efficient algorithm from UZIP.js\n    // i0 is lookbehind, i2 is lookahead - after processing two low-freq\n    // symbols that combined have high freq, will start processing i2 (high-freq,\n    // non-composite) symbols instead\n    // see https://reddit.com/r/photopea/comments/ikekht/uzipjs_questions/\n    while(i1 != s - 1){\n        l = t[t[i0].f < t[i2].f ? i0++ : i2++];\n        r = t[i0 != i1 && t[i0].f < t[i2].f ? i0++ : i2++];\n        t[i1++] = {\n            s: -1,\n            f: l.f + r.f,\n            l: l,\n            r: r\n        };\n    }\n    var maxSym = t2[0].s;\n    for(var i = 1; i < s; ++i){\n        if (t2[i].s > maxSym) maxSym = t2[i].s;\n    }\n    // code lengths\n    var tr = new u16(maxSym + 1);\n    // max bits in tree\n    var mbt = ln(t[i1 - 1], tr, 0);\n    if (mbt > mb) {\n        // more algorithms from UZIP.js\n        // TODO: find out how this code works (debt)\n        //  ind    debt\n        var i = 0, dt = 0;\n        //    left            cost\n        var lft = mbt - mb, cst = 1 << lft;\n        t2.sort(function(a, b) {\n            return tr[b.s] - tr[a.s] || a.f - b.f;\n        });\n        for(; i < s; ++i){\n            var i2_1 = t2[i].s;\n            if (tr[i2_1] > mb) {\n                dt += cst - (1 << mbt - tr[i2_1]);\n                tr[i2_1] = mb;\n            } else break;\n        }\n        dt >>= lft;\n        while(dt > 0){\n            var i2_2 = t2[i].s;\n            if (tr[i2_2] < mb) dt -= 1 << mb - tr[i2_2]++ - 1;\n            else ++i;\n        }\n        for(; i >= 0 && dt; --i){\n            var i2_3 = t2[i].s;\n            if (tr[i2_3] == mb) {\n                --tr[i2_3];\n                ++dt;\n            }\n        }\n        mbt = mb;\n    }\n    return {\n        t: new u8(tr),\n        l: mbt\n    };\n};\n// get the max length and assign length codes\nvar ln = function(n, l, d) {\n    return n.s == -1 ? Math.max(ln(n.l, l, d + 1), ln(n.r, l, d + 1)) : l[n.s] = d;\n};\n// length codes generation\nvar lc = function(c) {\n    var s = c.length;\n    // Note that the semicolon was intentional\n    while(s && !c[--s]);\n    var cl = new u16(++s);\n    //  ind      num         streak\n    var cli = 0, cln = c[0], cls = 1;\n    var w = function(v) {\n        cl[cli++] = v;\n    };\n    for(var i = 1; i <= s; ++i){\n        if (c[i] == cln && i != s) ++cls;\n        else {\n            if (!cln && cls > 2) {\n                for(; cls > 138; cls -= 138)w(32754);\n                if (cls > 2) {\n                    w(cls > 10 ? cls - 11 << 5 | 28690 : cls - 3 << 5 | 12305);\n                    cls = 0;\n                }\n            } else if (cls > 3) {\n                w(cln), --cls;\n                for(; cls > 6; cls -= 6)w(8304);\n                if (cls > 2) w(cls - 3 << 5 | 8208), cls = 0;\n            }\n            while(cls--)w(cln);\n            cls = 1;\n            cln = c[i];\n        }\n    }\n    return {\n        c: cl.subarray(0, cli),\n        n: s\n    };\n};\n// calculate the length of output from tree, code lengths\nvar clen = function(cf, cl) {\n    var l = 0;\n    for(var i = 0; i < cl.length; ++i)l += cf[i] * cl[i];\n    return l;\n};\n// writes a fixed block\n// returns the new bit pos\nvar wfblk = function(out, pos, dat) {\n    // no need to write 00 as type: TypedArray defaults to 0\n    var s = dat.length;\n    var o = shft(pos + 2);\n    out[o] = s & 255;\n    out[o + 1] = s >> 8;\n    out[o + 2] = out[o] ^ 255;\n    out[o + 3] = out[o + 1] ^ 255;\n    for(var i = 0; i < s; ++i)out[o + i + 4] = dat[i];\n    return (o + 4 + s) * 8;\n};\n// writes a block\nvar wblk = function(dat, out, final, syms, lf, df, eb, li, bs, bl, p) {\n    wbits(out, p++, final);\n    ++lf[256];\n    var _a = hTree(lf, 15), dlt = _a.t, mlb = _a.l;\n    var _b = hTree(df, 15), ddt = _b.t, mdb = _b.l;\n    var _c = lc(dlt), lclt = _c.c, nlc = _c.n;\n    var _d = lc(ddt), lcdt = _d.c, ndc = _d.n;\n    var lcfreq = new u16(19);\n    for(var i = 0; i < lclt.length; ++i)++lcfreq[lclt[i] & 31];\n    for(var i = 0; i < lcdt.length; ++i)++lcfreq[lcdt[i] & 31];\n    var _e = hTree(lcfreq, 7), lct = _e.t, mlcb = _e.l;\n    var nlcc = 19;\n    for(; nlcc > 4 && !lct[clim[nlcc - 1]]; --nlcc);\n    var flen = bl + 5 << 3;\n    var ftlen = clen(lf, flt) + clen(df, fdt) + eb;\n    var dtlen = clen(lf, dlt) + clen(df, ddt) + eb + 14 + 3 * nlcc + clen(lcfreq, lct) + 2 * lcfreq[16] + 3 * lcfreq[17] + 7 * lcfreq[18];\n    if (bs >= 0 && flen <= ftlen && flen <= dtlen) return wfblk(out, p, dat.subarray(bs, bs + bl));\n    var lm, ll, dm, dl;\n    wbits(out, p, 1 + (dtlen < ftlen)), p += 2;\n    if (dtlen < ftlen) {\n        lm = hMap(dlt, mlb, 0), ll = dlt, dm = hMap(ddt, mdb, 0), dl = ddt;\n        var llm = hMap(lct, mlcb, 0);\n        wbits(out, p, nlc - 257);\n        wbits(out, p + 5, ndc - 1);\n        wbits(out, p + 10, nlcc - 4);\n        p += 14;\n        for(var i = 0; i < nlcc; ++i)wbits(out, p + 3 * i, lct[clim[i]]);\n        p += 3 * nlcc;\n        var lcts = [\n            lclt,\n            lcdt\n        ];\n        for(var it = 0; it < 2; ++it){\n            var clct = lcts[it];\n            for(var i = 0; i < clct.length; ++i){\n                var len = clct[i] & 31;\n                wbits(out, p, llm[len]), p += lct[len];\n                if (len > 15) wbits(out, p, clct[i] >> 5 & 127), p += clct[i] >> 12;\n            }\n        }\n    } else {\n        lm = flm, ll = flt, dm = fdm, dl = fdt;\n    }\n    for(var i = 0; i < li; ++i){\n        var sym = syms[i];\n        if (sym > 255) {\n            var len = sym >> 18 & 31;\n            wbits16(out, p, lm[len + 257]), p += ll[len + 257];\n            if (len > 7) wbits(out, p, sym >> 23 & 31), p += fleb[len];\n            var dst = sym & 31;\n            wbits16(out, p, dm[dst]), p += dl[dst];\n            if (dst > 3) wbits16(out, p, sym >> 5 & 8191), p += fdeb[dst];\n        } else {\n            wbits16(out, p, lm[sym]), p += ll[sym];\n        }\n    }\n    wbits16(out, p, lm[256]);\n    return p + ll[256];\n};\n// deflate options (nice << 13) | chain\nvar deo = /*#__PURE__*/ new i32([\n    65540,\n    131080,\n    131088,\n    131104,\n    262176,\n    1048704,\n    1048832,\n    2114560,\n    2117632\n]);\n// empty\nvar et = /*#__PURE__*/ new u8(0);\n// compresses data into a raw DEFLATE buffer\nvar dflt = function(dat, lvl, plvl, pre, post, st) {\n    var s = st.z || dat.length;\n    var o = new u8(pre + s + 5 * (1 + Math.ceil(s / 7000)) + post);\n    // writing to this writes to the output buffer\n    var w = o.subarray(pre, o.length - post);\n    var lst = st.l;\n    var pos = (st.r || 0) & 7;\n    if (lvl) {\n        if (pos) w[0] = st.r >> 3;\n        var opt = deo[lvl - 1];\n        var n = opt >> 13, c = opt & 8191;\n        var msk_1 = (1 << plvl) - 1;\n        //    prev 2-byte val map    curr 2-byte val map\n        var prev = st.p || new u16(32768), head = st.h || new u16(msk_1 + 1);\n        var bs1_1 = Math.ceil(plvl / 3), bs2_1 = 2 * bs1_1;\n        var hsh = function(i) {\n            return (dat[i] ^ dat[i + 1] << bs1_1 ^ dat[i + 2] << bs2_1) & msk_1;\n        };\n        // 24576 is an arbitrary number of maximum symbols per block\n        // 424 buffer for last block\n        var syms = new i32(25000);\n        // length/literal freq   distance freq\n        var lf = new u16(288), df = new u16(32);\n        //  l/lcnt  exbits  index          l/lind  waitdx          blkpos\n        var lc_1 = 0, eb = 0, i = st.i || 0, li = 0, wi = st.w || 0, bs = 0;\n        for(; i + 2 < s; ++i){\n            // hash value\n            var hv = hsh(i);\n            // index mod 32768    previous index mod\n            var imod = i & 32767, pimod = head[hv];\n            prev[imod] = pimod;\n            head[hv] = imod;\n            // We always should modify head and prev, but only add symbols if\n            // this data is not yet processed (\"wait\" for wait index)\n            if (wi <= i) {\n                // bytes remaining\n                var rem = s - i;\n                if ((lc_1 > 7000 || li > 24576) && (rem > 423 || !lst)) {\n                    pos = wblk(dat, w, 0, syms, lf, df, eb, li, bs, i - bs, pos);\n                    li = lc_1 = eb = 0, bs = i;\n                    for(var j = 0; j < 286; ++j)lf[j] = 0;\n                    for(var j = 0; j < 30; ++j)df[j] = 0;\n                }\n                //  len    dist   chain\n                var l = 2, d = 0, ch_1 = c, dif = imod - pimod & 32767;\n                if (rem > 2 && hv == hsh(i - dif)) {\n                    var maxn = Math.min(n, rem) - 1;\n                    var maxd = Math.min(32767, i);\n                    // max possible length\n                    // not capped at dif because decompressors implement \"rolling\" index population\n                    var ml = Math.min(258, rem);\n                    while(dif <= maxd && --ch_1 && imod != pimod){\n                        if (dat[i + l] == dat[i + l - dif]) {\n                            var nl = 0;\n                            for(; nl < ml && dat[i + nl] == dat[i + nl - dif]; ++nl);\n                            if (nl > l) {\n                                l = nl, d = dif;\n                                // break out early when we reach \"nice\" (we are satisfied enough)\n                                if (nl > maxn) break;\n                                // now, find the rarest 2-byte sequence within this\n                                // length of literals and search for that instead.\n                                // Much faster than just using the start\n                                var mmd = Math.min(dif, nl - 2);\n                                var md = 0;\n                                for(var j = 0; j < mmd; ++j){\n                                    var ti = i - dif + j & 32767;\n                                    var pti = prev[ti];\n                                    var cd = ti - pti & 32767;\n                                    if (cd > md) md = cd, pimod = ti;\n                                }\n                            }\n                        }\n                        // check the previous match\n                        imod = pimod, pimod = prev[imod];\n                        dif += imod - pimod & 32767;\n                    }\n                }\n                // d will be nonzero only when a match was found\n                if (d) {\n                    // store both dist and len data in one int32\n                    // Make sure this is recognized as a len/dist with 28th bit (2^28)\n                    syms[li++] = 268435456 | revfl[l] << 18 | revfd[d];\n                    var lin = revfl[l] & 31, din = revfd[d] & 31;\n                    eb += fleb[lin] + fdeb[din];\n                    ++lf[257 + lin];\n                    ++df[din];\n                    wi = i + l;\n                    ++lc_1;\n                } else {\n                    syms[li++] = dat[i];\n                    ++lf[dat[i]];\n                }\n            }\n        }\n        for(i = Math.max(i, wi); i < s; ++i){\n            syms[li++] = dat[i];\n            ++lf[dat[i]];\n        }\n        pos = wblk(dat, w, lst, syms, lf, df, eb, li, bs, i - bs, pos);\n        if (!lst) {\n            st.r = pos & 7 | w[pos / 8 | 0] << 3;\n            // shft(pos) now 1 less if pos & 7 != 0\n            pos -= 7;\n            st.h = head, st.p = prev, st.i = i, st.w = wi;\n        }\n    } else {\n        for(var i = st.w || 0; i < s + lst; i += 65535){\n            // end\n            var e = i + 65535;\n            if (e >= s) {\n                // write final block\n                w[pos / 8 | 0] = lst;\n                e = s;\n            }\n            pos = wfblk(w, pos + 1, dat.subarray(i, e));\n        }\n        st.i = s;\n    }\n    return slc(o, 0, pre + shft(pos) + post);\n};\n// CRC32 table\nvar crct = /*#__PURE__*/ function() {\n    var t = new Int32Array(256);\n    for(var i = 0; i < 256; ++i){\n        var c = i, k = 9;\n        while(--k)c = (c & 1 && -306674912) ^ c >>> 1;\n        t[i] = c;\n    }\n    return t;\n}();\n// CRC32\nvar crc = function() {\n    var c = -1;\n    return {\n        p: function(d) {\n            // closures have awful performance\n            var cr = c;\n            for(var i = 0; i < d.length; ++i)cr = crct[cr & 255 ^ d[i]] ^ cr >>> 8;\n            c = cr;\n        },\n        d: function() {\n            return ~c;\n        }\n    };\n};\n// Adler32\nvar adler = function() {\n    var a = 1, b = 0;\n    return {\n        p: function(d) {\n            // closures have awful performance\n            var n = a, m = b;\n            var l = d.length | 0;\n            for(var i = 0; i != l;){\n                var e = Math.min(i + 2655, l);\n                for(; i < e; ++i)m += n += d[i];\n                n = (n & 65535) + 15 * (n >> 16), m = (m & 65535) + 15 * (m >> 16);\n            }\n            a = n, b = m;\n        },\n        d: function() {\n            a %= 65521, b %= 65521;\n            return (a & 255) << 24 | (a & 0xFF00) << 8 | (b & 255) << 8 | b >> 8;\n        }\n    };\n};\n;\n// deflate with opts\nvar dopt = function(dat, opt, pre, post, st) {\n    if (!st) {\n        st = {\n            l: 1\n        };\n        if (opt.dictionary) {\n            var dict = opt.dictionary.subarray(-32768);\n            var newDat = new u8(dict.length + dat.length);\n            newDat.set(dict);\n            newDat.set(dat, dict.length);\n            dat = newDat;\n            st.w = dict.length;\n        }\n    }\n    return dflt(dat, opt.level == null ? 6 : opt.level, opt.mem == null ? st.l ? Math.ceil(Math.max(8, Math.min(13, Math.log(dat.length))) * 1.5) : 20 : 12 + opt.mem, pre, post, st);\n};\n// Walmart object spread\nvar mrg = function(a, b) {\n    var o = {};\n    for(var k in a)o[k] = a[k];\n    for(var k in b)o[k] = b[k];\n    return o;\n};\n// worker clone\n// This is possibly the craziest part of the entire codebase, despite how simple it may seem.\n// The only parameter to this function is a closure that returns an array of variables outside of the function scope.\n// We're going to try to figure out the variable names used in the closure as strings because that is crucial for workerization.\n// We will return an object mapping of true variable name to value (basically, the current scope as a JS object).\n// The reason we can't just use the original variable names is minifiers mangling the toplevel scope.\n// This took me three weeks to figure out how to do.\nvar wcln = function(fn, fnStr, td) {\n    var dt = fn();\n    var st = fn.toString();\n    var ks = st.slice(st.indexOf(\"[\") + 1, st.lastIndexOf(\"]\")).replace(/\\s+/g, \"\").split(\",\");\n    for(var i = 0; i < dt.length; ++i){\n        var v = dt[i], k = ks[i];\n        if (typeof v == \"function\") {\n            fnStr += \";\" + k + \"=\";\n            var st_1 = v.toString();\n            if (v.prototype) {\n                // for global objects\n                if (st_1.indexOf(\"[native code]\") != -1) {\n                    var spInd = st_1.indexOf(\" \", 8) + 1;\n                    fnStr += st_1.slice(spInd, st_1.indexOf(\"(\", spInd));\n                } else {\n                    fnStr += st_1;\n                    for(var t in v.prototype)fnStr += \";\" + k + \".prototype.\" + t + \"=\" + v.prototype[t].toString();\n                }\n            } else fnStr += st_1;\n        } else td[k] = v;\n    }\n    return fnStr;\n};\nvar ch = [];\n// clone bufs\nvar cbfs = function(v) {\n    var tl = [];\n    for(var k in v){\n        if (v[k].buffer) {\n            tl.push((v[k] = new v[k].constructor(v[k])).buffer);\n        }\n    }\n    return tl;\n};\n// use a worker to execute code\nvar wrkr = function(fns, init, id, cb) {\n    if (!ch[id]) {\n        var fnStr = \"\", td_1 = {}, m = fns.length - 1;\n        for(var i = 0; i < m; ++i)fnStr = wcln(fns[i], fnStr, td_1);\n        ch[id] = {\n            c: wcln(fns[m], fnStr, td_1),\n            e: td_1\n        };\n    }\n    var td = mrg({}, ch[id].e);\n    return wk(ch[id].c + \";onmessage=function(e){for(var k in e.data)self[k]=e.data[k];onmessage=\" + init.toString() + \"}\", id, td, cbfs(td), cb);\n};\n// base async inflate fn\nvar bInflt = function() {\n    return [\n        u8,\n        u16,\n        i32,\n        fleb,\n        fdeb,\n        clim,\n        fl,\n        fd,\n        flrm,\n        fdrm,\n        rev,\n        ec,\n        hMap,\n        max,\n        bits,\n        bits16,\n        shft,\n        slc,\n        err,\n        inflt,\n        inflateSync,\n        pbf,\n        gopt\n    ];\n};\nvar bDflt = function() {\n    return [\n        u8,\n        u16,\n        i32,\n        fleb,\n        fdeb,\n        clim,\n        revfl,\n        revfd,\n        flm,\n        flt,\n        fdm,\n        fdt,\n        rev,\n        deo,\n        et,\n        hMap,\n        wbits,\n        wbits16,\n        hTree,\n        ln,\n        lc,\n        clen,\n        wfblk,\n        wblk,\n        shft,\n        slc,\n        dflt,\n        dopt,\n        deflateSync,\n        pbf\n    ];\n};\n// gzip extra\nvar gze = function() {\n    return [\n        gzh,\n        gzhl,\n        wbytes,\n        crc,\n        crct\n    ];\n};\n// gunzip extra\nvar guze = function() {\n    return [\n        gzs,\n        gzl\n    ];\n};\n// zlib extra\nvar zle = function() {\n    return [\n        zlh,\n        wbytes,\n        adler\n    ];\n};\n// unzlib extra\nvar zule = function() {\n    return [\n        zls\n    ];\n};\n// post buf\nvar pbf = function(msg) {\n    return postMessage(msg, [\n        msg.buffer\n    ]);\n};\n// get opts\nvar gopt = function(o) {\n    return o && {\n        out: o.size && new u8(o.size),\n        dictionary: o.dictionary\n    };\n};\n// async helper\nvar cbify = function(dat, opts, fns, init, id, cb) {\n    var w = wrkr(fns, init, id, function(err, dat) {\n        w.terminate();\n        cb(err, dat);\n    });\n    w.postMessage([\n        dat,\n        opts\n    ], opts.consume ? [\n        dat.buffer\n    ] : []);\n    return function() {\n        w.terminate();\n    };\n};\n// auto stream\nvar astrm = function(strm) {\n    strm.ondata = function(dat, final) {\n        return postMessage([\n            dat,\n            final\n        ], [\n            dat.buffer\n        ]);\n    };\n    return function(ev) {\n        if (ev.data.length) {\n            strm.push(ev.data[0], ev.data[1]);\n            postMessage([\n                ev.data[0].length\n            ]);\n        } else strm.flush();\n    };\n};\n// async stream attach\nvar astrmify = function(fns, strm, opts, init, id, flush, ext) {\n    var t;\n    var w = wrkr(fns, init, id, function(err, dat) {\n        if (err) w.terminate(), strm.ondata.call(strm, err);\n        else if (!Array.isArray(dat)) ext(dat);\n        else if (dat.length == 1) {\n            strm.queuedSize -= dat[0];\n            if (strm.ondrain) strm.ondrain(dat[0]);\n        } else {\n            if (dat[1]) w.terminate();\n            strm.ondata.call(strm, err, dat[0], dat[1]);\n        }\n    });\n    w.postMessage(opts);\n    strm.queuedSize = 0;\n    strm.push = function(d, f) {\n        if (!strm.ondata) err(5);\n        if (t) strm.ondata(err(4, 0, 1), null, !!f);\n        strm.queuedSize += d.length;\n        w.postMessage([\n            d,\n            t = f\n        ], [\n            d.buffer\n        ]);\n    };\n    strm.terminate = function() {\n        w.terminate();\n    };\n    if (flush) {\n        strm.flush = function() {\n            w.postMessage([]);\n        };\n    }\n};\n// read 2 bytes\nvar b2 = function(d, b) {\n    return d[b] | d[b + 1] << 8;\n};\n// read 4 bytes\nvar b4 = function(d, b) {\n    return (d[b] | d[b + 1] << 8 | d[b + 2] << 16 | d[b + 3] << 24) >>> 0;\n};\nvar b8 = function(d, b) {\n    return b4(d, b) + b4(d, b + 4) * 4294967296;\n};\n// write bytes\nvar wbytes = function(d, b, v) {\n    for(; v; ++b)d[b] = v, v >>>= 8;\n};\n// gzip header\nvar gzh = function(c, o) {\n    var fn = o.filename;\n    c[0] = 31, c[1] = 139, c[2] = 8, c[8] = o.level < 2 ? 4 : o.level == 9 ? 2 : 0, c[9] = 3; // assume Unix\n    if (o.mtime != 0) wbytes(c, 4, Math.floor(new Date(o.mtime || Date.now()) / 1000));\n    if (fn) {\n        c[3] = 8;\n        for(var i = 0; i <= fn.length; ++i)c[i + 10] = fn.charCodeAt(i);\n    }\n};\n// gzip footer: -8 to -4 = CRC, -4 to -0 is length\n// gzip start\nvar gzs = function(d) {\n    if (d[0] != 31 || d[1] != 139 || d[2] != 8) err(6, \"invalid gzip data\");\n    var flg = d[3];\n    var st = 10;\n    if (flg & 4) st += (d[10] | d[11] << 8) + 2;\n    for(var zs = (flg >> 3 & 1) + (flg >> 4 & 1); zs > 0; zs -= !d[st++]);\n    return st + (flg & 2);\n};\n// gzip length\nvar gzl = function(d) {\n    var l = d.length;\n    return (d[l - 4] | d[l - 3] << 8 | d[l - 2] << 16 | d[l - 1] << 24) >>> 0;\n};\n// gzip header length\nvar gzhl = function(o) {\n    return 10 + (o.filename ? o.filename.length + 1 : 0);\n};\n// zlib header\nvar zlh = function(c, o) {\n    var lv = o.level, fl = lv == 0 ? 0 : lv < 6 ? 1 : lv == 9 ? 3 : 2;\n    c[0] = 120, c[1] = fl << 6 | (o.dictionary && 32);\n    c[1] |= 31 - (c[0] << 8 | c[1]) % 31;\n    if (o.dictionary) {\n        var h = adler();\n        h.p(o.dictionary);\n        wbytes(c, 2, h.d());\n    }\n};\n// zlib start\nvar zls = function(d, dict) {\n    if ((d[0] & 15) != 8 || d[0] >> 4 > 7 || (d[0] << 8 | d[1]) % 31) err(6, \"invalid zlib data\");\n    if ((d[1] >> 5 & 1) == +!dict) err(6, \"invalid zlib data: \" + (d[1] & 32 ? \"need\" : \"unexpected\") + \" dictionary\");\n    return (d[1] >> 3 & 4) + 2;\n};\nfunction StrmOpt(opts, cb) {\n    if (typeof opts == \"function\") cb = opts, opts = {};\n    this.ondata = cb;\n    return opts;\n}\n/**\n * Streaming DEFLATE compression\n */ var Deflate = /*#__PURE__*/ function() {\n    function Deflate(opts, cb) {\n        if (typeof opts == \"function\") cb = opts, opts = {};\n        this.ondata = cb;\n        this.o = opts || {};\n        this.s = {\n            l: 0,\n            i: 32768,\n            w: 32768,\n            z: 32768\n        };\n        // Buffer length must always be 0 mod 32768 for index calculations to be correct when modifying head and prev\n        // 98304 = 32768 (lookback) + 65536 (common chunk size)\n        this.b = new u8(98304);\n        if (this.o.dictionary) {\n            var dict = this.o.dictionary.subarray(-32768);\n            this.b.set(dict, 32768 - dict.length);\n            this.s.i = 32768 - dict.length;\n        }\n    }\n    Deflate.prototype.p = function(c, f) {\n        this.ondata(dopt(c, this.o, 0, 0, this.s), f);\n    };\n    /**\n     * Pushes a chunk to be deflated\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */ Deflate.prototype.push = function(chunk, final) {\n        if (!this.ondata) err(5);\n        if (this.s.l) err(4);\n        var endLen = chunk.length + this.s.z;\n        if (endLen > this.b.length) {\n            if (endLen > 2 * this.b.length - 32768) {\n                var newBuf = new u8(endLen & -32768);\n                newBuf.set(this.b.subarray(0, this.s.z));\n                this.b = newBuf;\n            }\n            var split = this.b.length - this.s.z;\n            this.b.set(chunk.subarray(0, split), this.s.z);\n            this.s.z = this.b.length;\n            this.p(this.b, false);\n            this.b.set(this.b.subarray(-32768));\n            this.b.set(chunk.subarray(split), 32768);\n            this.s.z = chunk.length - split + 32768;\n            this.s.i = 32766, this.s.w = 32768;\n        } else {\n            this.b.set(chunk, this.s.z);\n            this.s.z += chunk.length;\n        }\n        this.s.l = final & 1;\n        if (this.s.z > this.s.w + 8191 || final) {\n            this.p(this.b, final || false);\n            this.s.w = this.s.i, this.s.i -= 2;\n        }\n    };\n    /**\n     * Flushes buffered uncompressed data. Useful to immediately retrieve the\n     * deflated output for small inputs.\n     */ Deflate.prototype.flush = function() {\n        if (!this.ondata) err(5);\n        if (this.s.l) err(4);\n        this.p(this.b, false);\n        this.s.w = this.s.i, this.s.i -= 2;\n    };\n    return Deflate;\n}();\n\n/**\n * Asynchronous streaming DEFLATE compression\n */ var AsyncDeflate = /*#__PURE__*/ function() {\n    function AsyncDeflate(opts, cb) {\n        astrmify([\n            bDflt,\n            function() {\n                return [\n                    astrm,\n                    Deflate\n                ];\n            }\n        ], this, StrmOpt.call(this, opts, cb), function(ev) {\n            var strm = new Deflate(ev.data);\n            onmessage = astrm(strm);\n        }, 6, 1);\n    }\n    return AsyncDeflate;\n}();\n\nfunction deflate(data, opts, cb) {\n    if (!cb) cb = opts, opts = {};\n    if (typeof cb != \"function\") err(7);\n    return cbify(data, opts, [\n        bDflt\n    ], function(ev) {\n        return pbf(deflateSync(ev.data[0], ev.data[1]));\n    }, 0, cb);\n}\n/**\n * Compresses data with DEFLATE without any wrapper\n * @param data The data to compress\n * @param opts The compression options\n * @returns The deflated version of the data\n */ function deflateSync(data, opts) {\n    return dopt(data, opts || {}, 0, 0);\n}\n/**\n * Streaming DEFLATE decompression\n */ var Inflate = /*#__PURE__*/ function() {\n    function Inflate(opts, cb) {\n        // no StrmOpt here to avoid adding to workerizer\n        if (typeof opts == \"function\") cb = opts, opts = {};\n        this.ondata = cb;\n        var dict = opts && opts.dictionary && opts.dictionary.subarray(-32768);\n        this.s = {\n            i: 0,\n            b: dict ? dict.length : 0\n        };\n        this.o = new u8(32768);\n        this.p = new u8(0);\n        if (dict) this.o.set(dict);\n    }\n    Inflate.prototype.e = function(c) {\n        if (!this.ondata) err(5);\n        if (this.d) err(4);\n        if (!this.p.length) this.p = c;\n        else if (c.length) {\n            var n = new u8(this.p.length + c.length);\n            n.set(this.p), n.set(c, this.p.length), this.p = n;\n        }\n    };\n    Inflate.prototype.c = function(final) {\n        this.s.i = +(this.d = final || false);\n        var bts = this.s.b;\n        var dt = inflt(this.p, this.s, this.o);\n        this.ondata(slc(dt, bts, this.s.b), this.d);\n        this.o = slc(dt, this.s.b - 32768), this.s.b = this.o.length;\n        this.p = slc(this.p, this.s.p / 8 | 0), this.s.p &= 7;\n    };\n    /**\n     * Pushes a chunk to be inflated\n     * @param chunk The chunk to push\n     * @param final Whether this is the final chunk\n     */ Inflate.prototype.push = function(chunk, final) {\n        this.e(chunk), this.c(final);\n    };\n    return Inflate;\n}();\n\n/**\n * Asynchronous streaming DEFLATE decompression\n */ var AsyncInflate = /*#__PURE__*/ function() {\n    function AsyncInflate(opts, cb) {\n        astrmify([\n            bInflt,\n            function() {\n                return [\n                    astrm,\n                    Inflate\n                ];\n            }\n        ], this, StrmOpt.call(this, opts, cb), function(ev) {\n            var strm = new Inflate(ev.data);\n            onmessage = astrm(strm);\n        }, 7, 0);\n    }\n    return AsyncInflate;\n}();\n\nfunction inflate(data, opts, cb) {\n    if (!cb) cb = opts, opts = {};\n    if (typeof cb != \"function\") err(7);\n    return cbify(data, opts, [\n        bInflt\n    ], function(ev) {\n        return pbf(inflateSync(ev.data[0], gopt(ev.data[1])));\n    }, 1, cb);\n}\n/**\n * Expands DEFLATE data with no wrapper\n * @param data The data to decompress\n * @param opts The decompression options\n * @returns The decompressed version of the data\n */ function inflateSync(data, opts) {\n    return inflt(data, {\n        i: 2\n    }, opts && opts.out, opts && opts.dictionary);\n}\n// before you yell at me for not just using extends, my reason is that TS inheritance is hard to workerize.\n/**\n * Streaming GZIP compression\n */ var Gzip = /*#__PURE__*/ function() {\n    function Gzip(opts, cb) {\n        this.c = crc();\n        this.l = 0;\n        this.v = 1;\n        Deflate.call(this, opts, cb);\n    }\n    /**\n     * Pushes a chunk to be GZIPped\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */ Gzip.prototype.push = function(chunk, final) {\n        this.c.p(chunk);\n        this.l += chunk.length;\n        Deflate.prototype.push.call(this, chunk, final);\n    };\n    Gzip.prototype.p = function(c, f) {\n        var raw = dopt(c, this.o, this.v && gzhl(this.o), f && 8, this.s);\n        if (this.v) gzh(raw, this.o), this.v = 0;\n        if (f) wbytes(raw, raw.length - 8, this.c.d()), wbytes(raw, raw.length - 4, this.l);\n        this.ondata(raw, f);\n    };\n    /**\n     * Flushes buffered uncompressed data. Useful to immediately retrieve the\n     * GZIPped output for small inputs.\n     */ Gzip.prototype.flush = function() {\n        Deflate.prototype.flush.call(this);\n    };\n    return Gzip;\n}();\n\n/**\n * Asynchronous streaming GZIP compression\n */ var AsyncGzip = /*#__PURE__*/ function() {\n    function AsyncGzip(opts, cb) {\n        astrmify([\n            bDflt,\n            gze,\n            function() {\n                return [\n                    astrm,\n                    Deflate,\n                    Gzip\n                ];\n            }\n        ], this, StrmOpt.call(this, opts, cb), function(ev) {\n            var strm = new Gzip(ev.data);\n            onmessage = astrm(strm);\n        }, 8, 1);\n    }\n    return AsyncGzip;\n}();\n\nfunction gzip(data, opts, cb) {\n    if (!cb) cb = opts, opts = {};\n    if (typeof cb != \"function\") err(7);\n    return cbify(data, opts, [\n        bDflt,\n        gze,\n        function() {\n            return [\n                gzipSync\n            ];\n        }\n    ], function(ev) {\n        return pbf(gzipSync(ev.data[0], ev.data[1]));\n    }, 2, cb);\n}\n/**\n * Compresses data with GZIP\n * @param data The data to compress\n * @param opts The compression options\n * @returns The gzipped version of the data\n */ function gzipSync(data, opts) {\n    if (!opts) opts = {};\n    var c = crc(), l = data.length;\n    c.p(data);\n    var d = dopt(data, opts, gzhl(opts), 8), s = d.length;\n    return gzh(d, opts), wbytes(d, s - 8, c.d()), wbytes(d, s - 4, l), d;\n}\n/**\n * Streaming single or multi-member GZIP decompression\n */ var Gunzip = /*#__PURE__*/ function() {\n    function Gunzip(opts, cb) {\n        this.v = 1;\n        this.r = 0;\n        Inflate.call(this, opts, cb);\n    }\n    /**\n     * Pushes a chunk to be GUNZIPped\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */ Gunzip.prototype.push = function(chunk, final) {\n        Inflate.prototype.e.call(this, chunk);\n        this.r += chunk.length;\n        if (this.v) {\n            var p = this.p.subarray(this.v - 1);\n            var s = p.length > 3 ? gzs(p) : 4;\n            if (s > p.length) {\n                if (!final) return;\n            } else if (this.v > 1 && this.onmember) {\n                this.onmember(this.r - p.length);\n            }\n            this.p = p.subarray(s), this.v = 0;\n        }\n        // necessary to prevent TS from using the closure value\n        // This allows for workerization to function correctly\n        Inflate.prototype.c.call(this, final);\n        // process concatenated GZIP\n        if (this.s.f && !this.s.l && !final) {\n            this.v = shft(this.s.p) + 9;\n            this.s = {\n                i: 0\n            };\n            this.o = new u8(0);\n            this.push(new u8(0), final);\n        }\n    };\n    return Gunzip;\n}();\n\n/**\n * Asynchronous streaming single or multi-member GZIP decompression\n */ var AsyncGunzip = /*#__PURE__*/ function() {\n    function AsyncGunzip(opts, cb) {\n        var _this = this;\n        astrmify([\n            bInflt,\n            guze,\n            function() {\n                return [\n                    astrm,\n                    Inflate,\n                    Gunzip\n                ];\n            }\n        ], this, StrmOpt.call(this, opts, cb), function(ev) {\n            var strm = new Gunzip(ev.data);\n            strm.onmember = function(offset) {\n                return postMessage(offset);\n            };\n            onmessage = astrm(strm);\n        }, 9, 0, function(offset) {\n            return _this.onmember && _this.onmember(offset);\n        });\n    }\n    return AsyncGunzip;\n}();\n\nfunction gunzip(data, opts, cb) {\n    if (!cb) cb = opts, opts = {};\n    if (typeof cb != \"function\") err(7);\n    return cbify(data, opts, [\n        bInflt,\n        guze,\n        function() {\n            return [\n                gunzipSync\n            ];\n        }\n    ], function(ev) {\n        return pbf(gunzipSync(ev.data[0], ev.data[1]));\n    }, 3, cb);\n}\n/**\n * Expands GZIP data\n * @param data The data to decompress\n * @param opts The decompression options\n * @returns The decompressed version of the data\n */ function gunzipSync(data, opts) {\n    var st = gzs(data);\n    if (st + 8 > data.length) err(6, \"invalid gzip data\");\n    return inflt(data.subarray(st, -8), {\n        i: 2\n    }, opts && opts.out || new u8(gzl(data)), opts && opts.dictionary);\n}\n/**\n * Streaming Zlib compression\n */ var Zlib = /*#__PURE__*/ function() {\n    function Zlib(opts, cb) {\n        this.c = adler();\n        this.v = 1;\n        Deflate.call(this, opts, cb);\n    }\n    /**\n     * Pushes a chunk to be zlibbed\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */ Zlib.prototype.push = function(chunk, final) {\n        this.c.p(chunk);\n        Deflate.prototype.push.call(this, chunk, final);\n    };\n    Zlib.prototype.p = function(c, f) {\n        var raw = dopt(c, this.o, this.v && (this.o.dictionary ? 6 : 2), f && 4, this.s);\n        if (this.v) zlh(raw, this.o), this.v = 0;\n        if (f) wbytes(raw, raw.length - 4, this.c.d());\n        this.ondata(raw, f);\n    };\n    /**\n     * Flushes buffered uncompressed data. Useful to immediately retrieve the\n     * zlibbed output for small inputs.\n     */ Zlib.prototype.flush = function() {\n        Deflate.prototype.flush.call(this);\n    };\n    return Zlib;\n}();\n\n/**\n * Asynchronous streaming Zlib compression\n */ var AsyncZlib = /*#__PURE__*/ function() {\n    function AsyncZlib(opts, cb) {\n        astrmify([\n            bDflt,\n            zle,\n            function() {\n                return [\n                    astrm,\n                    Deflate,\n                    Zlib\n                ];\n            }\n        ], this, StrmOpt.call(this, opts, cb), function(ev) {\n            var strm = new Zlib(ev.data);\n            onmessage = astrm(strm);\n        }, 10, 1);\n    }\n    return AsyncZlib;\n}();\n\nfunction zlib(data, opts, cb) {\n    if (!cb) cb = opts, opts = {};\n    if (typeof cb != \"function\") err(7);\n    return cbify(data, opts, [\n        bDflt,\n        zle,\n        function() {\n            return [\n                zlibSync\n            ];\n        }\n    ], function(ev) {\n        return pbf(zlibSync(ev.data[0], ev.data[1]));\n    }, 4, cb);\n}\n/**\n * Compress data with Zlib\n * @param data The data to compress\n * @param opts The compression options\n * @returns The zlib-compressed version of the data\n */ function zlibSync(data, opts) {\n    if (!opts) opts = {};\n    var a = adler();\n    a.p(data);\n    var d = dopt(data, opts, opts.dictionary ? 6 : 2, 4);\n    return zlh(d, opts), wbytes(d, d.length - 4, a.d()), d;\n}\n/**\n * Streaming Zlib decompression\n */ var Unzlib = /*#__PURE__*/ function() {\n    function Unzlib(opts, cb) {\n        Inflate.call(this, opts, cb);\n        this.v = opts && opts.dictionary ? 2 : 1;\n    }\n    /**\n     * Pushes a chunk to be unzlibbed\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */ Unzlib.prototype.push = function(chunk, final) {\n        Inflate.prototype.e.call(this, chunk);\n        if (this.v) {\n            if (this.p.length < 6 && !final) return;\n            this.p = this.p.subarray(zls(this.p, this.v - 1)), this.v = 0;\n        }\n        if (final) {\n            if (this.p.length < 4) err(6, \"invalid zlib data\");\n            this.p = this.p.subarray(0, -4);\n        }\n        // necessary to prevent TS from using the closure value\n        // This allows for workerization to function correctly\n        Inflate.prototype.c.call(this, final);\n    };\n    return Unzlib;\n}();\n\n/**\n * Asynchronous streaming Zlib decompression\n */ var AsyncUnzlib = /*#__PURE__*/ function() {\n    function AsyncUnzlib(opts, cb) {\n        astrmify([\n            bInflt,\n            zule,\n            function() {\n                return [\n                    astrm,\n                    Inflate,\n                    Unzlib\n                ];\n            }\n        ], this, StrmOpt.call(this, opts, cb), function(ev) {\n            var strm = new Unzlib(ev.data);\n            onmessage = astrm(strm);\n        }, 11, 0);\n    }\n    return AsyncUnzlib;\n}();\n\nfunction unzlib(data, opts, cb) {\n    if (!cb) cb = opts, opts = {};\n    if (typeof cb != \"function\") err(7);\n    return cbify(data, opts, [\n        bInflt,\n        zule,\n        function() {\n            return [\n                unzlibSync\n            ];\n        }\n    ], function(ev) {\n        return pbf(unzlibSync(ev.data[0], gopt(ev.data[1])));\n    }, 5, cb);\n}\n/**\n * Expands Zlib data\n * @param data The data to decompress\n * @param opts The decompression options\n * @returns The decompressed version of the data\n */ function unzlibSync(data, opts) {\n    return inflt(data.subarray(zls(data, opts && opts.dictionary), -4), {\n        i: 2\n    }, opts && opts.out, opts && opts.dictionary);\n}\n// Default algorithm for compression (used because having a known output size allows faster decompression)\n\n\n/**\n * Streaming GZIP, Zlib, or raw DEFLATE decompression\n */ var Decompress = /*#__PURE__*/ function() {\n    function Decompress(opts, cb) {\n        this.o = StrmOpt.call(this, opts, cb) || {};\n        this.G = Gunzip;\n        this.I = Inflate;\n        this.Z = Unzlib;\n    }\n    // init substream\n    // overriden by AsyncDecompress\n    Decompress.prototype.i = function() {\n        var _this = this;\n        this.s.ondata = function(dat, final) {\n            _this.ondata(dat, final);\n        };\n    };\n    /**\n     * Pushes a chunk to be decompressed\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */ Decompress.prototype.push = function(chunk, final) {\n        if (!this.ondata) err(5);\n        if (!this.s) {\n            if (this.p && this.p.length) {\n                var n = new u8(this.p.length + chunk.length);\n                n.set(this.p), n.set(chunk, this.p.length);\n            } else this.p = chunk;\n            if (this.p.length > 2) {\n                this.s = this.p[0] == 31 && this.p[1] == 139 && this.p[2] == 8 ? new this.G(this.o) : (this.p[0] & 15) != 8 || this.p[0] >> 4 > 7 || (this.p[0] << 8 | this.p[1]) % 31 ? new this.I(this.o) : new this.Z(this.o);\n                this.i();\n                this.s.push(this.p, final);\n                this.p = null;\n            }\n        } else this.s.push(chunk, final);\n    };\n    return Decompress;\n}();\n\n/**\n * Asynchronous streaming GZIP, Zlib, or raw DEFLATE decompression\n */ var AsyncDecompress = /*#__PURE__*/ function() {\n    function AsyncDecompress(opts, cb) {\n        Decompress.call(this, opts, cb);\n        this.queuedSize = 0;\n        this.G = AsyncGunzip;\n        this.I = AsyncInflate;\n        this.Z = AsyncUnzlib;\n    }\n    AsyncDecompress.prototype.i = function() {\n        var _this = this;\n        this.s.ondata = function(err, dat, final) {\n            _this.ondata(err, dat, final);\n        };\n        this.s.ondrain = function(size) {\n            _this.queuedSize -= size;\n            if (_this.ondrain) _this.ondrain(size);\n        };\n    };\n    /**\n     * Pushes a chunk to be decompressed\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */ AsyncDecompress.prototype.push = function(chunk, final) {\n        this.queuedSize += chunk.length;\n        Decompress.prototype.push.call(this, chunk, final);\n    };\n    return AsyncDecompress;\n}();\n\nfunction decompress(data, opts, cb) {\n    if (!cb) cb = opts, opts = {};\n    if (typeof cb != \"function\") err(7);\n    return data[0] == 31 && data[1] == 139 && data[2] == 8 ? gunzip(data, opts, cb) : (data[0] & 15) != 8 || data[0] >> 4 > 7 || (data[0] << 8 | data[1]) % 31 ? inflate(data, opts, cb) : unzlib(data, opts, cb);\n}\n/**\n * Expands compressed GZIP, Zlib, or raw DEFLATE data, automatically detecting the format\n * @param data The data to decompress\n * @param opts The decompression options\n * @returns The decompressed version of the data\n */ function decompressSync(data, opts) {\n    return data[0] == 31 && data[1] == 139 && data[2] == 8 ? gunzipSync(data, opts) : (data[0] & 15) != 8 || data[0] >> 4 > 7 || (data[0] << 8 | data[1]) % 31 ? inflateSync(data, opts) : unzlibSync(data, opts);\n}\n// flatten a directory structure\nvar fltn = function(d, p, t, o) {\n    for(var k in d){\n        var val = d[k], n = p + k, op = o;\n        if (Array.isArray(val)) op = mrg(o, val[1]), val = val[0];\n        if (val instanceof u8) t[n] = [\n            val,\n            op\n        ];\n        else {\n            t[n += \"/\"] = [\n                new u8(0),\n                op\n            ];\n            fltn(val, n, t, o);\n        }\n    }\n};\n// text encoder\nvar te = typeof TextEncoder != \"undefined\" && /*#__PURE__*/ new TextEncoder();\n// text decoder\nvar td = typeof TextDecoder != \"undefined\" && /*#__PURE__*/ new TextDecoder();\n// text decoder stream\nvar tds = 0;\ntry {\n    td.decode(et, {\n        stream: true\n    });\n    tds = 1;\n} catch (e) {}\n// decode UTF8\nvar dutf8 = function(d) {\n    for(var r = \"\", i = 0;;){\n        var c = d[i++];\n        var eb = (c > 127) + (c > 223) + (c > 239);\n        if (i + eb > d.length) return {\n            s: r,\n            r: slc(d, i - 1)\n        };\n        if (!eb) r += String.fromCharCode(c);\n        else if (eb == 3) {\n            c = ((c & 15) << 18 | (d[i++] & 63) << 12 | (d[i++] & 63) << 6 | d[i++] & 63) - 65536, r += String.fromCharCode(55296 | c >> 10, 56320 | c & 1023);\n        } else if (eb & 1) r += String.fromCharCode((c & 31) << 6 | d[i++] & 63);\n        else r += String.fromCharCode((c & 15) << 12 | (d[i++] & 63) << 6 | d[i++] & 63);\n    }\n};\n/**\n * Streaming UTF-8 decoding\n */ var DecodeUTF8 = /*#__PURE__*/ function() {\n    /**\n     * Creates a UTF-8 decoding stream\n     * @param cb The callback to call whenever data is decoded\n     */ function DecodeUTF8(cb) {\n        this.ondata = cb;\n        if (tds) this.t = new TextDecoder();\n        else this.p = et;\n    }\n    /**\n     * Pushes a chunk to be decoded from UTF-8 binary\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */ DecodeUTF8.prototype.push = function(chunk, final) {\n        if (!this.ondata) err(5);\n        final = !!final;\n        if (this.t) {\n            this.ondata(this.t.decode(chunk, {\n                stream: true\n            }), final);\n            if (final) {\n                if (this.t.decode().length) err(8);\n                this.t = null;\n            }\n            return;\n        }\n        if (!this.p) err(4);\n        var dat = new u8(this.p.length + chunk.length);\n        dat.set(this.p);\n        dat.set(chunk, this.p.length);\n        var _a = dutf8(dat), s = _a.s, r = _a.r;\n        if (final) {\n            if (r.length) err(8);\n            this.p = null;\n        } else this.p = r;\n        this.ondata(s, final);\n    };\n    return DecodeUTF8;\n}();\n\n/**\n * Streaming UTF-8 encoding\n */ var EncodeUTF8 = /*#__PURE__*/ function() {\n    /**\n     * Creates a UTF-8 decoding stream\n     * @param cb The callback to call whenever data is encoded\n     */ function EncodeUTF8(cb) {\n        this.ondata = cb;\n    }\n    /**\n     * Pushes a chunk to be encoded to UTF-8\n     * @param chunk The string data to push\n     * @param final Whether this is the last chunk\n     */ EncodeUTF8.prototype.push = function(chunk, final) {\n        if (!this.ondata) err(5);\n        if (this.d) err(4);\n        this.ondata(strToU8(chunk), this.d = final || false);\n    };\n    return EncodeUTF8;\n}();\n\n/**\n * Converts a string into a Uint8Array for use with compression/decompression methods\n * @param str The string to encode\n * @param latin1 Whether or not to interpret the data as Latin-1. This should\n *               not need to be true unless decoding a binary string.\n * @returns The string encoded in UTF-8/Latin-1 binary\n */ function strToU8(str, latin1) {\n    if (latin1) {\n        var ar_1 = new u8(str.length);\n        for(var i = 0; i < str.length; ++i)ar_1[i] = str.charCodeAt(i);\n        return ar_1;\n    }\n    if (te) return te.encode(str);\n    var l = str.length;\n    var ar = new u8(str.length + (str.length >> 1));\n    var ai = 0;\n    var w = function(v) {\n        ar[ai++] = v;\n    };\n    for(var i = 0; i < l; ++i){\n        if (ai + 5 > ar.length) {\n            var n = new u8(ai + 8 + (l - i << 1));\n            n.set(ar);\n            ar = n;\n        }\n        var c = str.charCodeAt(i);\n        if (c < 128 || latin1) w(c);\n        else if (c < 2048) w(192 | c >> 6), w(128 | c & 63);\n        else if (c > 55295 && c < 57344) c = 65536 + (c & 1023 << 10) | str.charCodeAt(++i) & 1023, w(240 | c >> 18), w(128 | c >> 12 & 63), w(128 | c >> 6 & 63), w(128 | c & 63);\n        else w(224 | c >> 12), w(128 | c >> 6 & 63), w(128 | c & 63);\n    }\n    return slc(ar, 0, ai);\n}\n/**\n * Converts a Uint8Array to a string\n * @param dat The data to decode to string\n * @param latin1 Whether or not to interpret the data as Latin-1. This should\n *               not need to be true unless encoding to binary string.\n * @returns The original UTF-8/Latin-1 string\n */ function strFromU8(dat, latin1) {\n    if (latin1) {\n        var r = \"\";\n        for(var i = 0; i < dat.length; i += 16384)r += String.fromCharCode.apply(null, dat.subarray(i, i + 16384));\n        return r;\n    } else if (td) {\n        return td.decode(dat);\n    } else {\n        var _a = dutf8(dat), s = _a.s, r = _a.r;\n        if (r.length) err(8);\n        return s;\n    }\n}\n;\n// deflate bit flag\nvar dbf = function(l) {\n    return l == 1 ? 3 : l < 6 ? 2 : l == 9 ? 1 : 0;\n};\n// skip local zip header\nvar slzh = function(d, b) {\n    return b + 30 + b2(d, b + 26) + b2(d, b + 28);\n};\n// read zip header\nvar zh = function(d, b, z) {\n    var fnl = b2(d, b + 28), fn = strFromU8(d.subarray(b + 46, b + 46 + fnl), !(b2(d, b + 8) & 2048)), es = b + 46 + fnl, bs = b4(d, b + 20);\n    var _a = z && bs == 4294967295 ? z64e(d, es) : [\n        bs,\n        b4(d, b + 24),\n        b4(d, b + 42)\n    ], sc = _a[0], su = _a[1], off = _a[2];\n    return [\n        b2(d, b + 10),\n        sc,\n        su,\n        fn,\n        es + b2(d, b + 30) + b2(d, b + 32),\n        off\n    ];\n};\n// read zip64 extra field\nvar z64e = function(d, b) {\n    for(; b2(d, b) != 1; b += 4 + b2(d, b + 2));\n    return [\n        b8(d, b + 12),\n        b8(d, b + 4),\n        b8(d, b + 20)\n    ];\n};\n// extra field length\nvar exfl = function(ex) {\n    var le = 0;\n    if (ex) {\n        for(var k in ex){\n            var l = ex[k].length;\n            if (l > 65535) err(9);\n            le += l + 4;\n        }\n    }\n    return le;\n};\n// write zip header\nvar wzh = function(d, b, f, fn, u, c, ce, co) {\n    var fl = fn.length, ex = f.extra, col = co && co.length;\n    var exl = exfl(ex);\n    wbytes(d, b, ce != null ? 0x2014B50 : 0x4034B50), b += 4;\n    if (ce != null) d[b++] = 20, d[b++] = f.os;\n    d[b] = 20, b += 2; // spec compliance? what's that?\n    d[b++] = f.flag << 1 | (c < 0 && 8), d[b++] = u && 8;\n    d[b++] = f.compression & 255, d[b++] = f.compression >> 8;\n    var dt = new Date(f.mtime == null ? Date.now() : f.mtime), y = dt.getFullYear() - 1980;\n    if (y < 0 || y > 119) err(10);\n    wbytes(d, b, y << 25 | dt.getMonth() + 1 << 21 | dt.getDate() << 16 | dt.getHours() << 11 | dt.getMinutes() << 5 | dt.getSeconds() >> 1), b += 4;\n    if (c != -1) {\n        wbytes(d, b, f.crc);\n        wbytes(d, b + 4, c < 0 ? -c - 2 : c);\n        wbytes(d, b + 8, f.size);\n    }\n    wbytes(d, b + 12, fl);\n    wbytes(d, b + 14, exl), b += 16;\n    if (ce != null) {\n        wbytes(d, b, col);\n        wbytes(d, b + 6, f.attrs);\n        wbytes(d, b + 10, ce), b += 14;\n    }\n    d.set(fn, b);\n    b += fl;\n    if (exl) {\n        for(var k in ex){\n            var exf = ex[k], l = exf.length;\n            wbytes(d, b, +k);\n            wbytes(d, b + 2, l);\n            d.set(exf, b + 4), b += 4 + l;\n        }\n    }\n    if (col) d.set(co, b), b += col;\n    return b;\n};\n// write zip footer (end of central directory)\nvar wzf = function(o, b, c, d, e) {\n    wbytes(o, b, 0x6054B50); // skip disk\n    wbytes(o, b + 8, c);\n    wbytes(o, b + 10, c);\n    wbytes(o, b + 12, d);\n    wbytes(o, b + 16, e);\n};\n/**\n * A pass-through stream to keep data uncompressed in a ZIP archive.\n */ var ZipPassThrough = /*#__PURE__*/ function() {\n    /**\n     * Creates a pass-through stream that can be added to ZIP archives\n     * @param filename The filename to associate with this data stream\n     */ function ZipPassThrough(filename) {\n        this.filename = filename;\n        this.c = crc();\n        this.size = 0;\n        this.compression = 0;\n    }\n    /**\n     * Processes a chunk and pushes to the output stream. You can override this\n     * method in a subclass for custom behavior, but by default this passes\n     * the data through. You must call this.ondata(err, chunk, final) at some\n     * point in this method.\n     * @param chunk The chunk to process\n     * @param final Whether this is the last chunk\n     */ ZipPassThrough.prototype.process = function(chunk, final) {\n        this.ondata(null, chunk, final);\n    };\n    /**\n     * Pushes a chunk to be added. If you are subclassing this with a custom\n     * compression algorithm, note that you must push data from the source\n     * file only, pre-compression.\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */ ZipPassThrough.prototype.push = function(chunk, final) {\n        if (!this.ondata) err(5);\n        this.c.p(chunk);\n        this.size += chunk.length;\n        if (final) this.crc = this.c.d();\n        this.process(chunk, final || false);\n    };\n    return ZipPassThrough;\n}();\n\n// I don't extend because TypeScript extension adds 1kB of runtime bloat\n/**\n * Streaming DEFLATE compression for ZIP archives. Prefer using AsyncZipDeflate\n * for better performance\n */ var ZipDeflate = /*#__PURE__*/ function() {\n    /**\n     * Creates a DEFLATE stream that can be added to ZIP archives\n     * @param filename The filename to associate with this data stream\n     * @param opts The compression options\n     */ function ZipDeflate(filename, opts) {\n        var _this = this;\n        if (!opts) opts = {};\n        ZipPassThrough.call(this, filename);\n        this.d = new Deflate(opts, function(dat, final) {\n            _this.ondata(null, dat, final);\n        });\n        this.compression = 8;\n        this.flag = dbf(opts.level);\n    }\n    ZipDeflate.prototype.process = function(chunk, final) {\n        try {\n            this.d.push(chunk, final);\n        } catch (e) {\n            this.ondata(e, null, final);\n        }\n    };\n    /**\n     * Pushes a chunk to be deflated\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */ ZipDeflate.prototype.push = function(chunk, final) {\n        ZipPassThrough.prototype.push.call(this, chunk, final);\n    };\n    return ZipDeflate;\n}();\n\n/**\n * Asynchronous streaming DEFLATE compression for ZIP archives\n */ var AsyncZipDeflate = /*#__PURE__*/ function() {\n    /**\n     * Creates an asynchronous DEFLATE stream that can be added to ZIP archives\n     * @param filename The filename to associate with this data stream\n     * @param opts The compression options\n     */ function AsyncZipDeflate(filename, opts) {\n        var _this = this;\n        if (!opts) opts = {};\n        ZipPassThrough.call(this, filename);\n        this.d = new AsyncDeflate(opts, function(err, dat, final) {\n            _this.ondata(err, dat, final);\n        });\n        this.compression = 8;\n        this.flag = dbf(opts.level);\n        this.terminate = this.d.terminate;\n    }\n    AsyncZipDeflate.prototype.process = function(chunk, final) {\n        this.d.push(chunk, final);\n    };\n    /**\n     * Pushes a chunk to be deflated\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */ AsyncZipDeflate.prototype.push = function(chunk, final) {\n        ZipPassThrough.prototype.push.call(this, chunk, final);\n    };\n    return AsyncZipDeflate;\n}();\n\n// TODO: Better tree shaking\n/**\n * A zippable archive to which files can incrementally be added\n */ var Zip = /*#__PURE__*/ function() {\n    /**\n     * Creates an empty ZIP archive to which files can be added\n     * @param cb The callback to call whenever data for the generated ZIP archive\n     *           is available\n     */ function Zip(cb) {\n        this.ondata = cb;\n        this.u = [];\n        this.d = 1;\n    }\n    /**\n     * Adds a file to the ZIP archive\n     * @param file The file stream to add\n     */ Zip.prototype.add = function(file) {\n        var _this = this;\n        if (!this.ondata) err(5);\n        // finishing or finished\n        if (this.d & 2) this.ondata(err(4 + (this.d & 1) * 8, 0, 1), null, false);\n        else {\n            var f = strToU8(file.filename), fl_1 = f.length;\n            var com = file.comment, o = com && strToU8(com);\n            var u = fl_1 != file.filename.length || o && com.length != o.length;\n            var hl_1 = fl_1 + exfl(file.extra) + 30;\n            if (fl_1 > 65535) this.ondata(err(11, 0, 1), null, false);\n            var header = new u8(hl_1);\n            wzh(header, 0, file, f, u, -1);\n            var chks_1 = [\n                header\n            ];\n            var pAll_1 = function() {\n                for(var _i = 0, chks_2 = chks_1; _i < chks_2.length; _i++){\n                    var chk = chks_2[_i];\n                    _this.ondata(null, chk, false);\n                }\n                chks_1 = [];\n            };\n            var tr_1 = this.d;\n            this.d = 0;\n            var ind_1 = this.u.length;\n            var uf_1 = mrg(file, {\n                f: f,\n                u: u,\n                o: o,\n                t: function() {\n                    if (file.terminate) file.terminate();\n                },\n                r: function() {\n                    pAll_1();\n                    if (tr_1) {\n                        var nxt = _this.u[ind_1 + 1];\n                        if (nxt) nxt.r();\n                        else _this.d = 1;\n                    }\n                    tr_1 = 1;\n                }\n            });\n            var cl_1 = 0;\n            file.ondata = function(err, dat, final) {\n                if (err) {\n                    _this.ondata(err, dat, final);\n                    _this.terminate();\n                } else {\n                    cl_1 += dat.length;\n                    chks_1.push(dat);\n                    if (final) {\n                        var dd = new u8(16);\n                        wbytes(dd, 0, 0x8074B50);\n                        wbytes(dd, 4, file.crc);\n                        wbytes(dd, 8, cl_1);\n                        wbytes(dd, 12, file.size);\n                        chks_1.push(dd);\n                        uf_1.c = cl_1, uf_1.b = hl_1 + cl_1 + 16, uf_1.crc = file.crc, uf_1.size = file.size;\n                        if (tr_1) uf_1.r();\n                        tr_1 = 1;\n                    } else if (tr_1) pAll_1();\n                }\n            };\n            this.u.push(uf_1);\n        }\n    };\n    /**\n     * Ends the process of adding files and prepares to emit the final chunks.\n     * This *must* be called after adding all desired files for the resulting\n     * ZIP file to work properly.\n     */ Zip.prototype.end = function() {\n        var _this = this;\n        if (this.d & 2) {\n            this.ondata(err(4 + (this.d & 1) * 8, 0, 1), null, true);\n            return;\n        }\n        if (this.d) this.e();\n        else this.u.push({\n            r: function() {\n                if (!(_this.d & 1)) return;\n                _this.u.splice(-1, 1);\n                _this.e();\n            },\n            t: function() {}\n        });\n        this.d = 3;\n    };\n    Zip.prototype.e = function() {\n        var bt = 0, l = 0, tl = 0;\n        for(var _i = 0, _a = this.u; _i < _a.length; _i++){\n            var f = _a[_i];\n            tl += 46 + f.f.length + exfl(f.extra) + (f.o ? f.o.length : 0);\n        }\n        var out = new u8(tl + 22);\n        for(var _b = 0, _c = this.u; _b < _c.length; _b++){\n            var f = _c[_b];\n            wzh(out, bt, f, f.f, f.u, -f.c - 2, l, f.o);\n            bt += 46 + f.f.length + exfl(f.extra) + (f.o ? f.o.length : 0), l += f.b;\n        }\n        wzf(out, bt, this.u.length, tl, l);\n        this.ondata(null, out, true);\n        this.d = 2;\n    };\n    /**\n     * A method to terminate any internal workers used by the stream. Subsequent\n     * calls to add() will fail.\n     */ Zip.prototype.terminate = function() {\n        for(var _i = 0, _a = this.u; _i < _a.length; _i++){\n            var f = _a[_i];\n            f.t();\n        }\n        this.d = 2;\n    };\n    return Zip;\n}();\n\nfunction zip(data, opts, cb) {\n    if (!cb) cb = opts, opts = {};\n    if (typeof cb != \"function\") err(7);\n    var r = {};\n    fltn(data, \"\", r, opts);\n    var k = Object.keys(r);\n    var lft = k.length, o = 0, tot = 0;\n    var slft = lft, files = new Array(lft);\n    var term = [];\n    var tAll = function() {\n        for(var i = 0; i < term.length; ++i)term[i]();\n    };\n    var cbd = function(a, b) {\n        mt(function() {\n            cb(a, b);\n        });\n    };\n    mt(function() {\n        cbd = cb;\n    });\n    var cbf = function() {\n        var out = new u8(tot + 22), oe = o, cdl = tot - o;\n        tot = 0;\n        for(var i = 0; i < slft; ++i){\n            var f = files[i];\n            try {\n                var l = f.c.length;\n                wzh(out, tot, f, f.f, f.u, l);\n                var badd = 30 + f.f.length + exfl(f.extra);\n                var loc = tot + badd;\n                out.set(f.c, loc);\n                wzh(out, o, f, f.f, f.u, l, tot, f.m), o += 16 + badd + (f.m ? f.m.length : 0), tot = loc + l;\n            } catch (e) {\n                return cbd(e, null);\n            }\n        }\n        wzf(out, o, files.length, cdl, oe);\n        cbd(null, out);\n    };\n    if (!lft) cbf();\n    var _loop_1 = function(i) {\n        var fn = k[i];\n        var _a = r[fn], file = _a[0], p = _a[1];\n        var c = crc(), size = file.length;\n        c.p(file);\n        var f = strToU8(fn), s = f.length;\n        var com = p.comment, m = com && strToU8(com), ms = m && m.length;\n        var exl = exfl(p.extra);\n        var compression = p.level == 0 ? 0 : 8;\n        var cbl = function(e, d) {\n            if (e) {\n                tAll();\n                cbd(e, null);\n            } else {\n                var l = d.length;\n                files[i] = mrg(p, {\n                    size: size,\n                    crc: c.d(),\n                    c: d,\n                    f: f,\n                    m: m,\n                    u: s != fn.length || m && com.length != ms,\n                    compression: compression\n                });\n                o += 30 + s + exl + l;\n                tot += 76 + 2 * (s + exl) + (ms || 0) + l;\n                if (!--lft) cbf();\n            }\n        };\n        if (s > 65535) cbl(err(11, 0, 1), null);\n        if (!compression) cbl(null, file);\n        else if (size < 160000) {\n            try {\n                cbl(null, deflateSync(file, p));\n            } catch (e) {\n                cbl(e, null);\n            }\n        } else term.push(deflate(file, p, cbl));\n    };\n    // Cannot use lft because it can decrease\n    for(var i = 0; i < slft; ++i){\n        _loop_1(i);\n    }\n    return tAll;\n}\n/**\n * Synchronously creates a ZIP file. Prefer using `zip` for better performance\n * with more than one file.\n * @param data The directory structure for the ZIP archive\n * @param opts The main options, merged with per-file options\n * @returns The generated ZIP archive\n */ function zipSync(data, opts) {\n    if (!opts) opts = {};\n    var r = {};\n    var files = [];\n    fltn(data, \"\", r, opts);\n    var o = 0;\n    var tot = 0;\n    for(var fn in r){\n        var _a = r[fn], file = _a[0], p = _a[1];\n        var compression = p.level == 0 ? 0 : 8;\n        var f = strToU8(fn), s = f.length;\n        var com = p.comment, m = com && strToU8(com), ms = m && m.length;\n        var exl = exfl(p.extra);\n        if (s > 65535) err(11);\n        var d = compression ? deflateSync(file, p) : file, l = d.length;\n        var c = crc();\n        c.p(file);\n        files.push(mrg(p, {\n            size: file.length,\n            crc: c.d(),\n            c: d,\n            f: f,\n            m: m,\n            u: s != fn.length || m && com.length != ms,\n            o: o,\n            compression: compression\n        }));\n        o += 30 + s + exl + l;\n        tot += 76 + 2 * (s + exl) + (ms || 0) + l;\n    }\n    var out = new u8(tot + 22), oe = o, cdl = tot - o;\n    for(var i = 0; i < files.length; ++i){\n        var f = files[i];\n        wzh(out, f.o, f, f.f, f.u, f.c.length);\n        var badd = 30 + f.f.length + exfl(f.extra);\n        out.set(f.c, f.o + badd);\n        wzh(out, o, f, f.f, f.u, f.c.length, f.o, f.m), o += 16 + badd + (f.m ? f.m.length : 0);\n    }\n    wzf(out, o, files.length, cdl, oe);\n    return out;\n}\n/**\n * Streaming pass-through decompression for ZIP archives\n */ var UnzipPassThrough = /*#__PURE__*/ function() {\n    function UnzipPassThrough() {}\n    UnzipPassThrough.prototype.push = function(data, final) {\n        this.ondata(null, data, final);\n    };\n    UnzipPassThrough.compression = 0;\n    return UnzipPassThrough;\n}();\n\n/**\n * Streaming DEFLATE decompression for ZIP archives. Prefer AsyncZipInflate for\n * better performance.\n */ var UnzipInflate = /*#__PURE__*/ function() {\n    /**\n     * Creates a DEFLATE decompression that can be used in ZIP archives\n     */ function UnzipInflate() {\n        var _this = this;\n        this.i = new Inflate(function(dat, final) {\n            _this.ondata(null, dat, final);\n        });\n    }\n    UnzipInflate.prototype.push = function(data, final) {\n        try {\n            this.i.push(data, final);\n        } catch (e) {\n            this.ondata(e, null, final);\n        }\n    };\n    UnzipInflate.compression = 8;\n    return UnzipInflate;\n}();\n\n/**\n * Asynchronous streaming DEFLATE decompression for ZIP archives\n */ var AsyncUnzipInflate = /*#__PURE__*/ function() {\n    /**\n     * Creates a DEFLATE decompression that can be used in ZIP archives\n     */ function AsyncUnzipInflate(_, sz) {\n        var _this = this;\n        if (sz < 320000) {\n            this.i = new Inflate(function(dat, final) {\n                _this.ondata(null, dat, final);\n            });\n        } else {\n            this.i = new AsyncInflate(function(err, dat, final) {\n                _this.ondata(err, dat, final);\n            });\n            this.terminate = this.i.terminate;\n        }\n    }\n    AsyncUnzipInflate.prototype.push = function(data, final) {\n        if (this.i.terminate) data = slc(data, 0);\n        this.i.push(data, final);\n    };\n    AsyncUnzipInflate.compression = 8;\n    return AsyncUnzipInflate;\n}();\n\n/**\n * A ZIP archive decompression stream that emits files as they are discovered\n */ var Unzip = /*#__PURE__*/ function() {\n    /**\n     * Creates a ZIP decompression stream\n     * @param cb The callback to call whenever a file in the ZIP archive is found\n     */ function Unzip(cb) {\n        this.onfile = cb;\n        this.k = [];\n        this.o = {\n            0: UnzipPassThrough\n        };\n        this.p = et;\n    }\n    /**\n     * Pushes a chunk to be unzipped\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */ Unzip.prototype.push = function(chunk, final) {\n        var _this = this;\n        if (!this.onfile) err(5);\n        if (!this.p) err(4);\n        if (this.c > 0) {\n            var len = Math.min(this.c, chunk.length);\n            var toAdd = chunk.subarray(0, len);\n            this.c -= len;\n            if (this.d) this.d.push(toAdd, !this.c);\n            else this.k[0].push(toAdd);\n            chunk = chunk.subarray(len);\n            if (chunk.length) return this.push(chunk, final);\n        } else {\n            var f = 0, i = 0, is = void 0, buf = void 0;\n            if (!this.p.length) buf = chunk;\n            else if (!chunk.length) buf = this.p;\n            else {\n                buf = new u8(this.p.length + chunk.length);\n                buf.set(this.p), buf.set(chunk, this.p.length);\n            }\n            var l = buf.length, oc = this.c, add = oc && this.d;\n            var _loop_2 = function() {\n                var _a;\n                var sig = b4(buf, i);\n                if (sig == 0x4034B50) {\n                    f = 1, is = i;\n                    this_1.d = null;\n                    this_1.c = 0;\n                    var bf = b2(buf, i + 6), cmp_1 = b2(buf, i + 8), u = bf & 2048, dd = bf & 8, fnl = b2(buf, i + 26), es = b2(buf, i + 28);\n                    if (l > i + 30 + fnl + es) {\n                        var chks_3 = [];\n                        this_1.k.unshift(chks_3);\n                        f = 2;\n                        var sc_1 = b4(buf, i + 18), su_1 = b4(buf, i + 22);\n                        var fn_1 = strFromU8(buf.subarray(i + 30, i += 30 + fnl), !u);\n                        if (sc_1 == 4294967295) {\n                            _a = dd ? [\n                                -2\n                            ] : z64e(buf, i), sc_1 = _a[0], su_1 = _a[1];\n                        } else if (dd) sc_1 = -1;\n                        i += es;\n                        this_1.c = sc_1;\n                        var d_1;\n                        var file_1 = {\n                            name: fn_1,\n                            compression: cmp_1,\n                            start: function() {\n                                if (!file_1.ondata) err(5);\n                                if (!sc_1) file_1.ondata(null, et, true);\n                                else {\n                                    var ctr = _this.o[cmp_1];\n                                    if (!ctr) file_1.ondata(err(14, \"unknown compression type \" + cmp_1, 1), null, false);\n                                    d_1 = sc_1 < 0 ? new ctr(fn_1) : new ctr(fn_1, sc_1, su_1);\n                                    d_1.ondata = function(err, dat, final) {\n                                        file_1.ondata(err, dat, final);\n                                    };\n                                    for(var _i = 0, chks_4 = chks_3; _i < chks_4.length; _i++){\n                                        var dat = chks_4[_i];\n                                        d_1.push(dat, false);\n                                    }\n                                    if (_this.k[0] == chks_3 && _this.c) _this.d = d_1;\n                                    else d_1.push(et, true);\n                                }\n                            },\n                            terminate: function() {\n                                if (d_1 && d_1.terminate) d_1.terminate();\n                            }\n                        };\n                        if (sc_1 >= 0) file_1.size = sc_1, file_1.originalSize = su_1;\n                        this_1.onfile(file_1);\n                    }\n                    return \"break\";\n                } else if (oc) {\n                    if (sig == 0x8074B50) {\n                        is = i += 12 + (oc == -2 && 8), f = 3, this_1.c = 0;\n                        return \"break\";\n                    } else if (sig == 0x2014B50) {\n                        is = i -= 4, f = 3, this_1.c = 0;\n                        return \"break\";\n                    }\n                }\n            };\n            var this_1 = this;\n            for(; i < l - 4; ++i){\n                var state_1 = _loop_2();\n                if (state_1 === \"break\") break;\n            }\n            this.p = et;\n            if (oc < 0) {\n                var dat = f ? buf.subarray(0, is - 12 - (oc == -2 && 8) - (b4(buf, is - 16) == 0x8074B50 && 4)) : buf.subarray(0, i);\n                if (add) add.push(dat, !!f);\n                else this.k[+(f == 2)].push(dat);\n            }\n            if (f & 2) return this.push(buf.subarray(i), final);\n            this.p = buf.subarray(i);\n        }\n        if (final) {\n            if (this.c) err(13);\n            this.p = null;\n        }\n    };\n    /**\n     * Registers a decoder with the stream, allowing for files compressed with\n     * the compression type provided to be expanded correctly\n     * @param decoder The decoder constructor\n     */ Unzip.prototype.register = function(decoder) {\n        this.o[decoder.compression] = decoder;\n    };\n    return Unzip;\n}();\n\nvar mt = typeof queueMicrotask == \"function\" ? queueMicrotask : typeof setTimeout == \"function\" ? setTimeout : function(fn) {\n    fn();\n};\nfunction unzip(data, opts, cb) {\n    if (!cb) cb = opts, opts = {};\n    if (typeof cb != \"function\") err(7);\n    var term = [];\n    var tAll = function() {\n        for(var i = 0; i < term.length; ++i)term[i]();\n    };\n    var files = {};\n    var cbd = function(a, b) {\n        mt(function() {\n            cb(a, b);\n        });\n    };\n    mt(function() {\n        cbd = cb;\n    });\n    var e = data.length - 22;\n    for(; b4(data, e) != 0x6054B50; --e){\n        if (!e || data.length - e > 65558) {\n            cbd(err(13, 0, 1), null);\n            return tAll;\n        }\n    }\n    ;\n    var lft = b2(data, e + 8);\n    if (lft) {\n        var c = lft;\n        var o = b4(data, e + 16);\n        var z = o == 4294967295 || c == 65535;\n        if (z) {\n            var ze = b4(data, e - 12);\n            z = b4(data, ze) == 0x6064B50;\n            if (z) {\n                c = lft = b4(data, ze + 32);\n                o = b4(data, ze + 48);\n            }\n        }\n        var fltr = opts && opts.filter;\n        var _loop_3 = function(i) {\n            var _a = zh(data, o, z), c_1 = _a[0], sc = _a[1], su = _a[2], fn = _a[3], no = _a[4], off = _a[5], b = slzh(data, off);\n            o = no;\n            var cbl = function(e, d) {\n                if (e) {\n                    tAll();\n                    cbd(e, null);\n                } else {\n                    if (d) files[fn] = d;\n                    if (!--lft) cbd(null, files);\n                }\n            };\n            if (!fltr || fltr({\n                name: fn,\n                size: sc,\n                originalSize: su,\n                compression: c_1\n            })) {\n                if (!c_1) cbl(null, slc(data, b, b + sc));\n                else if (c_1 == 8) {\n                    var infl = data.subarray(b, b + sc);\n                    // Synchronously decompress under 512KB, or barely-compressed data\n                    if (su < 524288 || sc > 0.8 * su) {\n                        try {\n                            cbl(null, inflateSync(infl, {\n                                out: new u8(su)\n                            }));\n                        } catch (e) {\n                            cbl(e, null);\n                        }\n                    } else term.push(inflate(infl, {\n                        size: su\n                    }, cbl));\n                } else cbl(err(14, \"unknown compression type \" + c_1, 1), null);\n            } else cbl(null, null);\n        };\n        for(var i = 0; i < c; ++i){\n            _loop_3(i);\n        }\n    } else cbd(null, {});\n    return tAll;\n}\n/**\n * Synchronously decompresses a ZIP archive. Prefer using `unzip` for better\n * performance with more than one file.\n * @param data The raw compressed ZIP file\n * @param opts The ZIP extraction options\n * @returns The decompressed files\n */ function unzipSync(data, opts) {\n    var files = {};\n    var e = data.length - 22;\n    for(; b4(data, e) != 0x6054B50; --e){\n        if (!e || data.length - e > 65558) err(13);\n    }\n    ;\n    var c = b2(data, e + 8);\n    if (!c) return {};\n    var o = b4(data, e + 16);\n    var z = o == 4294967295 || c == 65535;\n    if (z) {\n        var ze = b4(data, e - 12);\n        z = b4(data, ze) == 0x6064B50;\n        if (z) {\n            c = b4(data, ze + 32);\n            o = b4(data, ze + 48);\n        }\n    }\n    var fltr = opts && opts.filter;\n    for(var i = 0; i < c; ++i){\n        var _a = zh(data, o, z), c_2 = _a[0], sc = _a[1], su = _a[2], fn = _a[3], no = _a[4], off = _a[5], b = slzh(data, off);\n        o = no;\n        if (!fltr || fltr({\n            name: fn,\n            size: sc,\n            originalSize: su,\n            compression: c_2\n        })) {\n            if (!c_2) files[fn] = slc(data, b, b + sc);\n            else if (c_2 == 8) files[fn] = inflateSync(data.subarray(b, b + sc), {\n                out: new u8(su)\n            });\n            else err(14, \"unknown compression type \" + c_2);\n        }\n    }\n    return files;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fflate/esm/index.mjs\n");

/***/ })

};
;