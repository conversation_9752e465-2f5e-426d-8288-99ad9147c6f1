"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/formik";
exports.ids = ["vendor-chunks/formik"];
exports.modules = {

/***/ "(ssr)/./node_modules/formik/dist/formik.esm.js":
/*!************************************************!*\
  !*** ./node_modules/formik/dist/formik.esm.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorMessage: () => (/* binding */ ErrorMessage),\n/* harmony export */   FastField: () => (/* binding */ FastField),\n/* harmony export */   Field: () => (/* binding */ Field),\n/* harmony export */   FieldArray: () => (/* binding */ FieldArray),\n/* harmony export */   Form: () => (/* binding */ Form),\n/* harmony export */   Formik: () => (/* binding */ Formik),\n/* harmony export */   FormikConsumer: () => (/* binding */ FormikConsumer),\n/* harmony export */   FormikContext: () => (/* binding */ FormikContext),\n/* harmony export */   FormikProvider: () => (/* binding */ FormikProvider),\n/* harmony export */   connect: () => (/* binding */ connect),\n/* harmony export */   getActiveElement: () => (/* binding */ getActiveElement),\n/* harmony export */   getIn: () => (/* binding */ getIn),\n/* harmony export */   insert: () => (/* binding */ insert),\n/* harmony export */   isEmptyArray: () => (/* binding */ isEmptyArray),\n/* harmony export */   isEmptyChildren: () => (/* binding */ isEmptyChildren),\n/* harmony export */   isFunction: () => (/* binding */ isFunction),\n/* harmony export */   isInputEvent: () => (/* binding */ isInputEvent),\n/* harmony export */   isInteger: () => (/* binding */ isInteger),\n/* harmony export */   isNaN: () => (/* binding */ isNaN$1),\n/* harmony export */   isObject: () => (/* binding */ isObject),\n/* harmony export */   isPromise: () => (/* binding */ isPromise),\n/* harmony export */   isString: () => (/* binding */ isString),\n/* harmony export */   move: () => (/* binding */ move),\n/* harmony export */   prepareDataForValidation: () => (/* binding */ prepareDataForValidation),\n/* harmony export */   replace: () => (/* binding */ replace),\n/* harmony export */   setIn: () => (/* binding */ setIn),\n/* harmony export */   setNestedObjectValues: () => (/* binding */ setNestedObjectValues),\n/* harmony export */   swap: () => (/* binding */ swap),\n/* harmony export */   useField: () => (/* binding */ useField),\n/* harmony export */   useFormik: () => (/* binding */ useFormik),\n/* harmony export */   useFormikContext: () => (/* binding */ useFormikContext),\n/* harmony export */   validateYupSchema: () => (/* binding */ validateYupSchema),\n/* harmony export */   withFormik: () => (/* binding */ withFormik),\n/* harmony export */   yupToFormErrors: () => (/* binding */ yupToFormErrors)\n/* harmony export */ });\n/* harmony import */ var deepmerge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! deepmerge */ \"(ssr)/./node_modules/deepmerge/dist/es.js\");\n/* harmony import */ var lodash_es_isPlainObject__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! lodash-es/isPlainObject */ \"(ssr)/./node_modules/lodash-es/isPlainObject.js\");\n/* harmony import */ var lodash_es_cloneDeep__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lodash-es/cloneDeep */ \"(ssr)/./node_modules/lodash-es/cloneDeep.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_fast_compare__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-fast-compare */ \"(ssr)/./node_modules/react-fast-compare/index.js\");\n/* harmony import */ var react_fast_compare__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_fast_compare__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var tiny_warning__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! tiny-warning */ \"(ssr)/./node_modules/tiny-warning/dist/tiny-warning.esm.js\");\n/* harmony import */ var lodash_es_clone__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash-es/clone */ \"(ssr)/./node_modules/lodash-es/clone.js\");\n/* harmony import */ var lodash_es_toPath__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash-es/toPath */ \"(ssr)/./node_modules/lodash-es/toPath.js\");\n/* harmony import */ var hoist_non_react_statics__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hoist-non-react-statics */ \"(ssr)/./node_modules/hoist-non-react-statics/dist/hoist-non-react-statics.cjs.js\");\n/* harmony import */ var hoist_non_react_statics__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(hoist_non_react_statics__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\n\n\n\n\n\nfunction _extends() {\n    _extends = Object.assign || function(target) {\n        for(var i = 1; i < arguments.length; i++){\n            var source = arguments[i];\n            for(var key in source){\n                if (Object.prototype.hasOwnProperty.call(source, key)) {\n                    target[key] = source[key];\n                }\n            }\n        }\n        return target;\n    };\n    return _extends.apply(this, arguments);\n}\nfunction _inheritsLoose(subClass, superClass) {\n    subClass.prototype = Object.create(superClass.prototype);\n    subClass.prototype.constructor = subClass;\n    subClass.__proto__ = superClass;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n    if (source == null) return {};\n    var target = {};\n    var sourceKeys = Object.keys(source);\n    var key, i;\n    for(i = 0; i < sourceKeys.length; i++){\n        key = sourceKeys[i];\n        if (excluded.indexOf(key) >= 0) continue;\n        target[key] = source[key];\n    }\n    return target;\n}\nfunction _assertThisInitialized(self) {\n    if (self === void 0) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n    }\n    return self;\n}\nvar FormikContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nFormikContext.displayName = \"FormikContext\";\nvar FormikProvider = FormikContext.Provider;\nvar FormikConsumer = FormikContext.Consumer;\nfunction useFormikContext() {\n    var formik = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(FormikContext);\n    !!!formik ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, \"Formik context is undefined, please verify you are calling useFormikContext() as child of a <Formik> component.\") : 0 : void 0;\n    return formik;\n}\n/** @private is the value an empty array? */ var isEmptyArray = function isEmptyArray(value) {\n    return Array.isArray(value) && value.length === 0;\n};\n/** @private is the given object a Function? */ var isFunction = function isFunction(obj) {\n    return typeof obj === \"function\";\n};\n/** @private is the given object an Object? */ var isObject = function isObject(obj) {\n    return obj !== null && typeof obj === \"object\";\n};\n/** @private is the given object an integer? */ var isInteger = function isInteger(obj) {\n    return String(Math.floor(Number(obj))) === obj;\n};\n/** @private is the given object a string? */ var isString = function isString(obj) {\n    return Object.prototype.toString.call(obj) === \"[object String]\";\n};\n/** @private is the given object a NaN? */ // eslint-disable-next-line no-self-compare\nvar isNaN$1 = function isNaN1(obj) {\n    return obj !== obj;\n};\n/** @private Does a React component have exactly 0 children? */ var isEmptyChildren = function isEmptyChildren(children) {\n    return react__WEBPACK_IMPORTED_MODULE_1__.Children.count(children) === 0;\n};\n/** @private is the given object/value a promise? */ var isPromise = function isPromise(value) {\n    return isObject(value) && isFunction(value.then);\n};\n/** @private is the given object/value a type of synthetic event? */ var isInputEvent = function isInputEvent(value) {\n    return value && isObject(value) && isObject(value.target);\n};\n/**\r\n * Same as document.activeElement but wraps in a try-catch block. In IE it is\r\n * not safe to call document.activeElement if there is nothing focused.\r\n *\r\n * The activeElement will be null only if the document or document body is not\r\n * yet defined.\r\n *\r\n * @param {?Document} doc Defaults to current document.\r\n * @return {Element | null}\r\n * @see https://github.com/facebook/fbjs/blob/master/packages/fbjs/src/core/dom/getActiveElement.js\r\n */ function getActiveElement(doc) {\n    doc = doc || (typeof document !== \"undefined\" ? document : undefined);\n    if (typeof doc === \"undefined\") {\n        return null;\n    }\n    try {\n        return doc.activeElement || doc.body;\n    } catch (e) {\n        return doc.body;\n    }\n}\n/**\r\n * Deeply get a value from an object via its path.\r\n */ function getIn(obj, key, def, p) {\n    if (p === void 0) {\n        p = 0;\n    }\n    var path = (0,lodash_es_toPath__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(key);\n    while(obj && p < path.length){\n        obj = obj[path[p++]];\n    } // check if path is not in the end\n    if (p !== path.length && !obj) {\n        return def;\n    }\n    return obj === undefined ? def : obj;\n}\n/**\r\n * Deeply set a value from in object via it's path. If the value at `path`\r\n * has changed, return a shallow copy of obj with `value` set at `path`.\r\n * If `value` has not changed, return the original `obj`.\r\n *\r\n * Existing objects / arrays along `path` are also shallow copied. Sibling\r\n * objects along path retain the same internal js reference. Since new\r\n * objects / arrays are only created along `path`, we can test if anything\r\n * changed in a nested structure by comparing the object's reference in\r\n * the old and new object, similar to how russian doll cache invalidation\r\n * works.\r\n *\r\n * In earlier versions of this function, which used cloneDeep, there were\r\n * issues whereby settings a nested value would mutate the parent\r\n * instead of creating a new object. `clone` avoids that bug making a\r\n * shallow copy of the objects along the update path\r\n * so no object is mutated in place.\r\n *\r\n * Before changing this function, please read through the following\r\n * discussions.\r\n *\r\n * @see https://github.com/developit/linkstate\r\n * @see https://github.com/jaredpalmer/formik/pull/123\r\n */ function setIn(obj, path, value) {\n    var res = (0,lodash_es_clone__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(obj); // this keeps inheritance when obj is a class\n    var resVal = res;\n    var i = 0;\n    var pathArray = (0,lodash_es_toPath__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(path);\n    for(; i < pathArray.length - 1; i++){\n        var currentPath = pathArray[i];\n        var currentObj = getIn(obj, pathArray.slice(0, i + 1));\n        if (currentObj && (isObject(currentObj) || Array.isArray(currentObj))) {\n            resVal = resVal[currentPath] = (0,lodash_es_clone__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(currentObj);\n        } else {\n            var nextPath = pathArray[i + 1];\n            resVal = resVal[currentPath] = isInteger(nextPath) && Number(nextPath) >= 0 ? [] : {};\n        }\n    } // Return original object if new value is the same as current\n    if ((i === 0 ? obj : resVal)[pathArray[i]] === value) {\n        return obj;\n    }\n    if (value === undefined) {\n        delete resVal[pathArray[i]];\n    } else {\n        resVal[pathArray[i]] = value;\n    } // If the path array has a single element, the loop did not run.\n    // Deleting on `resVal` had no effect in this scenario, so we delete on the result instead.\n    if (i === 0 && value === undefined) {\n        delete res[pathArray[i]];\n    }\n    return res;\n}\n/**\r\n * Recursively a set the same value for all keys and arrays nested object, cloning\r\n * @param object\r\n * @param value\r\n * @param visited\r\n * @param response\r\n */ function setNestedObjectValues(object, value, visited, response) {\n    if (visited === void 0) {\n        visited = new WeakMap();\n    }\n    if (response === void 0) {\n        response = {};\n    }\n    for(var _i = 0, _Object$keys = Object.keys(object); _i < _Object$keys.length; _i++){\n        var k = _Object$keys[_i];\n        var val = object[k];\n        if (isObject(val)) {\n            if (!visited.get(val)) {\n                visited.set(val, true); // In order to keep array values consistent for both dot path  and\n                // bracket syntax, we need to check if this is an array so that\n                // this will output  { friends: [true] } and not { friends: { \"0\": true } }\n                response[k] = Array.isArray(val) ? [] : {};\n                setNestedObjectValues(val, value, visited, response[k]);\n            }\n        } else {\n            response[k] = value;\n        }\n    }\n    return response;\n}\nfunction formikReducer(state, msg) {\n    switch(msg.type){\n        case \"SET_VALUES\":\n            return _extends({}, state, {\n                values: msg.payload\n            });\n        case \"SET_TOUCHED\":\n            return _extends({}, state, {\n                touched: msg.payload\n            });\n        case \"SET_ERRORS\":\n            if (react_fast_compare__WEBPACK_IMPORTED_MODULE_2___default()(state.errors, msg.payload)) {\n                return state;\n            }\n            return _extends({}, state, {\n                errors: msg.payload\n            });\n        case \"SET_STATUS\":\n            return _extends({}, state, {\n                status: msg.payload\n            });\n        case \"SET_ISSUBMITTING\":\n            return _extends({}, state, {\n                isSubmitting: msg.payload\n            });\n        case \"SET_ISVALIDATING\":\n            return _extends({}, state, {\n                isValidating: msg.payload\n            });\n        case \"SET_FIELD_VALUE\":\n            return _extends({}, state, {\n                values: setIn(state.values, msg.payload.field, msg.payload.value)\n            });\n        case \"SET_FIELD_TOUCHED\":\n            return _extends({}, state, {\n                touched: setIn(state.touched, msg.payload.field, msg.payload.value)\n            });\n        case \"SET_FIELD_ERROR\":\n            return _extends({}, state, {\n                errors: setIn(state.errors, msg.payload.field, msg.payload.value)\n            });\n        case \"RESET_FORM\":\n            return _extends({}, state, msg.payload);\n        case \"SET_FORMIK_STATE\":\n            return msg.payload(state);\n        case \"SUBMIT_ATTEMPT\":\n            return _extends({}, state, {\n                touched: setNestedObjectValues(state.values, true),\n                isSubmitting: true,\n                submitCount: state.submitCount + 1\n            });\n        case \"SUBMIT_FAILURE\":\n            return _extends({}, state, {\n                isSubmitting: false\n            });\n        case \"SUBMIT_SUCCESS\":\n            return _extends({}, state, {\n                isSubmitting: false\n            });\n        default:\n            return state;\n    }\n} // Initial empty states // objects\nvar emptyErrors = {};\nvar emptyTouched = {};\nfunction useFormik(_ref) {\n    var _ref$validateOnChange = _ref.validateOnChange, validateOnChange = _ref$validateOnChange === void 0 ? true : _ref$validateOnChange, _ref$validateOnBlur = _ref.validateOnBlur, validateOnBlur = _ref$validateOnBlur === void 0 ? true : _ref$validateOnBlur, _ref$validateOnMount = _ref.validateOnMount, validateOnMount = _ref$validateOnMount === void 0 ? false : _ref$validateOnMount, isInitialValid = _ref.isInitialValid, _ref$enableReinitiali = _ref.enableReinitialize, enableReinitialize = _ref$enableReinitiali === void 0 ? false : _ref$enableReinitiali, onSubmit = _ref.onSubmit, rest = _objectWithoutPropertiesLoose(_ref, [\n        \"validateOnChange\",\n        \"validateOnBlur\",\n        \"validateOnMount\",\n        \"isInitialValid\",\n        \"enableReinitialize\",\n        \"onSubmit\"\n    ]);\n    var props = _extends({\n        validateOnChange: validateOnChange,\n        validateOnBlur: validateOnBlur,\n        validateOnMount: validateOnMount,\n        onSubmit: onSubmit\n    }, rest);\n    var initialValues = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(props.initialValues);\n    var initialErrors = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(props.initialErrors || emptyErrors);\n    var initialTouched = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(props.initialTouched || emptyTouched);\n    var initialStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(props.initialStatus);\n    var isMounted = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    var fieldRegistry = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n    if (true) {\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n            !(typeof isInitialValid === \"undefined\") ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, \"isInitialValid has been deprecated and will be removed in future versions of Formik. Please use initialErrors or validateOnMount instead.\") : 0 : void 0; // eslint-disable-next-line\n        }, []);\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        isMounted.current = true;\n        return function() {\n            isMounted.current = false;\n        };\n    }, []);\n    var _React$useState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0), setIteration = _React$useState[1];\n    var stateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        values: (0,lodash_es_cloneDeep__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(props.initialValues),\n        errors: (0,lodash_es_cloneDeep__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(props.initialErrors) || emptyErrors,\n        touched: (0,lodash_es_cloneDeep__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(props.initialTouched) || emptyTouched,\n        status: (0,lodash_es_cloneDeep__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(props.initialStatus),\n        isSubmitting: false,\n        isValidating: false,\n        submitCount: 0\n    });\n    var state = stateRef.current;\n    var dispatch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function(action) {\n        var prev = stateRef.current;\n        stateRef.current = formikReducer(prev, action); // force rerender\n        if (prev !== stateRef.current) setIteration(function(x) {\n            return x + 1;\n        });\n    }, []);\n    var runValidateHandler = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function(values, field) {\n        return new Promise(function(resolve, reject) {\n            var maybePromisedErrors = props.validate(values, field);\n            if (maybePromisedErrors == null) {\n                // use loose null check here on purpose\n                resolve(emptyErrors);\n            } else if (isPromise(maybePromisedErrors)) {\n                maybePromisedErrors.then(function(errors) {\n                    resolve(errors || emptyErrors);\n                }, function(actualException) {\n                    if (true) {\n                        console.warn(\"Warning: An unhandled error was caught during validation in <Formik validate />\", actualException);\n                    }\n                    reject(actualException);\n                });\n            } else {\n                resolve(maybePromisedErrors);\n            }\n        });\n    }, [\n        props.validate\n    ]);\n    /**\r\n   * Run validation against a Yup schema and optionally run a function if successful\r\n   */ var runValidationSchema = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function(values, field) {\n        var validationSchema = props.validationSchema;\n        var schema = isFunction(validationSchema) ? validationSchema(field) : validationSchema;\n        var promise = field && schema.validateAt ? schema.validateAt(field, values) : validateYupSchema(values, schema);\n        return new Promise(function(resolve, reject) {\n            promise.then(function() {\n                resolve(emptyErrors);\n            }, function(err) {\n                // Yup will throw a validation error if validation fails. We catch those and\n                // resolve them into Formik errors. We can sniff if something is a Yup error\n                // by checking error.name.\n                // @see https://github.com/jquense/yup#validationerrorerrors-string--arraystring-value-any-path-string\n                if (err.name === \"ValidationError\") {\n                    resolve(yupToFormErrors(err));\n                } else {\n                    // We throw any other errors\n                    if (true) {\n                        console.warn(\"Warning: An unhandled error was caught during validation in <Formik validationSchema />\", err);\n                    }\n                    reject(err);\n                }\n            });\n        });\n    }, [\n        props.validationSchema\n    ]);\n    var runSingleFieldLevelValidation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function(field, value) {\n        return new Promise(function(resolve) {\n            return resolve(fieldRegistry.current[field].validate(value));\n        });\n    }, []);\n    var runFieldLevelValidations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function(values) {\n        var fieldKeysWithValidation = Object.keys(fieldRegistry.current).filter(function(f) {\n            return isFunction(fieldRegistry.current[f].validate);\n        }); // Construct an array with all of the field validation functions\n        var fieldValidations = fieldKeysWithValidation.length > 0 ? fieldKeysWithValidation.map(function(f) {\n            return runSingleFieldLevelValidation(f, getIn(values, f));\n        }) : [\n            Promise.resolve(\"DO_NOT_DELETE_YOU_WILL_BE_FIRED\")\n        ]; // use special case ;)\n        return Promise.all(fieldValidations).then(function(fieldErrorsList) {\n            return fieldErrorsList.reduce(function(prev, curr, index) {\n                if (curr === \"DO_NOT_DELETE_YOU_WILL_BE_FIRED\") {\n                    return prev;\n                }\n                if (curr) {\n                    prev = setIn(prev, fieldKeysWithValidation[index], curr);\n                }\n                return prev;\n            }, {});\n        });\n    }, [\n        runSingleFieldLevelValidation\n    ]); // Run all validations and return the result\n    var runAllValidations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function(values) {\n        return Promise.all([\n            runFieldLevelValidations(values),\n            props.validationSchema ? runValidationSchema(values) : {},\n            props.validate ? runValidateHandler(values) : {}\n        ]).then(function(_ref2) {\n            var fieldErrors = _ref2[0], schemaErrors = _ref2[1], validateErrors = _ref2[2];\n            var combinedErrors = deepmerge__WEBPACK_IMPORTED_MODULE_0__[\"default\"].all([\n                fieldErrors,\n                schemaErrors,\n                validateErrors\n            ], {\n                arrayMerge: arrayMerge\n            });\n            return combinedErrors;\n        });\n    }, [\n        props.validate,\n        props.validationSchema,\n        runFieldLevelValidations,\n        runValidateHandler,\n        runValidationSchema\n    ]); // Run all validations methods and update state accordingly\n    var validateFormWithHighPriority = useEventCallback(function(values) {\n        if (values === void 0) {\n            values = state.values;\n        }\n        dispatch({\n            type: \"SET_ISVALIDATING\",\n            payload: true\n        });\n        return runAllValidations(values).then(function(combinedErrors) {\n            if (!!isMounted.current) {\n                dispatch({\n                    type: \"SET_ISVALIDATING\",\n                    payload: false\n                });\n                dispatch({\n                    type: \"SET_ERRORS\",\n                    payload: combinedErrors\n                });\n            }\n            return combinedErrors;\n        });\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        if (validateOnMount && isMounted.current === true && react_fast_compare__WEBPACK_IMPORTED_MODULE_2___default()(initialValues.current, props.initialValues)) {\n            validateFormWithHighPriority(initialValues.current);\n        }\n    }, [\n        validateOnMount,\n        validateFormWithHighPriority\n    ]);\n    var resetForm = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function(nextState) {\n        var values = nextState && nextState.values ? nextState.values : initialValues.current;\n        var errors = nextState && nextState.errors ? nextState.errors : initialErrors.current ? initialErrors.current : props.initialErrors || {};\n        var touched = nextState && nextState.touched ? nextState.touched : initialTouched.current ? initialTouched.current : props.initialTouched || {};\n        var status = nextState && nextState.status ? nextState.status : initialStatus.current ? initialStatus.current : props.initialStatus;\n        initialValues.current = values;\n        initialErrors.current = errors;\n        initialTouched.current = touched;\n        initialStatus.current = status;\n        var dispatchFn = function dispatchFn() {\n            dispatch({\n                type: \"RESET_FORM\",\n                payload: {\n                    isSubmitting: !!nextState && !!nextState.isSubmitting,\n                    errors: errors,\n                    touched: touched,\n                    status: status,\n                    values: values,\n                    isValidating: !!nextState && !!nextState.isValidating,\n                    submitCount: !!nextState && !!nextState.submitCount && typeof nextState.submitCount === \"number\" ? nextState.submitCount : 0\n                }\n            });\n        };\n        if (props.onReset) {\n            var maybePromisedOnReset = props.onReset(state.values, imperativeMethods);\n            if (isPromise(maybePromisedOnReset)) {\n                maybePromisedOnReset.then(dispatchFn);\n            } else {\n                dispatchFn();\n            }\n        } else {\n            dispatchFn();\n        }\n    }, [\n        props.initialErrors,\n        props.initialStatus,\n        props.initialTouched,\n        props.onReset\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        if (isMounted.current === true && !react_fast_compare__WEBPACK_IMPORTED_MODULE_2___default()(initialValues.current, props.initialValues)) {\n            if (enableReinitialize) {\n                initialValues.current = props.initialValues;\n                resetForm();\n                if (validateOnMount) {\n                    validateFormWithHighPriority(initialValues.current);\n                }\n            }\n        }\n    }, [\n        enableReinitialize,\n        props.initialValues,\n        resetForm,\n        validateOnMount,\n        validateFormWithHighPriority\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        if (enableReinitialize && isMounted.current === true && !react_fast_compare__WEBPACK_IMPORTED_MODULE_2___default()(initialErrors.current, props.initialErrors)) {\n            initialErrors.current = props.initialErrors || emptyErrors;\n            dispatch({\n                type: \"SET_ERRORS\",\n                payload: props.initialErrors || emptyErrors\n            });\n        }\n    }, [\n        enableReinitialize,\n        props.initialErrors\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        if (enableReinitialize && isMounted.current === true && !react_fast_compare__WEBPACK_IMPORTED_MODULE_2___default()(initialTouched.current, props.initialTouched)) {\n            initialTouched.current = props.initialTouched || emptyTouched;\n            dispatch({\n                type: \"SET_TOUCHED\",\n                payload: props.initialTouched || emptyTouched\n            });\n        }\n    }, [\n        enableReinitialize,\n        props.initialTouched\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        if (enableReinitialize && isMounted.current === true && !react_fast_compare__WEBPACK_IMPORTED_MODULE_2___default()(initialStatus.current, props.initialStatus)) {\n            initialStatus.current = props.initialStatus;\n            dispatch({\n                type: \"SET_STATUS\",\n                payload: props.initialStatus\n            });\n        }\n    }, [\n        enableReinitialize,\n        props.initialStatus,\n        props.initialTouched\n    ]);\n    var validateField = useEventCallback(function(name) {\n        // This will efficiently validate a single field by avoiding state\n        // changes if the validation function is synchronous. It's different from\n        // what is called when using validateForm.\n        if (fieldRegistry.current[name] && isFunction(fieldRegistry.current[name].validate)) {\n            var value = getIn(state.values, name);\n            var maybePromise = fieldRegistry.current[name].validate(value);\n            if (isPromise(maybePromise)) {\n                // Only flip isValidating if the function is async.\n                dispatch({\n                    type: \"SET_ISVALIDATING\",\n                    payload: true\n                });\n                return maybePromise.then(function(x) {\n                    return x;\n                }).then(function(error) {\n                    dispatch({\n                        type: \"SET_FIELD_ERROR\",\n                        payload: {\n                            field: name,\n                            value: error\n                        }\n                    });\n                    dispatch({\n                        type: \"SET_ISVALIDATING\",\n                        payload: false\n                    });\n                });\n            } else {\n                dispatch({\n                    type: \"SET_FIELD_ERROR\",\n                    payload: {\n                        field: name,\n                        value: maybePromise\n                    }\n                });\n                return Promise.resolve(maybePromise);\n            }\n        } else if (props.validationSchema) {\n            dispatch({\n                type: \"SET_ISVALIDATING\",\n                payload: true\n            });\n            return runValidationSchema(state.values, name).then(function(x) {\n                return x;\n            }).then(function(error) {\n                dispatch({\n                    type: \"SET_FIELD_ERROR\",\n                    payload: {\n                        field: name,\n                        value: getIn(error, name)\n                    }\n                });\n                dispatch({\n                    type: \"SET_ISVALIDATING\",\n                    payload: false\n                });\n            });\n        }\n        return Promise.resolve();\n    });\n    var registerField = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function(name, _ref3) {\n        var validate = _ref3.validate;\n        fieldRegistry.current[name] = {\n            validate: validate\n        };\n    }, []);\n    var unregisterField = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function(name) {\n        delete fieldRegistry.current[name];\n    }, []);\n    var setTouched = useEventCallback(function(touched, shouldValidate) {\n        dispatch({\n            type: \"SET_TOUCHED\",\n            payload: touched\n        });\n        var willValidate = shouldValidate === undefined ? validateOnBlur : shouldValidate;\n        return willValidate ? validateFormWithHighPriority(state.values) : Promise.resolve();\n    });\n    var setErrors = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function(errors) {\n        dispatch({\n            type: \"SET_ERRORS\",\n            payload: errors\n        });\n    }, []);\n    var setValues = useEventCallback(function(values, shouldValidate) {\n        var resolvedValues = isFunction(values) ? values(state.values) : values;\n        dispatch({\n            type: \"SET_VALUES\",\n            payload: resolvedValues\n        });\n        var willValidate = shouldValidate === undefined ? validateOnChange : shouldValidate;\n        return willValidate ? validateFormWithHighPriority(resolvedValues) : Promise.resolve();\n    });\n    var setFieldError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function(field, value) {\n        dispatch({\n            type: \"SET_FIELD_ERROR\",\n            payload: {\n                field: field,\n                value: value\n            }\n        });\n    }, []);\n    var setFieldValue = useEventCallback(function(field, value, shouldValidate) {\n        dispatch({\n            type: \"SET_FIELD_VALUE\",\n            payload: {\n                field: field,\n                value: value\n            }\n        });\n        var willValidate = shouldValidate === undefined ? validateOnChange : shouldValidate;\n        return willValidate ? validateFormWithHighPriority(setIn(state.values, field, value)) : Promise.resolve();\n    });\n    var executeChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function(eventOrTextValue, maybePath) {\n        // By default, assume that the first argument is a string. This allows us to use\n        // handleChange with React Native and React Native Web's onChangeText prop which\n        // provides just the value of the input.\n        var field = maybePath;\n        var val = eventOrTextValue;\n        var parsed; // If the first argument is not a string though, it has to be a synthetic React Event (or a fake one),\n        // so we handle like we would a normal HTML change event.\n        if (!isString(eventOrTextValue)) {\n            // If we can, persist the event\n            // @see https://reactjs.org/docs/events.html#event-pooling\n            if (eventOrTextValue.persist) {\n                eventOrTextValue.persist();\n            }\n            var target = eventOrTextValue.target ? eventOrTextValue.target : eventOrTextValue.currentTarget;\n            var type = target.type, name = target.name, id = target.id, value = target.value, checked = target.checked, outerHTML = target.outerHTML, options = target.options, multiple = target.multiple;\n            field = maybePath ? maybePath : name ? name : id;\n            if (!field && \"development\" !== \"production\") {\n                warnAboutMissingIdentifier({\n                    htmlContent: outerHTML,\n                    documentationAnchorLink: \"handlechange-e-reactchangeeventany--void\",\n                    handlerName: \"handleChange\"\n                });\n            }\n            val = /number|range/.test(type) ? (parsed = parseFloat(value), isNaN(parsed) ? \"\" : parsed) : /checkbox/.test(type) // checkboxes\n             ? getValueForCheckbox(getIn(state.values, field), checked, value) : options && multiple // <select multiple>\n             ? getSelectedValues(options) : value;\n        }\n        if (field) {\n            // Set form fields by name\n            setFieldValue(field, val);\n        }\n    }, [\n        setFieldValue,\n        state.values\n    ]);\n    var handleChange = useEventCallback(function(eventOrPath) {\n        if (isString(eventOrPath)) {\n            return function(event) {\n                return executeChange(event, eventOrPath);\n            };\n        } else {\n            executeChange(eventOrPath);\n        }\n    });\n    var setFieldTouched = useEventCallback(function(field, touched, shouldValidate) {\n        if (touched === void 0) {\n            touched = true;\n        }\n        dispatch({\n            type: \"SET_FIELD_TOUCHED\",\n            payload: {\n                field: field,\n                value: touched\n            }\n        });\n        var willValidate = shouldValidate === undefined ? validateOnBlur : shouldValidate;\n        return willValidate ? validateFormWithHighPriority(state.values) : Promise.resolve();\n    });\n    var executeBlur = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function(e, path) {\n        if (e.persist) {\n            e.persist();\n        }\n        var _e$target = e.target, name = _e$target.name, id = _e$target.id, outerHTML = _e$target.outerHTML;\n        var field = path ? path : name ? name : id;\n        if (!field && \"development\" !== \"production\") {\n            warnAboutMissingIdentifier({\n                htmlContent: outerHTML,\n                documentationAnchorLink: \"handleblur-e-any--void\",\n                handlerName: \"handleBlur\"\n            });\n        }\n        setFieldTouched(field, true);\n    }, [\n        setFieldTouched\n    ]);\n    var handleBlur = useEventCallback(function(eventOrString) {\n        if (isString(eventOrString)) {\n            return function(event) {\n                return executeBlur(event, eventOrString);\n            };\n        } else {\n            executeBlur(eventOrString);\n        }\n    });\n    var setFormikState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function(stateOrCb) {\n        if (isFunction(stateOrCb)) {\n            dispatch({\n                type: \"SET_FORMIK_STATE\",\n                payload: stateOrCb\n            });\n        } else {\n            dispatch({\n                type: \"SET_FORMIK_STATE\",\n                payload: function payload() {\n                    return stateOrCb;\n                }\n            });\n        }\n    }, []);\n    var setStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function(status) {\n        dispatch({\n            type: \"SET_STATUS\",\n            payload: status\n        });\n    }, []);\n    var setSubmitting = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function(isSubmitting) {\n        dispatch({\n            type: \"SET_ISSUBMITTING\",\n            payload: isSubmitting\n        });\n    }, []);\n    var submitForm = useEventCallback(function() {\n        dispatch({\n            type: \"SUBMIT_ATTEMPT\"\n        });\n        return validateFormWithHighPriority().then(function(combinedErrors) {\n            // In case an error was thrown and passed to the resolved Promise,\n            // `combinedErrors` can be an instance of an Error. We need to check\n            // that and abort the submit.\n            // If we don't do that, calling `Object.keys(new Error())` yields an\n            // empty array, which causes the validation to pass and the form\n            // to be submitted.\n            var isInstanceOfError = combinedErrors instanceof Error;\n            var isActuallyValid = !isInstanceOfError && Object.keys(combinedErrors).length === 0;\n            if (isActuallyValid) {\n                // Proceed with submit...\n                //\n                // To respect sync submit fns, we can't simply wrap executeSubmit in a promise and\n                // _always_ dispatch SUBMIT_SUCCESS because isSubmitting would then always be false.\n                // This would be fine in simple cases, but make it impossible to disable submit\n                // buttons where people use callbacks or promises as side effects (which is basically\n                // all of v1 Formik code). Instead, recall that we are inside of a promise chain already,\n                //  so we can try/catch executeSubmit(), if it returns undefined, then just bail.\n                // If there are errors, throw em. Otherwise, wrap executeSubmit in a promise and handle\n                // cleanup of isSubmitting on behalf of the consumer.\n                var promiseOrUndefined;\n                try {\n                    promiseOrUndefined = executeSubmit(); // Bail if it's sync, consumer is responsible for cleaning up\n                    // via setSubmitting(false)\n                    if (promiseOrUndefined === undefined) {\n                        return;\n                    }\n                } catch (error) {\n                    throw error;\n                }\n                return Promise.resolve(promiseOrUndefined).then(function(result) {\n                    if (!!isMounted.current) {\n                        dispatch({\n                            type: \"SUBMIT_SUCCESS\"\n                        });\n                    }\n                    return result;\n                })[\"catch\"](function(_errors) {\n                    if (!!isMounted.current) {\n                        dispatch({\n                            type: \"SUBMIT_FAILURE\"\n                        }); // This is a legit error rejected by the onSubmit fn\n                        // so we don't want to break the promise chain\n                        throw _errors;\n                    }\n                });\n            } else if (!!isMounted.current) {\n                // ^^^ Make sure Formik is still mounted before updating state\n                dispatch({\n                    type: \"SUBMIT_FAILURE\"\n                }); // throw combinedErrors;\n                if (isInstanceOfError) {\n                    throw combinedErrors;\n                }\n            }\n            return;\n        });\n    });\n    var handleSubmit = useEventCallback(function(e) {\n        if (e && e.preventDefault && isFunction(e.preventDefault)) {\n            e.preventDefault();\n        }\n        if (e && e.stopPropagation && isFunction(e.stopPropagation)) {\n            e.stopPropagation();\n        } // Warn if form submission is triggered by a <button> without a\n        // specified `type` attribute during development. This mitigates\n        // a common gotcha in forms with both reset and submit buttons,\n        // where the dev forgets to add type=\"button\" to the reset button.\n        if ( true && typeof document !== \"undefined\") {\n            // Safely get the active element (works with IE)\n            var activeElement = getActiveElement();\n            if (activeElement !== null && activeElement instanceof HTMLButtonElement) {\n                !(activeElement.attributes && activeElement.attributes.getNamedItem(\"type\")) ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, 'You submitted a Formik form using a button with an unspecified `type` attribute.  Most browsers default button elements to `type=\"submit\"`. If this is not a submit button, please add `type=\"button\"`.') : 0 : void 0;\n            }\n        }\n        submitForm()[\"catch\"](function(reason) {\n            console.warn(\"Warning: An unhandled error was caught from submitForm()\", reason);\n        });\n    });\n    var imperativeMethods = {\n        resetForm: resetForm,\n        validateForm: validateFormWithHighPriority,\n        validateField: validateField,\n        setErrors: setErrors,\n        setFieldError: setFieldError,\n        setFieldTouched: setFieldTouched,\n        setFieldValue: setFieldValue,\n        setStatus: setStatus,\n        setSubmitting: setSubmitting,\n        setTouched: setTouched,\n        setValues: setValues,\n        setFormikState: setFormikState,\n        submitForm: submitForm\n    };\n    var executeSubmit = useEventCallback(function() {\n        return onSubmit(state.values, imperativeMethods);\n    });\n    var handleReset = useEventCallback(function(e) {\n        if (e && e.preventDefault && isFunction(e.preventDefault)) {\n            e.preventDefault();\n        }\n        if (e && e.stopPropagation && isFunction(e.stopPropagation)) {\n            e.stopPropagation();\n        }\n        resetForm();\n    });\n    var getFieldMeta = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function(name) {\n        return {\n            value: getIn(state.values, name),\n            error: getIn(state.errors, name),\n            touched: !!getIn(state.touched, name),\n            initialValue: getIn(initialValues.current, name),\n            initialTouched: !!getIn(initialTouched.current, name),\n            initialError: getIn(initialErrors.current, name)\n        };\n    }, [\n        state.errors,\n        state.touched,\n        state.values\n    ]);\n    var getFieldHelpers = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function(name) {\n        return {\n            setValue: function setValue(value, shouldValidate) {\n                return setFieldValue(name, value, shouldValidate);\n            },\n            setTouched: function setTouched(value, shouldValidate) {\n                return setFieldTouched(name, value, shouldValidate);\n            },\n            setError: function setError(value) {\n                return setFieldError(name, value);\n            }\n        };\n    }, [\n        setFieldValue,\n        setFieldTouched,\n        setFieldError\n    ]);\n    var getFieldProps = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function(nameOrOptions) {\n        var isAnObject = isObject(nameOrOptions);\n        var name = isAnObject ? nameOrOptions.name : nameOrOptions;\n        var valueState = getIn(state.values, name);\n        var field = {\n            name: name,\n            value: valueState,\n            onChange: handleChange,\n            onBlur: handleBlur\n        };\n        if (isAnObject) {\n            var type = nameOrOptions.type, valueProp = nameOrOptions.value, is = nameOrOptions.as, multiple = nameOrOptions.multiple;\n            if (type === \"checkbox\") {\n                if (valueProp === undefined) {\n                    field.checked = !!valueState;\n                } else {\n                    field.checked = !!(Array.isArray(valueState) && ~valueState.indexOf(valueProp));\n                    field.value = valueProp;\n                }\n            } else if (type === \"radio\") {\n                field.checked = valueState === valueProp;\n                field.value = valueProp;\n            } else if (is === \"select\" && multiple) {\n                field.value = field.value || [];\n                field.multiple = true;\n            }\n        }\n        return field;\n    }, [\n        handleBlur,\n        handleChange,\n        state.values\n    ]);\n    var dirty = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function() {\n        return !react_fast_compare__WEBPACK_IMPORTED_MODULE_2___default()(initialValues.current, state.values);\n    }, [\n        initialValues.current,\n        state.values\n    ]);\n    var isValid = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function() {\n        return typeof isInitialValid !== \"undefined\" ? dirty ? state.errors && Object.keys(state.errors).length === 0 : isInitialValid !== false && isFunction(isInitialValid) ? isInitialValid(props) : isInitialValid : state.errors && Object.keys(state.errors).length === 0;\n    }, [\n        isInitialValid,\n        dirty,\n        state.errors,\n        props\n    ]);\n    var ctx = _extends({}, state, {\n        initialValues: initialValues.current,\n        initialErrors: initialErrors.current,\n        initialTouched: initialTouched.current,\n        initialStatus: initialStatus.current,\n        handleBlur: handleBlur,\n        handleChange: handleChange,\n        handleReset: handleReset,\n        handleSubmit: handleSubmit,\n        resetForm: resetForm,\n        setErrors: setErrors,\n        setFormikState: setFormikState,\n        setFieldTouched: setFieldTouched,\n        setFieldValue: setFieldValue,\n        setFieldError: setFieldError,\n        setStatus: setStatus,\n        setSubmitting: setSubmitting,\n        setTouched: setTouched,\n        setValues: setValues,\n        submitForm: submitForm,\n        validateForm: validateFormWithHighPriority,\n        validateField: validateField,\n        isValid: isValid,\n        dirty: dirty,\n        unregisterField: unregisterField,\n        registerField: registerField,\n        getFieldProps: getFieldProps,\n        getFieldMeta: getFieldMeta,\n        getFieldHelpers: getFieldHelpers,\n        validateOnBlur: validateOnBlur,\n        validateOnChange: validateOnChange,\n        validateOnMount: validateOnMount\n    });\n    return ctx;\n}\nfunction Formik(props) {\n    var formikbag = useFormik(props);\n    var component = props.component, children = props.children, render = props.render, innerRef = props.innerRef; // This allows folks to pass a ref to <Formik />\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(innerRef, function() {\n        return formikbag;\n    });\n    if (true) {\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n            !!props.render ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, \"<Formik render> has been deprecated and will be removed in future versions of Formik. Please use a child callback function instead. To get rid of this warning, replace <Formik render={(props) => ...} /> with <Formik>{(props) => ...}</Formik>\") : 0 : void 0; // eslint-disable-next-line\n        }, []);\n    }\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(FormikProvider, {\n        value: formikbag\n    }, component ? /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(component, formikbag) : render ? render(formikbag) : children // children come last, always called\n     ? isFunction(children) ? children(formikbag) : !isEmptyChildren(children) ? react__WEBPACK_IMPORTED_MODULE_1__.Children.only(children) : null : null);\n}\nfunction warnAboutMissingIdentifier(_ref4) {\n    var htmlContent = _ref4.htmlContent, documentationAnchorLink = _ref4.documentationAnchorLink, handlerName = _ref4.handlerName;\n    console.warn(\"Warning: Formik called `\" + handlerName + \"`, but you forgot to pass an `id` or `name` attribute to your input:\\n    \" + htmlContent + \"\\n    Formik cannot determine which value to update. For more info see https://formik.org/docs/api/formik#\" + documentationAnchorLink + \"\\n  \");\n}\n/**\r\n * Transform Yup ValidationError to a more usable object\r\n */ function yupToFormErrors(yupError) {\n    var errors = {};\n    if (yupError.inner) {\n        if (yupError.inner.length === 0) {\n            return setIn(errors, yupError.path, yupError.message);\n        }\n        for(var _iterator = yupError.inner, _isArray = Array.isArray(_iterator), _i = 0, _iterator = _isArray ? _iterator : _iterator[Symbol.iterator]();;){\n            var _ref5;\n            if (_isArray) {\n                if (_i >= _iterator.length) break;\n                _ref5 = _iterator[_i++];\n            } else {\n                _i = _iterator.next();\n                if (_i.done) break;\n                _ref5 = _i.value;\n            }\n            var err = _ref5;\n            if (!getIn(errors, err.path)) {\n                errors = setIn(errors, err.path, err.message);\n            }\n        }\n    }\n    return errors;\n}\n/**\r\n * Validate a yup schema.\r\n */ function validateYupSchema(values, schema, sync, context) {\n    if (sync === void 0) {\n        sync = false;\n    }\n    var normalizedValues = prepareDataForValidation(values);\n    return schema[sync ? \"validateSync\" : \"validate\"](normalizedValues, {\n        abortEarly: false,\n        context: context || normalizedValues\n    });\n}\n/**\r\n * Recursively prepare values.\r\n */ function prepareDataForValidation(values) {\n    var data = Array.isArray(values) ? [] : {};\n    for(var k in values){\n        if (Object.prototype.hasOwnProperty.call(values, k)) {\n            var key = String(k);\n            if (Array.isArray(values[key]) === true) {\n                data[key] = values[key].map(function(value) {\n                    if (Array.isArray(value) === true || (0,lodash_es_isPlainObject__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(value)) {\n                        return prepareDataForValidation(value);\n                    } else {\n                        return value !== \"\" ? value : undefined;\n                    }\n                });\n            } else if ((0,lodash_es_isPlainObject__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(values[key])) {\n                data[key] = prepareDataForValidation(values[key]);\n            } else {\n                data[key] = values[key] !== \"\" ? values[key] : undefined;\n            }\n        }\n    }\n    return data;\n}\n/**\r\n * deepmerge array merging algorithm\r\n * https://github.com/KyleAMathews/deepmerge#combine-array\r\n */ function arrayMerge(target, source, options) {\n    var destination = target.slice();\n    source.forEach(function merge(e, i) {\n        if (typeof destination[i] === \"undefined\") {\n            var cloneRequested = options.clone !== false;\n            var shouldClone = cloneRequested && options.isMergeableObject(e);\n            destination[i] = shouldClone ? (0,deepmerge__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(Array.isArray(e) ? [] : {}, e, options) : e;\n        } else if (options.isMergeableObject(e)) {\n            destination[i] = (0,deepmerge__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(target[i], e, options);\n        } else if (target.indexOf(e) === -1) {\n            destination.push(e);\n        }\n    });\n    return destination;\n}\n/** Return multi select values based on an array of options */ function getSelectedValues(options) {\n    return Array.from(options).filter(function(el) {\n        return el.selected;\n    }).map(function(el) {\n        return el.value;\n    });\n}\n/** Return the next value for a checkbox */ function getValueForCheckbox(currentValue, checked, valueProp) {\n    // If the current value was a boolean, return a boolean\n    if (typeof currentValue === \"boolean\") {\n        return Boolean(checked);\n    } // If the currentValue was not a boolean we want to return an array\n    var currentArrayOfValues = [];\n    var isValueInArray = false;\n    var index = -1;\n    if (!Array.isArray(currentValue)) {\n        // eslint-disable-next-line eqeqeq\n        if (!valueProp || valueProp == \"true\" || valueProp == \"false\") {\n            return Boolean(checked);\n        }\n    } else {\n        // If the current value is already an array, use it\n        currentArrayOfValues = currentValue;\n        index = currentValue.indexOf(valueProp);\n        isValueInArray = index >= 0;\n    } // If the checkbox was checked and the value is not already present in the aray we want to add the new value to the array of values\n    if (checked && valueProp && !isValueInArray) {\n        return currentArrayOfValues.concat(valueProp);\n    } // If the checkbox was unchecked and the value is not in the array, simply return the already existing array of values\n    if (!isValueInArray) {\n        return currentArrayOfValues;\n    } // If the checkbox was unchecked and the value is in the array, remove the value and return the array\n    return currentArrayOfValues.slice(0, index).concat(currentArrayOfValues.slice(index + 1));\n} // React currently throws a warning when using useLayoutEffect on the server.\n// To get around it, we can conditionally useEffect on the server (no-op) and\n// useLayoutEffect in the browser.\n// @see https://gist.github.com/gaearon/e7d97cdf38a2907924ea12e4ebdf3c85\nvar useIsomorphicLayoutEffect =  false ? 0 : react__WEBPACK_IMPORTED_MODULE_1__.useEffect;\nfunction useEventCallback(fn) {\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(fn); // we copy a ref to the callback scoped to the current state/props on each render\n    useIsomorphicLayoutEffect(function() {\n        ref.current = fn;\n    });\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        return ref.current.apply(void 0, args);\n    }, []);\n}\nfunction useField(propsOrFieldName) {\n    var formik = useFormikContext();\n    var getFieldProps = formik.getFieldProps, getFieldMeta = formik.getFieldMeta, getFieldHelpers = formik.getFieldHelpers, registerField = formik.registerField, unregisterField = formik.unregisterField;\n    var isAnObject = isObject(propsOrFieldName); // Normalize propsOrFieldName to FieldHookConfig<Val>\n    var props = isAnObject ? propsOrFieldName : {\n        name: propsOrFieldName\n    };\n    var fieldName = props.name, validateFn = props.validate;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        if (fieldName) {\n            registerField(fieldName, {\n                validate: validateFn\n            });\n        }\n        return function() {\n            if (fieldName) {\n                unregisterField(fieldName);\n            }\n        };\n    }, [\n        registerField,\n        unregisterField,\n        fieldName,\n        validateFn\n    ]);\n    if (true) {\n        !formik ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, \"useField() / <Field /> must be used underneath a <Formik> component or withFormik() higher order component\") : 0 : void 0;\n    }\n    !fieldName ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, \"Invalid field name. Either pass `useField` a string or an object containing a `name` key.\") : 0 : void 0;\n    var fieldHelpers = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function() {\n        return getFieldHelpers(fieldName);\n    }, [\n        getFieldHelpers,\n        fieldName\n    ]);\n    return [\n        getFieldProps(props),\n        getFieldMeta(fieldName),\n        fieldHelpers\n    ];\n}\nfunction Field(_ref) {\n    var validate = _ref.validate, name = _ref.name, render = _ref.render, children = _ref.children, is = _ref.as, component = _ref.component, className = _ref.className, props = _objectWithoutPropertiesLoose(_ref, [\n        \"validate\",\n        \"name\",\n        \"render\",\n        \"children\",\n        \"as\",\n        \"component\",\n        \"className\"\n    ]);\n    var _useFormikContext = useFormikContext(), formik = _objectWithoutPropertiesLoose(_useFormikContext, [\n        \"validate\",\n        \"validationSchema\"\n    ]);\n    if (true) {\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n            !!render ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, '<Field render> has been deprecated and will be removed in future versions of Formik. Please use a child callback function instead. To get rid of this warning, replace <Field name=\"' + name + '\" render={({field, form}) => ...} /> with <Field name=\"' + name + '\">{({field, form, meta}) => ...}</Field>') : 0 : void 0;\n            !!(is && children && isFunction(children)) ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, \"You should not use <Field as> and <Field children> as a function in the same <Field> component; <Field as> will be ignored.\") : 0 : void 0;\n            !!(component && children && isFunction(children)) ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, \"You should not use <Field component> and <Field children> as a function in the same <Field> component; <Field component> will be ignored.\") : 0 : void 0;\n            !!(render && children && !isEmptyChildren(children)) ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, \"You should not use <Field render> and <Field children> in the same <Field> component; <Field children> will be ignored\") : 0 : void 0; // eslint-disable-next-line\n        }, []);\n    } // Register field and field-level validation with parent <Formik>\n    var registerField = formik.registerField, unregisterField = formik.unregisterField;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        registerField(name, {\n            validate: validate\n        });\n        return function() {\n            unregisterField(name);\n        };\n    }, [\n        registerField,\n        unregisterField,\n        name,\n        validate\n    ]);\n    var field = formik.getFieldProps(_extends({\n        name: name\n    }, props));\n    var meta = formik.getFieldMeta(name);\n    var legacyBag = {\n        field: field,\n        form: formik\n    };\n    if (render) {\n        return render(_extends({}, legacyBag, {\n            meta: meta\n        }));\n    }\n    if (isFunction(children)) {\n        return children(_extends({}, legacyBag, {\n            meta: meta\n        }));\n    }\n    if (component) {\n        // This behavior is backwards compat with earlier Formik 0.9 to 1.x\n        if (typeof component === \"string\") {\n            var innerRef = props.innerRef, rest = _objectWithoutPropertiesLoose(props, [\n                \"innerRef\"\n            ]);\n            return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(component, _extends({\n                ref: innerRef\n            }, field, rest, {\n                className: className\n            }), children);\n        } // We don't pass `meta` for backwards compat\n        return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(component, _extends({\n            field: field,\n            form: formik\n        }, props, {\n            className: className\n        }), children);\n    } // default to input here so we can check for both `as` and `children` above\n    var asElement = is || \"input\";\n    if (typeof asElement === \"string\") {\n        var _innerRef = props.innerRef, _rest = _objectWithoutPropertiesLoose(props, [\n            \"innerRef\"\n        ]);\n        return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(asElement, _extends({\n            ref: _innerRef\n        }, field, _rest, {\n            className: className\n        }), children);\n    }\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(asElement, _extends({}, field, props, {\n        className: className\n    }), children);\n}\nvar Form = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(function(props, ref) {\n    // iOS needs an \"action\" attribute for nice input: https://stackoverflow.com/a/39485162/406725\n    // We default the action to \"#\" in case the preventDefault fails (just updates the URL hash)\n    var action = props.action, rest = _objectWithoutPropertiesLoose(props, [\n        \"action\"\n    ]);\n    var _action = action != null ? action : \"#\";\n    var _useFormikContext = useFormikContext(), handleReset = _useFormikContext.handleReset, handleSubmit = _useFormikContext.handleSubmit;\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(\"form\", _extends({\n        onSubmit: handleSubmit,\n        ref: ref,\n        onReset: handleReset,\n        action: _action\n    }, rest));\n});\nForm.displayName = \"Form\";\n/**\r\n * A public higher-order component to access the imperative API\r\n */ function withFormik(_ref) {\n    var _ref$mapPropsToValues = _ref.mapPropsToValues, mapPropsToValues = _ref$mapPropsToValues === void 0 ? function(vanillaProps) {\n        var val = {};\n        for(var k in vanillaProps){\n            if (vanillaProps.hasOwnProperty(k) && typeof vanillaProps[k] !== \"function\") {\n                // @todo TypeScript fix\n                val[k] = vanillaProps[k];\n            }\n        }\n        return val;\n    } : _ref$mapPropsToValues, config = _objectWithoutPropertiesLoose(_ref, [\n        \"mapPropsToValues\"\n    ]);\n    return function createFormik(Component$1) {\n        var componentDisplayName = Component$1.displayName || Component$1.name || Component$1.constructor && Component$1.constructor.name || \"Component\";\n        /**\r\n     * We need to use closures here for to provide the wrapped component's props to\r\n     * the respective withFormik config methods.\r\n     */ var C = /*#__PURE__*/ function(_React$Component) {\n            _inheritsLoose(C, _React$Component);\n            function C() {\n                var _this;\n                for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n                    args[_key] = arguments[_key];\n                }\n                _this = _React$Component.call.apply(_React$Component, [\n                    this\n                ].concat(args)) || this;\n                _this.validate = function(values) {\n                    return config.validate(values, _this.props);\n                };\n                _this.validationSchema = function() {\n                    return isFunction(config.validationSchema) ? config.validationSchema(_this.props) : config.validationSchema;\n                };\n                _this.handleSubmit = function(values, actions) {\n                    return config.handleSubmit(values, _extends({}, actions, {\n                        props: _this.props\n                    }));\n                };\n                _this.renderFormComponent = function(formikProps) {\n                    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(Component$1, _extends({}, _this.props, formikProps));\n                };\n                return _this;\n            }\n            var _proto = C.prototype;\n            _proto.render = function render() {\n                var _this$props = this.props, props = _objectWithoutPropertiesLoose(_this$props, [\n                    \"children\"\n                ]);\n                return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(Formik, _extends({}, props, config, {\n                    validate: config.validate && this.validate,\n                    validationSchema: config.validationSchema && this.validationSchema,\n                    initialValues: mapPropsToValues(this.props),\n                    initialStatus: config.mapPropsToStatus && config.mapPropsToStatus(this.props),\n                    initialErrors: config.mapPropsToErrors && config.mapPropsToErrors(this.props),\n                    initialTouched: config.mapPropsToTouched && config.mapPropsToTouched(this.props),\n                    onSubmit: this.handleSubmit,\n                    children: this.renderFormComponent\n                }));\n            };\n            return C;\n        }(react__WEBPACK_IMPORTED_MODULE_1__.Component);\n        C.displayName = \"WithFormik(\" + componentDisplayName + \")\";\n        return hoist_non_react_statics__WEBPACK_IMPORTED_MODULE_3___default()(C, Component$1 // cast type to ComponentClass (even if SFC)\n        );\n    };\n}\n/**\r\n * Connect any component to Formik context, and inject as a prop called `formik`;\r\n * @param Comp React Component\r\n */ function connect(Comp) {\n    var C = function C(props) {\n        return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(FormikConsumer, null, function(formik) {\n            !!!formik ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, \"Formik context is undefined, please verify you are rendering <Form>, <Field>, <FastField>, <FieldArray>, or your custom context-using component as a child of a <Formik> component. Component name: \" + Comp.name) : 0 : void 0;\n            return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(Comp, _extends({}, props, {\n                formik: formik\n            }));\n        });\n    };\n    var componentDisplayName = Comp.displayName || Comp.name || Comp.constructor && Comp.constructor.name || \"Component\"; // Assign Comp to C.WrappedComponent so we can access the inner component in tests\n    // For example, <Field.WrappedComponent /> gets us <FieldInner/>\n    C.WrappedComponent = Comp;\n    C.displayName = \"FormikConnect(\" + componentDisplayName + \")\";\n    return hoist_non_react_statics__WEBPACK_IMPORTED_MODULE_3___default()(C, Comp // cast type to ComponentClass (even if SFC)\n    );\n}\n/**\r\n * Some array helpers!\r\n */ var move = function move(array, from, to) {\n    var copy = copyArrayLike(array);\n    var value = copy[from];\n    copy.splice(from, 1);\n    copy.splice(to, 0, value);\n    return copy;\n};\nvar swap = function swap(arrayLike, indexA, indexB) {\n    var copy = copyArrayLike(arrayLike);\n    var a = copy[indexA];\n    copy[indexA] = copy[indexB];\n    copy[indexB] = a;\n    return copy;\n};\nvar insert = function insert(arrayLike, index, value) {\n    var copy = copyArrayLike(arrayLike);\n    copy.splice(index, 0, value);\n    return copy;\n};\nvar replace = function replace(arrayLike, index, value) {\n    var copy = copyArrayLike(arrayLike);\n    copy[index] = value;\n    return copy;\n};\nvar copyArrayLike = function copyArrayLike(arrayLike) {\n    if (!arrayLike) {\n        return [];\n    } else if (Array.isArray(arrayLike)) {\n        return [].concat(arrayLike);\n    } else {\n        var maxIndex = Object.keys(arrayLike).map(function(key) {\n            return parseInt(key);\n        }).reduce(function(max, el) {\n            return el > max ? el : max;\n        }, 0);\n        return Array.from(_extends({}, arrayLike, {\n            length: maxIndex + 1\n        }));\n    }\n};\nvar createAlterationHandler = function createAlterationHandler(alteration, defaultFunction) {\n    var fn = typeof alteration === \"function\" ? alteration : defaultFunction;\n    return function(data) {\n        if (Array.isArray(data) || isObject(data)) {\n            var clone = copyArrayLike(data);\n            return fn(clone);\n        } // This can be assumed to be a primitive, which\n        // is a case for top level validation errors\n        return data;\n    };\n};\nvar FieldArrayInner = /*#__PURE__*/ function(_React$Component) {\n    _inheritsLoose(FieldArrayInner, _React$Component);\n    function FieldArrayInner(props) {\n        var _this;\n        _this = _React$Component.call(this, props) || this; // We need TypeScript generics on these, so we'll bind them in the constructor\n        // @todo Fix TS 3.2.1\n        _this.updateArrayField = function(fn, alterTouched, alterErrors) {\n            var _this$props = _this.props, name = _this$props.name, setFormikState = _this$props.formik.setFormikState;\n            setFormikState(function(prevState) {\n                var updateErrors = createAlterationHandler(alterErrors, fn);\n                var updateTouched = createAlterationHandler(alterTouched, fn); // values fn should be executed before updateErrors and updateTouched,\n                // otherwise it causes an error with unshift.\n                var values = setIn(prevState.values, name, fn(getIn(prevState.values, name)));\n                var fieldError = alterErrors ? updateErrors(getIn(prevState.errors, name)) : undefined;\n                var fieldTouched = alterTouched ? updateTouched(getIn(prevState.touched, name)) : undefined;\n                if (isEmptyArray(fieldError)) {\n                    fieldError = undefined;\n                }\n                if (isEmptyArray(fieldTouched)) {\n                    fieldTouched = undefined;\n                }\n                return _extends({}, prevState, {\n                    values: values,\n                    errors: alterErrors ? setIn(prevState.errors, name, fieldError) : prevState.errors,\n                    touched: alterTouched ? setIn(prevState.touched, name, fieldTouched) : prevState.touched\n                });\n            });\n        };\n        _this.push = function(value) {\n            return _this.updateArrayField(function(arrayLike) {\n                return [].concat(copyArrayLike(arrayLike), [\n                    (0,lodash_es_cloneDeep__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(value)\n                ]);\n            }, false, false);\n        };\n        _this.handlePush = function(value) {\n            return function() {\n                return _this.push(value);\n            };\n        };\n        _this.swap = function(indexA, indexB) {\n            return _this.updateArrayField(function(array) {\n                return swap(array, indexA, indexB);\n            }, true, true);\n        };\n        _this.handleSwap = function(indexA, indexB) {\n            return function() {\n                return _this.swap(indexA, indexB);\n            };\n        };\n        _this.move = function(from, to) {\n            return _this.updateArrayField(function(array) {\n                return move(array, from, to);\n            }, true, true);\n        };\n        _this.handleMove = function(from, to) {\n            return function() {\n                return _this.move(from, to);\n            };\n        };\n        _this.insert = function(index, value) {\n            return _this.updateArrayField(function(array) {\n                return insert(array, index, value);\n            }, function(array) {\n                return insert(array, index, null);\n            }, function(array) {\n                return insert(array, index, null);\n            });\n        };\n        _this.handleInsert = function(index, value) {\n            return function() {\n                return _this.insert(index, value);\n            };\n        };\n        _this.replace = function(index, value) {\n            return _this.updateArrayField(function(array) {\n                return replace(array, index, value);\n            }, false, false);\n        };\n        _this.handleReplace = function(index, value) {\n            return function() {\n                return _this.replace(index, value);\n            };\n        };\n        _this.unshift = function(value) {\n            var length = -1;\n            _this.updateArrayField(function(array) {\n                var arr = array ? [\n                    value\n                ].concat(array) : [\n                    value\n                ];\n                length = arr.length;\n                return arr;\n            }, function(array) {\n                return array ? [\n                    null\n                ].concat(array) : [\n                    null\n                ];\n            }, function(array) {\n                return array ? [\n                    null\n                ].concat(array) : [\n                    null\n                ];\n            });\n            return length;\n        };\n        _this.handleUnshift = function(value) {\n            return function() {\n                return _this.unshift(value);\n            };\n        };\n        _this.handleRemove = function(index) {\n            return function() {\n                return _this.remove(index);\n            };\n        };\n        _this.handlePop = function() {\n            return function() {\n                return _this.pop();\n            };\n        };\n        _this.remove = _this.remove.bind(_assertThisInitialized(_this));\n        _this.pop = _this.pop.bind(_assertThisInitialized(_this));\n        return _this;\n    }\n    var _proto = FieldArrayInner.prototype;\n    _proto.componentDidUpdate = function componentDidUpdate(prevProps) {\n        if (this.props.validateOnChange && this.props.formik.validateOnChange && !react_fast_compare__WEBPACK_IMPORTED_MODULE_2___default()(getIn(prevProps.formik.values, prevProps.name), getIn(this.props.formik.values, this.props.name))) {\n            this.props.formik.validateForm(this.props.formik.values);\n        }\n    };\n    _proto.remove = function remove(index) {\n        // We need to make sure we also remove relevant pieces of `touched` and `errors`\n        var result;\n        this.updateArrayField(function(array) {\n            var copy = array ? copyArrayLike(array) : [];\n            if (!result) {\n                result = copy[index];\n            }\n            if (isFunction(copy.splice)) {\n                copy.splice(index, 1);\n            } // if the array only includes undefined values we have to return an empty array\n            return isFunction(copy.every) ? copy.every(function(v) {\n                return v === undefined;\n            }) ? [] : copy : copy;\n        }, true, true);\n        return result;\n    };\n    _proto.pop = function pop() {\n        // Remove relevant pieces of `touched` and `errors` too!\n        var result;\n        this.updateArrayField(function(array) {\n            var tmp = array.slice();\n            if (!result) {\n                result = tmp && tmp.pop && tmp.pop();\n            }\n            return tmp;\n        }, true, true);\n        return result;\n    };\n    _proto.render = function render() {\n        var arrayHelpers = {\n            push: this.push,\n            pop: this.pop,\n            swap: this.swap,\n            move: this.move,\n            insert: this.insert,\n            replace: this.replace,\n            unshift: this.unshift,\n            remove: this.remove,\n            handlePush: this.handlePush,\n            handlePop: this.handlePop,\n            handleSwap: this.handleSwap,\n            handleMove: this.handleMove,\n            handleInsert: this.handleInsert,\n            handleReplace: this.handleReplace,\n            handleUnshift: this.handleUnshift,\n            handleRemove: this.handleRemove\n        };\n        var _this$props2 = this.props, component = _this$props2.component, render = _this$props2.render, children = _this$props2.children, name = _this$props2.name, _this$props2$formik = _this$props2.formik, restOfFormik = _objectWithoutPropertiesLoose(_this$props2$formik, [\n            \"validate\",\n            \"validationSchema\"\n        ]);\n        var props = _extends({}, arrayHelpers, {\n            form: restOfFormik,\n            name: name\n        });\n        return component ? /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(component, props) : render ? render(props) : children // children come last, always called\n         ? typeof children === \"function\" ? children(props) : !isEmptyChildren(children) ? react__WEBPACK_IMPORTED_MODULE_1__.Children.only(children) : null : null;\n    };\n    return FieldArrayInner;\n}(react__WEBPACK_IMPORTED_MODULE_1__.Component);\nFieldArrayInner.defaultProps = {\n    validateOnChange: true\n};\nvar FieldArray = /*#__PURE__*/ connect(FieldArrayInner);\nvar ErrorMessageImpl = /*#__PURE__*/ function(_React$Component) {\n    _inheritsLoose(ErrorMessageImpl, _React$Component);\n    function ErrorMessageImpl() {\n        return _React$Component.apply(this, arguments) || this;\n    }\n    var _proto = ErrorMessageImpl.prototype;\n    _proto.shouldComponentUpdate = function shouldComponentUpdate(props) {\n        if (getIn(this.props.formik.errors, this.props.name) !== getIn(props.formik.errors, this.props.name) || getIn(this.props.formik.touched, this.props.name) !== getIn(props.formik.touched, this.props.name) || Object.keys(this.props).length !== Object.keys(props).length) {\n            return true;\n        } else {\n            return false;\n        }\n    };\n    _proto.render = function render() {\n        var _this$props = this.props, component = _this$props.component, formik = _this$props.formik, render = _this$props.render, children = _this$props.children, name = _this$props.name, rest = _objectWithoutPropertiesLoose(_this$props, [\n            \"component\",\n            \"formik\",\n            \"render\",\n            \"children\",\n            \"name\"\n        ]);\n        var touch = getIn(formik.touched, name);\n        var error = getIn(formik.errors, name);\n        return !!touch && !!error ? render ? isFunction(render) ? render(error) : null : children ? isFunction(children) ? children(error) : null : component ? /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(component, rest, error) : error : null;\n    };\n    return ErrorMessageImpl;\n}(react__WEBPACK_IMPORTED_MODULE_1__.Component);\nvar ErrorMessage = /*#__PURE__*/ connect(ErrorMessageImpl);\n/**\r\n * Custom Field component for quickly hooking into Formik\r\n * context and wiring up forms.\r\n */ var FastFieldInner = /*#__PURE__*/ function(_React$Component) {\n    _inheritsLoose(FastFieldInner, _React$Component);\n    function FastFieldInner(props) {\n        var _this;\n        _this = _React$Component.call(this, props) || this;\n        var render = props.render, children = props.children, component = props.component, is = props.as, name = props.name;\n        !!render ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, \"<FastField render> has been deprecated. Please use a child callback function instead: <FastField name={\" + name + \"}>{props => ...}</FastField> instead.\") : 0 : void 0;\n        !!(component && render) ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, \"You should not use <FastField component> and <FastField render> in the same <FastField> component; <FastField component> will be ignored\") : 0 : void 0;\n        !!(is && children && isFunction(children)) ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, \"You should not use <FastField as> and <FastField children> as a function in the same <FastField> component; <FastField as> will be ignored.\") : 0 : void 0;\n        !!(component && children && isFunction(children)) ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, \"You should not use <FastField component> and <FastField children> as a function in the same <FastField> component; <FastField component> will be ignored.\") : 0 : void 0;\n        !!(render && children && !isEmptyChildren(children)) ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, \"You should not use <FastField render> and <FastField children> in the same <FastField> component; <FastField children> will be ignored\") : 0 : void 0;\n        return _this;\n    }\n    var _proto = FastFieldInner.prototype;\n    _proto.shouldComponentUpdate = function shouldComponentUpdate(props) {\n        if (this.props.shouldUpdate) {\n            return this.props.shouldUpdate(props, this.props);\n        } else if (props.name !== this.props.name || getIn(props.formik.values, this.props.name) !== getIn(this.props.formik.values, this.props.name) || getIn(props.formik.errors, this.props.name) !== getIn(this.props.formik.errors, this.props.name) || getIn(props.formik.touched, this.props.name) !== getIn(this.props.formik.touched, this.props.name) || Object.keys(this.props).length !== Object.keys(props).length || props.formik.isSubmitting !== this.props.formik.isSubmitting) {\n            return true;\n        } else {\n            return false;\n        }\n    };\n    _proto.componentDidMount = function componentDidMount() {\n        // Register the Field with the parent Formik. Parent will cycle through\n        // registered Field's validate fns right prior to submit\n        this.props.formik.registerField(this.props.name, {\n            validate: this.props.validate\n        });\n    };\n    _proto.componentDidUpdate = function componentDidUpdate(prevProps) {\n        if (this.props.name !== prevProps.name) {\n            this.props.formik.unregisterField(prevProps.name);\n            this.props.formik.registerField(this.props.name, {\n                validate: this.props.validate\n            });\n        }\n        if (this.props.validate !== prevProps.validate) {\n            this.props.formik.registerField(this.props.name, {\n                validate: this.props.validate\n            });\n        }\n    };\n    _proto.componentWillUnmount = function componentWillUnmount() {\n        this.props.formik.unregisterField(this.props.name);\n    };\n    _proto.render = function render() {\n        var _this$props = this.props, name = _this$props.name, render = _this$props.render, is = _this$props.as, children = _this$props.children, component = _this$props.component, formik = _this$props.formik, props = _objectWithoutPropertiesLoose(_this$props, [\n            \"validate\",\n            \"name\",\n            \"render\",\n            \"as\",\n            \"children\",\n            \"component\",\n            \"shouldUpdate\",\n            \"formik\"\n        ]);\n        var restOfFormik = _objectWithoutPropertiesLoose(formik, [\n            \"validate\",\n            \"validationSchema\"\n        ]);\n        var field = formik.getFieldProps(_extends({\n            name: name\n        }, props));\n        var meta = {\n            value: getIn(formik.values, name),\n            error: getIn(formik.errors, name),\n            touched: !!getIn(formik.touched, name),\n            initialValue: getIn(formik.initialValues, name),\n            initialTouched: !!getIn(formik.initialTouched, name),\n            initialError: getIn(formik.initialErrors, name)\n        };\n        var bag = {\n            field: field,\n            meta: meta,\n            form: restOfFormik\n        };\n        if (render) {\n            return render(bag);\n        }\n        if (isFunction(children)) {\n            return children(bag);\n        }\n        if (component) {\n            // This behavior is backwards compat with earlier Formik 0.9 to 1.x\n            if (typeof component === \"string\") {\n                var innerRef = props.innerRef, rest = _objectWithoutPropertiesLoose(props, [\n                    \"innerRef\"\n                ]);\n                return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(component, _extends({\n                    ref: innerRef\n                }, field, rest), children);\n            } // We don't pass `meta` for backwards compat\n            return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(component, _extends({\n                field: field,\n                form: formik\n            }, props), children);\n        } // default to input here so we can check for both `as` and `children` above\n        var asElement = is || \"input\";\n        if (typeof asElement === \"string\") {\n            var _innerRef = props.innerRef, _rest = _objectWithoutPropertiesLoose(props, [\n                \"innerRef\"\n            ]);\n            return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(asElement, _extends({\n                ref: _innerRef\n            }, field, _rest), children);\n        }\n        return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(asElement, _extends({}, field, props), children);\n    };\n    return FastFieldInner;\n}(react__WEBPACK_IMPORTED_MODULE_1__.Component);\nvar FastField = /*#__PURE__*/ connect(FastFieldInner);\n //# sourceMappingURL=formik.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/formik/dist/formik.esm.js\n");

/***/ })

};
;