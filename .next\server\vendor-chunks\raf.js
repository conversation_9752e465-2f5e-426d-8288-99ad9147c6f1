"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/raf";
exports.ids = ["vendor-chunks/raf"];
exports.modules = {

/***/ "(ssr)/./node_modules/raf/index.js":
/*!***********************************!*\
  !*** ./node_modules/raf/index.js ***!
  \***********************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar now = __webpack_require__(/*! performance-now */ \"(ssr)/./node_modules/performance-now/lib/performance-now.js\"), root = \"undefined\" === \"undefined\" ? global : window, vendors = [\n    \"moz\",\n    \"webkit\"\n], suffix = \"AnimationFrame\", raf = root[\"request\" + suffix], caf = root[\"cancel\" + suffix] || root[\"cancelRequest\" + suffix];\nfor(var i = 0; !raf && i < vendors.length; i++){\n    raf = root[vendors[i] + \"Request\" + suffix];\n    caf = root[vendors[i] + \"Cancel\" + suffix] || root[vendors[i] + \"CancelRequest\" + suffix];\n}\n// Some versions of FF have rAF but not cAF\nif (!raf || !caf) {\n    var last = 0, id = 0, queue = [], frameDuration = 1000 / 60;\n    raf = function(callback) {\n        if (queue.length === 0) {\n            var _now = now(), next = Math.max(0, frameDuration - (_now - last));\n            last = next + _now;\n            setTimeout(function() {\n                var cp = queue.slice(0);\n                // Clear queue here to prevent\n                // callbacks from appending listeners\n                // to the current frame's queue\n                queue.length = 0;\n                for(var i = 0; i < cp.length; i++){\n                    if (!cp[i].cancelled) {\n                        try {\n                            cp[i].callback(last);\n                        } catch (e) {\n                            setTimeout(function() {\n                                throw e;\n                            }, 0);\n                        }\n                    }\n                }\n            }, Math.round(next));\n        }\n        queue.push({\n            handle: ++id,\n            callback: callback,\n            cancelled: false\n        });\n        return id;\n    };\n    caf = function(handle) {\n        for(var i = 0; i < queue.length; i++){\n            if (queue[i].handle === handle) {\n                queue[i].cancelled = true;\n            }\n        }\n    };\n}\nmodule.exports = function(fn) {\n    // Wrap in a new function to prevent\n    // `cancel` potentially being assigned\n    // to the native rAF function\n    return raf.call(root, fn);\n};\nmodule.exports.cancel = function() {\n    caf.apply(root, arguments);\n};\nmodule.exports.polyfill = function(object) {\n    if (!object) {\n        object = root;\n    }\n    object.requestAnimationFrame = raf;\n    object.cancelAnimationFrame = caf;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/raf/index.js\n");

/***/ })

};
;