"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/stackblur-canvas";
exports.ids = ["vendor-chunks/stackblur-canvas"];
exports.modules = {

/***/ "(ssr)/./node_modules/stackblur-canvas/dist/stackblur-es.js":
/*!************************************************************!*\
  !*** ./node_modules/stackblur-canvas/dist/stackblur-es.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlurStack: () => (/* binding */ BlurStack),\n/* harmony export */   canvasRGB: () => (/* binding */ processCanvasRGB),\n/* harmony export */   canvasRGBA: () => (/* binding */ processCanvasRGBA),\n/* harmony export */   image: () => (/* binding */ processImage),\n/* harmony export */   imageDataRGB: () => (/* binding */ processImageDataRGB),\n/* harmony export */   imageDataRGBA: () => (/* binding */ processImageDataRGBA)\n/* harmony export */ });\nfunction _typeof(obj) {\n    \"@babel/helpers - typeof\";\n    if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n        _typeof = function(obj) {\n            return typeof obj;\n        };\n    } else {\n        _typeof = function(obj) {\n            return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n        };\n    }\n    return _typeof(obj);\n}\nfunction _classCallCheck(instance, Constructor) {\n    if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n    }\n}\n/* eslint-disable no-bitwise -- used for calculations */ /* eslint-disable unicorn/prefer-query-selector -- aiming at\n  backward-compatibility */ /**\n* StackBlur - a fast almost Gaussian Blur For Canvas\n*\n* In case you find this class useful - especially in commercial projects -\n* I am not totally unhappy for a small donation to my PayPal account\n* <EMAIL>\n*\n* Or support me on flattr:\n* {@link https://flattr.com/thing/72791/StackBlur-a-fast-almost-Gaussian-Blur-Effect-for-CanvasJavascript}.\n*\n* @module StackBlur\n* <AUTHOR> Klingemann\n* Contact: <EMAIL>\n* Website: {@link http://www.quasimondo.com/StackBlurForCanvas/StackBlurDemo.html}\n* Twitter: @quasimondo\n*\n* @copyright (c) 2010 Mario Klingemann\n*\n* Permission is hereby granted, free of charge, to any person\n* obtaining a copy of this software and associated documentation\n* files (the \"Software\"), to deal in the Software without\n* restriction, including without limitation the rights to use,\n* copy, modify, merge, publish, distribute, sublicense, and/or sell\n* copies of the Software, and to permit persons to whom the\n* Software is furnished to do so, subject to the following\n* conditions:\n*\n* The above copyright notice and this permission notice shall be\n* included in all copies or substantial portions of the Software.\n*\n* THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n* EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES\n* OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n* NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT\n* HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,\n* WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n* FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR\n* OTHER DEALINGS IN THE SOFTWARE.\n*/ var mulTable = [\n    512,\n    512,\n    456,\n    512,\n    328,\n    456,\n    335,\n    512,\n    405,\n    328,\n    271,\n    456,\n    388,\n    335,\n    292,\n    512,\n    454,\n    405,\n    364,\n    328,\n    298,\n    271,\n    496,\n    456,\n    420,\n    388,\n    360,\n    335,\n    312,\n    292,\n    273,\n    512,\n    482,\n    454,\n    428,\n    405,\n    383,\n    364,\n    345,\n    328,\n    312,\n    298,\n    284,\n    271,\n    259,\n    496,\n    475,\n    456,\n    437,\n    420,\n    404,\n    388,\n    374,\n    360,\n    347,\n    335,\n    323,\n    312,\n    302,\n    292,\n    282,\n    273,\n    265,\n    512,\n    497,\n    482,\n    468,\n    454,\n    441,\n    428,\n    417,\n    405,\n    394,\n    383,\n    373,\n    364,\n    354,\n    345,\n    337,\n    328,\n    320,\n    312,\n    305,\n    298,\n    291,\n    284,\n    278,\n    271,\n    265,\n    259,\n    507,\n    496,\n    485,\n    475,\n    465,\n    456,\n    446,\n    437,\n    428,\n    420,\n    412,\n    404,\n    396,\n    388,\n    381,\n    374,\n    367,\n    360,\n    354,\n    347,\n    341,\n    335,\n    329,\n    323,\n    318,\n    312,\n    307,\n    302,\n    297,\n    292,\n    287,\n    282,\n    278,\n    273,\n    269,\n    265,\n    261,\n    512,\n    505,\n    497,\n    489,\n    482,\n    475,\n    468,\n    461,\n    454,\n    447,\n    441,\n    435,\n    428,\n    422,\n    417,\n    411,\n    405,\n    399,\n    394,\n    389,\n    383,\n    378,\n    373,\n    368,\n    364,\n    359,\n    354,\n    350,\n    345,\n    341,\n    337,\n    332,\n    328,\n    324,\n    320,\n    316,\n    312,\n    309,\n    305,\n    301,\n    298,\n    294,\n    291,\n    287,\n    284,\n    281,\n    278,\n    274,\n    271,\n    268,\n    265,\n    262,\n    259,\n    257,\n    507,\n    501,\n    496,\n    491,\n    485,\n    480,\n    475,\n    470,\n    465,\n    460,\n    456,\n    451,\n    446,\n    442,\n    437,\n    433,\n    428,\n    424,\n    420,\n    416,\n    412,\n    408,\n    404,\n    400,\n    396,\n    392,\n    388,\n    385,\n    381,\n    377,\n    374,\n    370,\n    367,\n    363,\n    360,\n    357,\n    354,\n    350,\n    347,\n    344,\n    341,\n    338,\n    335,\n    332,\n    329,\n    326,\n    323,\n    320,\n    318,\n    315,\n    312,\n    310,\n    307,\n    304,\n    302,\n    299,\n    297,\n    294,\n    292,\n    289,\n    287,\n    285,\n    282,\n    280,\n    278,\n    275,\n    273,\n    271,\n    269,\n    267,\n    265,\n    263,\n    261,\n    259\n];\nvar shgTable = [\n    9,\n    11,\n    12,\n    13,\n    13,\n    14,\n    14,\n    15,\n    15,\n    15,\n    15,\n    16,\n    16,\n    16,\n    16,\n    17,\n    17,\n    17,\n    17,\n    17,\n    17,\n    17,\n    18,\n    18,\n    18,\n    18,\n    18,\n    18,\n    18,\n    18,\n    18,\n    19,\n    19,\n    19,\n    19,\n    19,\n    19,\n    19,\n    19,\n    19,\n    19,\n    19,\n    19,\n    19,\n    19,\n    20,\n    20,\n    20,\n    20,\n    20,\n    20,\n    20,\n    20,\n    20,\n    20,\n    20,\n    20,\n    20,\n    20,\n    20,\n    20,\n    20,\n    20,\n    21,\n    21,\n    21,\n    21,\n    21,\n    21,\n    21,\n    21,\n    21,\n    21,\n    21,\n    21,\n    21,\n    21,\n    21,\n    21,\n    21,\n    21,\n    21,\n    21,\n    21,\n    21,\n    21,\n    21,\n    21,\n    21,\n    21,\n    22,\n    22,\n    22,\n    22,\n    22,\n    22,\n    22,\n    22,\n    22,\n    22,\n    22,\n    22,\n    22,\n    22,\n    22,\n    22,\n    22,\n    22,\n    22,\n    22,\n    22,\n    22,\n    22,\n    22,\n    22,\n    22,\n    22,\n    22,\n    22,\n    22,\n    22,\n    22,\n    22,\n    22,\n    22,\n    22,\n    22,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    23,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24,\n    24\n];\n/**\n * @param {string|HTMLImageElement} img\n * @param {string|HTMLCanvasElement} canvas\n * @param {Float} radius\n * @param {boolean} blurAlphaChannel\n * @param {boolean} useOffset\n * @param {boolean} skipStyles\n * @returns {undefined}\n */ function processImage(img, canvas, radius, blurAlphaChannel, useOffset, skipStyles) {\n    if (typeof img === \"string\") {\n        img = document.getElementById(img);\n    }\n    if (!img || Object.prototype.toString.call(img).slice(8, -1) === \"HTMLImageElement\" && !(\"naturalWidth\" in img)) {\n        return;\n    }\n    var dimensionType = useOffset ? \"offset\" : \"natural\";\n    var w = img[dimensionType + \"Width\"];\n    var h = img[dimensionType + \"Height\"]; // add ImageBitmap support,can blur texture source\n    if (Object.prototype.toString.call(img).slice(8, -1) === \"ImageBitmap\") {\n        w = img.width;\n        h = img.height;\n    }\n    if (typeof canvas === \"string\") {\n        canvas = document.getElementById(canvas);\n    }\n    if (!canvas || !(\"getContext\" in canvas)) {\n        return;\n    }\n    if (!skipStyles) {\n        canvas.style.width = w + \"px\";\n        canvas.style.height = h + \"px\";\n    }\n    canvas.width = w;\n    canvas.height = h;\n    var context = canvas.getContext(\"2d\");\n    context.clearRect(0, 0, w, h);\n    context.drawImage(img, 0, 0, img.naturalWidth, img.naturalHeight, 0, 0, w, h);\n    if (isNaN(radius) || radius < 1) {\n        return;\n    }\n    if (blurAlphaChannel) {\n        processCanvasRGBA(canvas, 0, 0, w, h, radius);\n    } else {\n        processCanvasRGB(canvas, 0, 0, w, h, radius);\n    }\n}\n/**\n * @param {string|HTMLCanvasElement} canvas\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @throws {Error|TypeError}\n * @returns {ImageData} See {@link https://html.spec.whatwg.org/multipage/canvas.html#imagedata}\n */ function getImageDataFromCanvas(canvas, topX, topY, width, height) {\n    if (typeof canvas === \"string\") {\n        canvas = document.getElementById(canvas);\n    }\n    if (!canvas || _typeof(canvas) !== \"object\" || !(\"getContext\" in canvas)) {\n        throw new TypeError(\"Expecting canvas with `getContext` method \" + \"in processCanvasRGB(A) calls!\");\n    }\n    var context = canvas.getContext(\"2d\");\n    try {\n        return context.getImageData(topX, topY, width, height);\n    } catch (e) {\n        throw new Error(\"unable to access image data: \" + e);\n    }\n}\n/**\n * @param {HTMLCanvasElement} canvas\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @param {Float} radius\n * @returns {undefined}\n */ function processCanvasRGBA(canvas, topX, topY, width, height, radius) {\n    if (isNaN(radius) || radius < 1) {\n        return;\n    }\n    radius |= 0;\n    var imageData = getImageDataFromCanvas(canvas, topX, topY, width, height);\n    imageData = processImageDataRGBA(imageData, topX, topY, width, height, radius);\n    canvas.getContext(\"2d\").putImageData(imageData, topX, topY);\n}\n/**\n * @param {ImageData} imageData\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @param {Float} radius\n * @returns {ImageData}\n */ function processImageDataRGBA(imageData, topX, topY, width, height, radius) {\n    var pixels = imageData.data;\n    var div = 2 * radius + 1; // const w4 = width << 2;\n    var widthMinus1 = width - 1;\n    var heightMinus1 = height - 1;\n    var radiusPlus1 = radius + 1;\n    var sumFactor = radiusPlus1 * (radiusPlus1 + 1) / 2;\n    var stackStart = new BlurStack();\n    var stack = stackStart;\n    var stackEnd;\n    for(var i = 1; i < div; i++){\n        stack = stack.next = new BlurStack();\n        if (i === radiusPlus1) {\n            stackEnd = stack;\n        }\n    }\n    stack.next = stackStart;\n    var stackIn = null, stackOut = null, yw = 0, yi = 0;\n    var mulSum = mulTable[radius];\n    var shgSum = shgTable[radius];\n    for(var y = 0; y < height; y++){\n        stack = stackStart;\n        var pr = pixels[yi], pg = pixels[yi + 1], pb = pixels[yi + 2], pa = pixels[yi + 3];\n        for(var _i = 0; _i < radiusPlus1; _i++){\n            stack.r = pr;\n            stack.g = pg;\n            stack.b = pb;\n            stack.a = pa;\n            stack = stack.next;\n        }\n        var rInSum = 0, gInSum = 0, bInSum = 0, aInSum = 0, rOutSum = radiusPlus1 * pr, gOutSum = radiusPlus1 * pg, bOutSum = radiusPlus1 * pb, aOutSum = radiusPlus1 * pa, rSum = sumFactor * pr, gSum = sumFactor * pg, bSum = sumFactor * pb, aSum = sumFactor * pa;\n        for(var _i2 = 1; _i2 < radiusPlus1; _i2++){\n            var p = yi + ((widthMinus1 < _i2 ? widthMinus1 : _i2) << 2);\n            var r = pixels[p], g = pixels[p + 1], b = pixels[p + 2], a = pixels[p + 3];\n            var rbs = radiusPlus1 - _i2;\n            rSum += (stack.r = r) * rbs;\n            gSum += (stack.g = g) * rbs;\n            bSum += (stack.b = b) * rbs;\n            aSum += (stack.a = a) * rbs;\n            rInSum += r;\n            gInSum += g;\n            bInSum += b;\n            aInSum += a;\n            stack = stack.next;\n        }\n        stackIn = stackStart;\n        stackOut = stackEnd;\n        for(var x = 0; x < width; x++){\n            var paInitial = aSum * mulSum >>> shgSum;\n            pixels[yi + 3] = paInitial;\n            if (paInitial !== 0) {\n                var _a2 = 255 / paInitial;\n                pixels[yi] = (rSum * mulSum >>> shgSum) * _a2;\n                pixels[yi + 1] = (gSum * mulSum >>> shgSum) * _a2;\n                pixels[yi + 2] = (bSum * mulSum >>> shgSum) * _a2;\n            } else {\n                pixels[yi] = pixels[yi + 1] = pixels[yi + 2] = 0;\n            }\n            rSum -= rOutSum;\n            gSum -= gOutSum;\n            bSum -= bOutSum;\n            aSum -= aOutSum;\n            rOutSum -= stackIn.r;\n            gOutSum -= stackIn.g;\n            bOutSum -= stackIn.b;\n            aOutSum -= stackIn.a;\n            var _p = x + radius + 1;\n            _p = yw + (_p < widthMinus1 ? _p : widthMinus1) << 2;\n            rInSum += stackIn.r = pixels[_p];\n            gInSum += stackIn.g = pixels[_p + 1];\n            bInSum += stackIn.b = pixels[_p + 2];\n            aInSum += stackIn.a = pixels[_p + 3];\n            rSum += rInSum;\n            gSum += gInSum;\n            bSum += bInSum;\n            aSum += aInSum;\n            stackIn = stackIn.next;\n            var _stackOut = stackOut, _r = _stackOut.r, _g = _stackOut.g, _b = _stackOut.b, _a = _stackOut.a;\n            rOutSum += _r;\n            gOutSum += _g;\n            bOutSum += _b;\n            aOutSum += _a;\n            rInSum -= _r;\n            gInSum -= _g;\n            bInSum -= _b;\n            aInSum -= _a;\n            stackOut = stackOut.next;\n            yi += 4;\n        }\n        yw += width;\n    }\n    for(var _x = 0; _x < width; _x++){\n        yi = _x << 2;\n        var _pr = pixels[yi], _pg = pixels[yi + 1], _pb = pixels[yi + 2], _pa = pixels[yi + 3], _rOutSum = radiusPlus1 * _pr, _gOutSum = radiusPlus1 * _pg, _bOutSum = radiusPlus1 * _pb, _aOutSum = radiusPlus1 * _pa, _rSum = sumFactor * _pr, _gSum = sumFactor * _pg, _bSum = sumFactor * _pb, _aSum = sumFactor * _pa;\n        stack = stackStart;\n        for(var _i3 = 0; _i3 < radiusPlus1; _i3++){\n            stack.r = _pr;\n            stack.g = _pg;\n            stack.b = _pb;\n            stack.a = _pa;\n            stack = stack.next;\n        }\n        var yp = width;\n        var _gInSum = 0, _bInSum = 0, _aInSum = 0, _rInSum = 0;\n        for(var _i4 = 1; _i4 <= radius; _i4++){\n            yi = yp + _x << 2;\n            var _rbs = radiusPlus1 - _i4;\n            _rSum += (stack.r = _pr = pixels[yi]) * _rbs;\n            _gSum += (stack.g = _pg = pixels[yi + 1]) * _rbs;\n            _bSum += (stack.b = _pb = pixels[yi + 2]) * _rbs;\n            _aSum += (stack.a = _pa = pixels[yi + 3]) * _rbs;\n            _rInSum += _pr;\n            _gInSum += _pg;\n            _bInSum += _pb;\n            _aInSum += _pa;\n            stack = stack.next;\n            if (_i4 < heightMinus1) {\n                yp += width;\n            }\n        }\n        yi = _x;\n        stackIn = stackStart;\n        stackOut = stackEnd;\n        for(var _y = 0; _y < height; _y++){\n            var _p2 = yi << 2;\n            pixels[_p2 + 3] = _pa = _aSum * mulSum >>> shgSum;\n            if (_pa > 0) {\n                _pa = 255 / _pa;\n                pixels[_p2] = (_rSum * mulSum >>> shgSum) * _pa;\n                pixels[_p2 + 1] = (_gSum * mulSum >>> shgSum) * _pa;\n                pixels[_p2 + 2] = (_bSum * mulSum >>> shgSum) * _pa;\n            } else {\n                pixels[_p2] = pixels[_p2 + 1] = pixels[_p2 + 2] = 0;\n            }\n            _rSum -= _rOutSum;\n            _gSum -= _gOutSum;\n            _bSum -= _bOutSum;\n            _aSum -= _aOutSum;\n            _rOutSum -= stackIn.r;\n            _gOutSum -= stackIn.g;\n            _bOutSum -= stackIn.b;\n            _aOutSum -= stackIn.a;\n            _p2 = _x + ((_p2 = _y + radiusPlus1) < heightMinus1 ? _p2 : heightMinus1) * width << 2;\n            _rSum += _rInSum += stackIn.r = pixels[_p2];\n            _gSum += _gInSum += stackIn.g = pixels[_p2 + 1];\n            _bSum += _bInSum += stackIn.b = pixels[_p2 + 2];\n            _aSum += _aInSum += stackIn.a = pixels[_p2 + 3];\n            stackIn = stackIn.next;\n            _rOutSum += _pr = stackOut.r;\n            _gOutSum += _pg = stackOut.g;\n            _bOutSum += _pb = stackOut.b;\n            _aOutSum += _pa = stackOut.a;\n            _rInSum -= _pr;\n            _gInSum -= _pg;\n            _bInSum -= _pb;\n            _aInSum -= _pa;\n            stackOut = stackOut.next;\n            yi += width;\n        }\n    }\n    return imageData;\n}\n/**\n * @param {HTMLCanvasElement} canvas\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @param {Float} radius\n * @returns {undefined}\n */ function processCanvasRGB(canvas, topX, topY, width, height, radius) {\n    if (isNaN(radius) || radius < 1) {\n        return;\n    }\n    radius |= 0;\n    var imageData = getImageDataFromCanvas(canvas, topX, topY, width, height);\n    imageData = processImageDataRGB(imageData, topX, topY, width, height, radius);\n    canvas.getContext(\"2d\").putImageData(imageData, topX, topY);\n}\n/**\n * @param {ImageData} imageData\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @param {Float} radius\n * @returns {ImageData}\n */ function processImageDataRGB(imageData, topX, topY, width, height, radius) {\n    var pixels = imageData.data;\n    var div = 2 * radius + 1; // const w4 = width << 2;\n    var widthMinus1 = width - 1;\n    var heightMinus1 = height - 1;\n    var radiusPlus1 = radius + 1;\n    var sumFactor = radiusPlus1 * (radiusPlus1 + 1) / 2;\n    var stackStart = new BlurStack();\n    var stack = stackStart;\n    var stackEnd;\n    for(var i = 1; i < div; i++){\n        stack = stack.next = new BlurStack();\n        if (i === radiusPlus1) {\n            stackEnd = stack;\n        }\n    }\n    stack.next = stackStart;\n    var stackIn = null;\n    var stackOut = null;\n    var mulSum = mulTable[radius];\n    var shgSum = shgTable[radius];\n    var p, rbs;\n    var yw = 0, yi = 0;\n    for(var y = 0; y < height; y++){\n        var pr = pixels[yi], pg = pixels[yi + 1], pb = pixels[yi + 2], rOutSum = radiusPlus1 * pr, gOutSum = radiusPlus1 * pg, bOutSum = radiusPlus1 * pb, rSum = sumFactor * pr, gSum = sumFactor * pg, bSum = sumFactor * pb;\n        stack = stackStart;\n        for(var _i5 = 0; _i5 < radiusPlus1; _i5++){\n            stack.r = pr;\n            stack.g = pg;\n            stack.b = pb;\n            stack = stack.next;\n        }\n        var rInSum = 0, gInSum = 0, bInSum = 0;\n        for(var _i6 = 1; _i6 < radiusPlus1; _i6++){\n            p = yi + ((widthMinus1 < _i6 ? widthMinus1 : _i6) << 2);\n            rSum += (stack.r = pr = pixels[p]) * (rbs = radiusPlus1 - _i6);\n            gSum += (stack.g = pg = pixels[p + 1]) * rbs;\n            bSum += (stack.b = pb = pixels[p + 2]) * rbs;\n            rInSum += pr;\n            gInSum += pg;\n            bInSum += pb;\n            stack = stack.next;\n        }\n        stackIn = stackStart;\n        stackOut = stackEnd;\n        for(var x = 0; x < width; x++){\n            pixels[yi] = rSum * mulSum >>> shgSum;\n            pixels[yi + 1] = gSum * mulSum >>> shgSum;\n            pixels[yi + 2] = bSum * mulSum >>> shgSum;\n            rSum -= rOutSum;\n            gSum -= gOutSum;\n            bSum -= bOutSum;\n            rOutSum -= stackIn.r;\n            gOutSum -= stackIn.g;\n            bOutSum -= stackIn.b;\n            p = yw + ((p = x + radius + 1) < widthMinus1 ? p : widthMinus1) << 2;\n            rInSum += stackIn.r = pixels[p];\n            gInSum += stackIn.g = pixels[p + 1];\n            bInSum += stackIn.b = pixels[p + 2];\n            rSum += rInSum;\n            gSum += gInSum;\n            bSum += bInSum;\n            stackIn = stackIn.next;\n            rOutSum += pr = stackOut.r;\n            gOutSum += pg = stackOut.g;\n            bOutSum += pb = stackOut.b;\n            rInSum -= pr;\n            gInSum -= pg;\n            bInSum -= pb;\n            stackOut = stackOut.next;\n            yi += 4;\n        }\n        yw += width;\n    }\n    for(var _x2 = 0; _x2 < width; _x2++){\n        yi = _x2 << 2;\n        var _pr2 = pixels[yi], _pg2 = pixels[yi + 1], _pb2 = pixels[yi + 2], _rOutSum2 = radiusPlus1 * _pr2, _gOutSum2 = radiusPlus1 * _pg2, _bOutSum2 = radiusPlus1 * _pb2, _rSum2 = sumFactor * _pr2, _gSum2 = sumFactor * _pg2, _bSum2 = sumFactor * _pb2;\n        stack = stackStart;\n        for(var _i7 = 0; _i7 < radiusPlus1; _i7++){\n            stack.r = _pr2;\n            stack.g = _pg2;\n            stack.b = _pb2;\n            stack = stack.next;\n        }\n        var _rInSum2 = 0, _gInSum2 = 0, _bInSum2 = 0;\n        for(var _i8 = 1, yp = width; _i8 <= radius; _i8++){\n            yi = yp + _x2 << 2;\n            _rSum2 += (stack.r = _pr2 = pixels[yi]) * (rbs = radiusPlus1 - _i8);\n            _gSum2 += (stack.g = _pg2 = pixels[yi + 1]) * rbs;\n            _bSum2 += (stack.b = _pb2 = pixels[yi + 2]) * rbs;\n            _rInSum2 += _pr2;\n            _gInSum2 += _pg2;\n            _bInSum2 += _pb2;\n            stack = stack.next;\n            if (_i8 < heightMinus1) {\n                yp += width;\n            }\n        }\n        yi = _x2;\n        stackIn = stackStart;\n        stackOut = stackEnd;\n        for(var _y2 = 0; _y2 < height; _y2++){\n            p = yi << 2;\n            pixels[p] = _rSum2 * mulSum >>> shgSum;\n            pixels[p + 1] = _gSum2 * mulSum >>> shgSum;\n            pixels[p + 2] = _bSum2 * mulSum >>> shgSum;\n            _rSum2 -= _rOutSum2;\n            _gSum2 -= _gOutSum2;\n            _bSum2 -= _bOutSum2;\n            _rOutSum2 -= stackIn.r;\n            _gOutSum2 -= stackIn.g;\n            _bOutSum2 -= stackIn.b;\n            p = _x2 + ((p = _y2 + radiusPlus1) < heightMinus1 ? p : heightMinus1) * width << 2;\n            _rSum2 += _rInSum2 += stackIn.r = pixels[p];\n            _gSum2 += _gInSum2 += stackIn.g = pixels[p + 1];\n            _bSum2 += _bInSum2 += stackIn.b = pixels[p + 2];\n            stackIn = stackIn.next;\n            _rOutSum2 += _pr2 = stackOut.r;\n            _gOutSum2 += _pg2 = stackOut.g;\n            _bOutSum2 += _pb2 = stackOut.b;\n            _rInSum2 -= _pr2;\n            _gInSum2 -= _pg2;\n            _bInSum2 -= _pb2;\n            stackOut = stackOut.next;\n            yi += width;\n        }\n    }\n    return imageData;\n}\n/**\n *\n */ var BlurStack = /**\n * Set properties.\n */ function BlurStack() {\n    _classCallCheck(this, BlurStack);\n    this.r = 0;\n    this.g = 0;\n    this.b = 0;\n    this.a = 0;\n    this.next = null;\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/stackblur-canvas/dist/stackblur-es.js\n");

/***/ })

};
;