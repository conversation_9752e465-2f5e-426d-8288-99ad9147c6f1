"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/yup";
exports.ids = ["vendor-chunks/yup"];
exports.modules = {

/***/ "(ssr)/./node_modules/yup/index.esm.js":
/*!***************************************!*\
  !*** ./node_modules/yup/index.esm.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArraySchema: () => (/* binding */ ArraySchema),\n/* harmony export */   BooleanSchema: () => (/* binding */ BooleanSchema),\n/* harmony export */   DateSchema: () => (/* binding */ DateSchema),\n/* harmony export */   LazySchema: () => (/* binding */ Lazy),\n/* harmony export */   MixedSchema: () => (/* binding */ MixedSchema),\n/* harmony export */   NumberSchema: () => (/* binding */ NumberSchema),\n/* harmony export */   ObjectSchema: () => (/* binding */ ObjectSchema),\n/* harmony export */   Schema: () => (/* binding */ Schema),\n/* harmony export */   StringSchema: () => (/* binding */ StringSchema),\n/* harmony export */   TupleSchema: () => (/* binding */ TupleSchema),\n/* harmony export */   ValidationError: () => (/* binding */ ValidationError),\n/* harmony export */   addMethod: () => (/* binding */ addMethod),\n/* harmony export */   array: () => (/* binding */ create$2),\n/* harmony export */   bool: () => (/* binding */ create$7),\n/* harmony export */   boolean: () => (/* binding */ create$7),\n/* harmony export */   date: () => (/* binding */ create$4),\n/* harmony export */   defaultLocale: () => (/* binding */ locale),\n/* harmony export */   getIn: () => (/* binding */ getIn),\n/* harmony export */   isSchema: () => (/* binding */ isSchema),\n/* harmony export */   lazy: () => (/* binding */ create),\n/* harmony export */   mixed: () => (/* binding */ create$8),\n/* harmony export */   number: () => (/* binding */ create$5),\n/* harmony export */   object: () => (/* binding */ create$3),\n/* harmony export */   printValue: () => (/* binding */ printValue),\n/* harmony export */   reach: () => (/* binding */ reach),\n/* harmony export */   ref: () => (/* binding */ create$9),\n/* harmony export */   setLocale: () => (/* binding */ setLocale),\n/* harmony export */   string: () => (/* binding */ create$6),\n/* harmony export */   tuple: () => (/* binding */ create$1)\n/* harmony export */ });\n/* harmony import */ var property_expr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! property-expr */ \"(ssr)/./node_modules/property-expr/index.js\");\n/* harmony import */ var property_expr__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(property_expr__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var tiny_case__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tiny-case */ \"(ssr)/./node_modules/tiny-case/index.js\");\n/* harmony import */ var tiny_case__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(tiny_case__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var toposort__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! toposort */ \"(ssr)/./node_modules/toposort/index.js\");\n/* harmony import */ var toposort__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(toposort__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst toString = Object.prototype.toString;\nconst errorToString = Error.prototype.toString;\nconst regExpToString = RegExp.prototype.toString;\nconst symbolToString = typeof Symbol !== \"undefined\" ? Symbol.prototype.toString : ()=>\"\";\nconst SYMBOL_REGEXP = /^Symbol\\((.*)\\)(.*)$/;\nfunction printNumber(val) {\n    if (val != +val) return \"NaN\";\n    const isNegativeZero = val === 0 && 1 / val < 0;\n    return isNegativeZero ? \"-0\" : \"\" + val;\n}\nfunction printSimpleValue(val, quoteStrings = false) {\n    if (val == null || val === true || val === false) return \"\" + val;\n    const typeOf = typeof val;\n    if (typeOf === \"number\") return printNumber(val);\n    if (typeOf === \"string\") return quoteStrings ? `\"${val}\"` : val;\n    if (typeOf === \"function\") return \"[Function \" + (val.name || \"anonymous\") + \"]\";\n    if (typeOf === \"symbol\") return symbolToString.call(val).replace(SYMBOL_REGEXP, \"Symbol($1)\");\n    const tag = toString.call(val).slice(8, -1);\n    if (tag === \"Date\") return isNaN(val.getTime()) ? \"\" + val : val.toISOString(val);\n    if (tag === \"Error\" || val instanceof Error) return \"[\" + errorToString.call(val) + \"]\";\n    if (tag === \"RegExp\") return regExpToString.call(val);\n    return null;\n}\nfunction printValue(value, quoteStrings) {\n    let result = printSimpleValue(value, quoteStrings);\n    if (result !== null) return result;\n    return JSON.stringify(value, function(key, value) {\n        let result = printSimpleValue(this[key], quoteStrings);\n        if (result !== null) return result;\n        return value;\n    }, 2);\n}\nfunction toArray(value) {\n    return value == null ? [] : [].concat(value);\n}\nlet _Symbol$toStringTag, _Symbol$hasInstance, _Symbol$toStringTag2;\nlet strReg = /\\$\\{\\s*(\\w+)\\s*\\}/g;\n_Symbol$toStringTag = Symbol.toStringTag;\nclass ValidationErrorNoStack {\n    constructor(errorOrErrors, value, field, type){\n        this.name = void 0;\n        this.message = void 0;\n        this.value = void 0;\n        this.path = void 0;\n        this.type = void 0;\n        this.params = void 0;\n        this.errors = void 0;\n        this.inner = void 0;\n        this[_Symbol$toStringTag] = \"Error\";\n        this.name = \"ValidationError\";\n        this.value = value;\n        this.path = field;\n        this.type = type;\n        this.errors = [];\n        this.inner = [];\n        toArray(errorOrErrors).forEach((err)=>{\n            if (ValidationError.isError(err)) {\n                this.errors.push(...err.errors);\n                const innerErrors = err.inner.length ? err.inner : [\n                    err\n                ];\n                this.inner.push(...innerErrors);\n            } else {\n                this.errors.push(err);\n            }\n        });\n        this.message = this.errors.length > 1 ? `${this.errors.length} errors occurred` : this.errors[0];\n    }\n}\n_Symbol$hasInstance = Symbol.hasInstance;\n_Symbol$toStringTag2 = Symbol.toStringTag;\nclass ValidationError extends Error {\n    static formatError(message, params) {\n        // Attempt to make the path more friendly for error message interpolation.\n        const path = params.label || params.path || \"this\";\n        // Store the original path under `originalPath` so it isn't lost to custom\n        // message functions; e.g., ones provided in `setLocale()` calls.\n        params = Object.assign({}, params, {\n            path,\n            originalPath: params.path\n        });\n        if (typeof message === \"string\") return message.replace(strReg, (_, key)=>printValue(params[key]));\n        if (typeof message === \"function\") return message(params);\n        return message;\n    }\n    static isError(err) {\n        return err && err.name === \"ValidationError\";\n    }\n    constructor(errorOrErrors, value, field, type, disableStack){\n        const errorNoStack = new ValidationErrorNoStack(errorOrErrors, value, field, type);\n        if (disableStack) {\n            return errorNoStack;\n        }\n        super();\n        this.value = void 0;\n        this.path = void 0;\n        this.type = void 0;\n        this.params = void 0;\n        this.errors = [];\n        this.inner = [];\n        this[_Symbol$toStringTag2] = \"Error\";\n        this.name = errorNoStack.name;\n        this.message = errorNoStack.message;\n        this.type = errorNoStack.type;\n        this.value = errorNoStack.value;\n        this.path = errorNoStack.path;\n        this.errors = errorNoStack.errors;\n        this.inner = errorNoStack.inner;\n        if (Error.captureStackTrace) {\n            Error.captureStackTrace(this, ValidationError);\n        }\n    }\n    static [_Symbol$hasInstance](inst) {\n        return ValidationErrorNoStack[Symbol.hasInstance](inst) || super[Symbol.hasInstance](inst);\n    }\n}\nlet mixed = {\n    default: \"${path} is invalid\",\n    required: \"${path} is a required field\",\n    defined: \"${path} must be defined\",\n    notNull: \"${path} cannot be null\",\n    oneOf: \"${path} must be one of the following values: ${values}\",\n    notOneOf: \"${path} must not be one of the following values: ${values}\",\n    notType: ({ path, type, value, originalValue })=>{\n        const castMsg = originalValue != null && originalValue !== value ? ` (cast from the value \\`${printValue(originalValue, true)}\\`).` : \".\";\n        return type !== \"mixed\" ? `${path} must be a \\`${type}\\` type, ` + `but the final value was: \\`${printValue(value, true)}\\`` + castMsg : `${path} must match the configured type. ` + `The validated value was: \\`${printValue(value, true)}\\`` + castMsg;\n    }\n};\nlet string = {\n    length: \"${path} must be exactly ${length} characters\",\n    min: \"${path} must be at least ${min} characters\",\n    max: \"${path} must be at most ${max} characters\",\n    matches: '${path} must match the following: \"${regex}\"',\n    email: \"${path} must be a valid email\",\n    url: \"${path} must be a valid URL\",\n    uuid: \"${path} must be a valid UUID\",\n    datetime: \"${path} must be a valid ISO date-time\",\n    datetime_precision: \"${path} must be a valid ISO date-time with a sub-second precision of exactly ${precision} digits\",\n    datetime_offset: '${path} must be a valid ISO date-time with UTC \"Z\" timezone',\n    trim: \"${path} must be a trimmed string\",\n    lowercase: \"${path} must be a lowercase string\",\n    uppercase: \"${path} must be a upper case string\"\n};\nlet number = {\n    min: \"${path} must be greater than or equal to ${min}\",\n    max: \"${path} must be less than or equal to ${max}\",\n    lessThan: \"${path} must be less than ${less}\",\n    moreThan: \"${path} must be greater than ${more}\",\n    positive: \"${path} must be a positive number\",\n    negative: \"${path} must be a negative number\",\n    integer: \"${path} must be an integer\"\n};\nlet date = {\n    min: \"${path} field must be later than ${min}\",\n    max: \"${path} field must be at earlier than ${max}\"\n};\nlet boolean = {\n    isValue: \"${path} field must be ${value}\"\n};\nlet object = {\n    noUnknown: \"${path} field has unspecified keys: ${unknown}\",\n    exact: \"${path} object contains unknown properties: ${properties}\"\n};\nlet array = {\n    min: \"${path} field must have at least ${min} items\",\n    max: \"${path} field must have less than or equal to ${max} items\",\n    length: \"${path} must have ${length} items\"\n};\nlet tuple = {\n    notType: (params)=>{\n        const { path, value, spec } = params;\n        const typeLen = spec.types.length;\n        if (Array.isArray(value)) {\n            if (value.length < typeLen) return `${path} tuple value has too few items, expected a length of ${typeLen} but got ${value.length} for value: \\`${printValue(value, true)}\\``;\n            if (value.length > typeLen) return `${path} tuple value has too many items, expected a length of ${typeLen} but got ${value.length} for value: \\`${printValue(value, true)}\\``;\n        }\n        return ValidationError.formatError(mixed.notType, params);\n    }\n};\nvar locale = Object.assign(Object.create(null), {\n    mixed,\n    string,\n    number,\n    date,\n    object,\n    array,\n    boolean,\n    tuple\n});\nconst isSchema = (obj)=>obj && obj.__isYupSchema__;\nclass Condition {\n    static fromOptions(refs, config) {\n        if (!config.then && !config.otherwise) throw new TypeError(\"either `then:` or `otherwise:` is required for `when()` conditions\");\n        let { is, then, otherwise } = config;\n        let check = typeof is === \"function\" ? is : (...values)=>values.every((value)=>value === is);\n        return new Condition(refs, (values, schema)=>{\n            var _branch;\n            let branch = check(...values) ? then : otherwise;\n            return (_branch = branch == null ? void 0 : branch(schema)) != null ? _branch : schema;\n        });\n    }\n    constructor(refs, builder){\n        this.fn = void 0;\n        this.refs = refs;\n        this.refs = refs;\n        this.fn = builder;\n    }\n    resolve(base, options) {\n        let values = this.refs.map((ref)=>// TODO: ? operator here?\n            ref.getValue(options == null ? void 0 : options.value, options == null ? void 0 : options.parent, options == null ? void 0 : options.context));\n        let schema = this.fn(values, base, options);\n        if (schema === undefined || // @ts-ignore this can be base\n        schema === base) {\n            return base;\n        }\n        if (!isSchema(schema)) throw new TypeError(\"conditions must return a schema object\");\n        return schema.resolve(options);\n    }\n}\nconst prefixes = {\n    context: \"$\",\n    value: \".\"\n};\nfunction create$9(key, options) {\n    return new Reference(key, options);\n}\nclass Reference {\n    constructor(key, options = {}){\n        this.key = void 0;\n        this.isContext = void 0;\n        this.isValue = void 0;\n        this.isSibling = void 0;\n        this.path = void 0;\n        this.getter = void 0;\n        this.map = void 0;\n        if (typeof key !== \"string\") throw new TypeError(\"ref must be a string, got: \" + key);\n        this.key = key.trim();\n        if (key === \"\") throw new TypeError(\"ref must be a non-empty string\");\n        this.isContext = this.key[0] === prefixes.context;\n        this.isValue = this.key[0] === prefixes.value;\n        this.isSibling = !this.isContext && !this.isValue;\n        let prefix = this.isContext ? prefixes.context : this.isValue ? prefixes.value : \"\";\n        this.path = this.key.slice(prefix.length);\n        this.getter = this.path && (0,property_expr__WEBPACK_IMPORTED_MODULE_0__.getter)(this.path, true);\n        this.map = options.map;\n    }\n    getValue(value, parent, context) {\n        let result = this.isContext ? context : this.isValue ? value : parent;\n        if (this.getter) result = this.getter(result || {});\n        if (this.map) result = this.map(result);\n        return result;\n    }\n    /**\n   *\n   * @param {*} value\n   * @param {Object} options\n   * @param {Object=} options.context\n   * @param {Object=} options.parent\n   */ cast(value, options) {\n        return this.getValue(value, options == null ? void 0 : options.parent, options == null ? void 0 : options.context);\n    }\n    resolve() {\n        return this;\n    }\n    describe() {\n        return {\n            type: \"ref\",\n            key: this.key\n        };\n    }\n    toString() {\n        return `Ref(${this.key})`;\n    }\n    static isRef(value) {\n        return value && value.__isYupRef;\n    }\n}\n// @ts-ignore\nReference.prototype.__isYupRef = true;\nconst isAbsent = (value)=>value == null;\nfunction createValidation(config) {\n    function validate({ value, path = \"\", options, originalValue, schema }, panic, next) {\n        const { name, test, params, message, skipAbsent } = config;\n        let { parent, context, abortEarly = schema.spec.abortEarly, disableStackTrace = schema.spec.disableStackTrace } = options;\n        function resolve(item) {\n            return Reference.isRef(item) ? item.getValue(value, parent, context) : item;\n        }\n        function createError(overrides = {}) {\n            const nextParams = Object.assign({\n                value,\n                originalValue,\n                label: schema.spec.label,\n                path: overrides.path || path,\n                spec: schema.spec,\n                disableStackTrace: overrides.disableStackTrace || disableStackTrace\n            }, params, overrides.params);\n            for (const key of Object.keys(nextParams))nextParams[key] = resolve(nextParams[key]);\n            const error = new ValidationError(ValidationError.formatError(overrides.message || message, nextParams), value, nextParams.path, overrides.type || name, nextParams.disableStackTrace);\n            error.params = nextParams;\n            return error;\n        }\n        const invalid = abortEarly ? panic : next;\n        let ctx = {\n            path,\n            parent,\n            type: name,\n            from: options.from,\n            createError,\n            resolve,\n            options,\n            originalValue,\n            schema\n        };\n        const handleResult = (validOrError)=>{\n            if (ValidationError.isError(validOrError)) invalid(validOrError);\n            else if (!validOrError) invalid(createError());\n            else next(null);\n        };\n        const handleError = (err)=>{\n            if (ValidationError.isError(err)) invalid(err);\n            else panic(err);\n        };\n        const shouldSkip = skipAbsent && isAbsent(value);\n        if (shouldSkip) {\n            return handleResult(true);\n        }\n        let result;\n        try {\n            var _result;\n            result = test.call(ctx, value, ctx);\n            if (typeof ((_result = result) == null ? void 0 : _result.then) === \"function\") {\n                if (options.sync) {\n                    throw new Error(`Validation test of type: \"${ctx.type}\" returned a Promise during a synchronous validate. ` + `This test will finish after the validate call has returned`);\n                }\n                return Promise.resolve(result).then(handleResult, handleError);\n            }\n        } catch (err) {\n            handleError(err);\n            return;\n        }\n        handleResult(result);\n    }\n    validate.OPTIONS = config;\n    return validate;\n}\nfunction getIn(schema, path, value, context = value) {\n    let parent, lastPart, lastPartDebug;\n    // root path: ''\n    if (!path) return {\n        parent,\n        parentPath: path,\n        schema\n    };\n    (0,property_expr__WEBPACK_IMPORTED_MODULE_0__.forEach)(path, (_part, isBracket, isArray)=>{\n        let part = isBracket ? _part.slice(1, _part.length - 1) : _part;\n        schema = schema.resolve({\n            context,\n            parent,\n            value\n        });\n        let isTuple = schema.type === \"tuple\";\n        let idx = isArray ? parseInt(part, 10) : 0;\n        if (schema.innerType || isTuple) {\n            if (isTuple && !isArray) throw new Error(`Yup.reach cannot implicitly index into a tuple type. the path part \"${lastPartDebug}\" must contain an index to the tuple element, e.g. \"${lastPartDebug}[0]\"`);\n            if (value && idx >= value.length) {\n                throw new Error(`Yup.reach cannot resolve an array item at index: ${_part}, in the path: ${path}. ` + `because there is no value at that index. `);\n            }\n            parent = value;\n            value = value && value[idx];\n            schema = isTuple ? schema.spec.types[idx] : schema.innerType;\n        }\n        // sometimes the array index part of a path doesn't exist: \"nested.arr.child\"\n        // in these cases the current part is the next schema and should be processed\n        // in this iteration. For cases where the index signature is included this\n        // check will fail and we'll handle the `child` part on the next iteration like normal\n        if (!isArray) {\n            if (!schema.fields || !schema.fields[part]) throw new Error(`The schema does not contain the path: ${path}. ` + `(failed at: ${lastPartDebug} which is a type: \"${schema.type}\")`);\n            parent = value;\n            value = value && value[part];\n            schema = schema.fields[part];\n        }\n        lastPart = part;\n        lastPartDebug = isBracket ? \"[\" + _part + \"]\" : \".\" + _part;\n    });\n    return {\n        schema,\n        parent,\n        parentPath: lastPart\n    };\n}\nfunction reach(obj, path, value, context) {\n    return getIn(obj, path, value, context).schema;\n}\nclass ReferenceSet extends Set {\n    describe() {\n        const description = [];\n        for (const item of this.values()){\n            description.push(Reference.isRef(item) ? item.describe() : item);\n        }\n        return description;\n    }\n    resolveAll(resolve) {\n        let result = [];\n        for (const item of this.values()){\n            result.push(resolve(item));\n        }\n        return result;\n    }\n    clone() {\n        return new ReferenceSet(this.values());\n    }\n    merge(newItems, removeItems) {\n        const next = this.clone();\n        newItems.forEach((value)=>next.add(value));\n        removeItems.forEach((value)=>next.delete(value));\n        return next;\n    }\n}\n// tweaked from https://github.com/Kelin2025/nanoclone/blob/0abeb7635bda9b68ef2277093f76dbe3bf3948e1/src/index.js\nfunction clone(src, seen = new Map()) {\n    if (isSchema(src) || !src || typeof src !== \"object\") return src;\n    if (seen.has(src)) return seen.get(src);\n    let copy;\n    if (src instanceof Date) {\n        // Date\n        copy = new Date(src.getTime());\n        seen.set(src, copy);\n    } else if (src instanceof RegExp) {\n        // RegExp\n        copy = new RegExp(src);\n        seen.set(src, copy);\n    } else if (Array.isArray(src)) {\n        // Array\n        copy = new Array(src.length);\n        seen.set(src, copy);\n        for(let i = 0; i < src.length; i++)copy[i] = clone(src[i], seen);\n    } else if (src instanceof Map) {\n        // Map\n        copy = new Map();\n        seen.set(src, copy);\n        for (const [k, v] of src.entries())copy.set(k, clone(v, seen));\n    } else if (src instanceof Set) {\n        // Set\n        copy = new Set();\n        seen.set(src, copy);\n        for (const v of src)copy.add(clone(v, seen));\n    } else if (src instanceof Object) {\n        // Object\n        copy = {};\n        seen.set(src, copy);\n        for (const [k, v] of Object.entries(src))copy[k] = clone(v, seen);\n    } else {\n        throw Error(`Unable to clone ${src}`);\n    }\n    return copy;\n}\n// If `CustomSchemaMeta` isn't extended with any keys, we'll fall back to a\n// loose Record definition allowing free form usage.\nclass Schema {\n    constructor(options){\n        this.type = void 0;\n        this.deps = [];\n        this.tests = void 0;\n        this.transforms = void 0;\n        this.conditions = [];\n        this._mutate = void 0;\n        this.internalTests = {};\n        this._whitelist = new ReferenceSet();\n        this._blacklist = new ReferenceSet();\n        this.exclusiveTests = Object.create(null);\n        this._typeCheck = void 0;\n        this.spec = void 0;\n        this.tests = [];\n        this.transforms = [];\n        this.withMutation(()=>{\n            this.typeError(mixed.notType);\n        });\n        this.type = options.type;\n        this._typeCheck = options.check;\n        this.spec = Object.assign({\n            strip: false,\n            strict: false,\n            abortEarly: true,\n            recursive: true,\n            disableStackTrace: false,\n            nullable: false,\n            optional: true,\n            coerce: true\n        }, options == null ? void 0 : options.spec);\n        this.withMutation((s)=>{\n            s.nonNullable();\n        });\n    }\n    // TODO: remove\n    get _type() {\n        return this.type;\n    }\n    clone(spec) {\n        if (this._mutate) {\n            if (spec) Object.assign(this.spec, spec);\n            return this;\n        }\n        // if the nested value is a schema we can skip cloning, since\n        // they are already immutable\n        const next = Object.create(Object.getPrototypeOf(this));\n        // @ts-expect-error this is readonly\n        next.type = this.type;\n        next._typeCheck = this._typeCheck;\n        next._whitelist = this._whitelist.clone();\n        next._blacklist = this._blacklist.clone();\n        next.internalTests = Object.assign({}, this.internalTests);\n        next.exclusiveTests = Object.assign({}, this.exclusiveTests);\n        // @ts-expect-error this is readonly\n        next.deps = [\n            ...this.deps\n        ];\n        next.conditions = [\n            ...this.conditions\n        ];\n        next.tests = [\n            ...this.tests\n        ];\n        next.transforms = [\n            ...this.transforms\n        ];\n        next.spec = clone(Object.assign({}, this.spec, spec));\n        return next;\n    }\n    label(label) {\n        let next = this.clone();\n        next.spec.label = label;\n        return next;\n    }\n    meta(...args) {\n        if (args.length === 0) return this.spec.meta;\n        let next = this.clone();\n        next.spec.meta = Object.assign(next.spec.meta || {}, args[0]);\n        return next;\n    }\n    withMutation(fn) {\n        let before = this._mutate;\n        this._mutate = true;\n        let result = fn(this);\n        this._mutate = before;\n        return result;\n    }\n    concat(schema) {\n        if (!schema || schema === this) return this;\n        if (schema.type !== this.type && this.type !== \"mixed\") throw new TypeError(`You cannot \\`concat()\\` schema's of different types: ${this.type} and ${schema.type}`);\n        let base = this;\n        let combined = schema.clone();\n        const mergedSpec = Object.assign({}, base.spec, combined.spec);\n        combined.spec = mergedSpec;\n        combined.internalTests = Object.assign({}, base.internalTests, combined.internalTests);\n        // manually merge the blacklist/whitelist (the other `schema` takes\n        // precedence in case of conflicts)\n        combined._whitelist = base._whitelist.merge(schema._whitelist, schema._blacklist);\n        combined._blacklist = base._blacklist.merge(schema._blacklist, schema._whitelist);\n        // start with the current tests\n        combined.tests = base.tests;\n        combined.exclusiveTests = base.exclusiveTests;\n        // manually add the new tests to ensure\n        // the deduping logic is consistent\n        combined.withMutation((next)=>{\n            schema.tests.forEach((fn)=>{\n                next.test(fn.OPTIONS);\n            });\n        });\n        combined.transforms = [\n            ...base.transforms,\n            ...combined.transforms\n        ];\n        return combined;\n    }\n    isType(v) {\n        if (v == null) {\n            if (this.spec.nullable && v === null) return true;\n            if (this.spec.optional && v === undefined) return true;\n            return false;\n        }\n        return this._typeCheck(v);\n    }\n    resolve(options) {\n        let schema = this;\n        if (schema.conditions.length) {\n            let conditions = schema.conditions;\n            schema = schema.clone();\n            schema.conditions = [];\n            schema = conditions.reduce((prevSchema, condition)=>condition.resolve(prevSchema, options), schema);\n            schema = schema.resolve(options);\n        }\n        return schema;\n    }\n    resolveOptions(options) {\n        var _options$strict, _options$abortEarly, _options$recursive, _options$disableStack;\n        return Object.assign({}, options, {\n            from: options.from || [],\n            strict: (_options$strict = options.strict) != null ? _options$strict : this.spec.strict,\n            abortEarly: (_options$abortEarly = options.abortEarly) != null ? _options$abortEarly : this.spec.abortEarly,\n            recursive: (_options$recursive = options.recursive) != null ? _options$recursive : this.spec.recursive,\n            disableStackTrace: (_options$disableStack = options.disableStackTrace) != null ? _options$disableStack : this.spec.disableStackTrace\n        });\n    }\n    /**\n   * Run the configured transform pipeline over an input value.\n   */ cast(value, options = {}) {\n        let resolvedSchema = this.resolve(Object.assign({\n            value\n        }, options));\n        let allowOptionality = options.assert === \"ignore-optionality\";\n        let result = resolvedSchema._cast(value, options);\n        if (options.assert !== false && !resolvedSchema.isType(result)) {\n            if (allowOptionality && isAbsent(result)) {\n                return result;\n            }\n            let formattedValue = printValue(value);\n            let formattedResult = printValue(result);\n            throw new TypeError(`The value of ${options.path || \"field\"} could not be cast to a value ` + `that satisfies the schema type: \"${resolvedSchema.type}\". \\n\\n` + `attempted value: ${formattedValue} \\n` + (formattedResult !== formattedValue ? `result of cast: ${formattedResult}` : \"\"));\n        }\n        return result;\n    }\n    _cast(rawValue, options) {\n        let value = rawValue === undefined ? rawValue : this.transforms.reduce((prevValue, fn)=>fn.call(this, prevValue, rawValue, this), rawValue);\n        if (value === undefined) {\n            value = this.getDefault(options);\n        }\n        return value;\n    }\n    _validate(_value, options = {}, panic, next) {\n        let { path, originalValue = _value, strict = this.spec.strict } = options;\n        let value = _value;\n        if (!strict) {\n            value = this._cast(value, Object.assign({\n                assert: false\n            }, options));\n        }\n        let initialTests = [];\n        for (let test of Object.values(this.internalTests)){\n            if (test) initialTests.push(test);\n        }\n        this.runTests({\n            path,\n            value,\n            originalValue,\n            options,\n            tests: initialTests\n        }, panic, (initialErrors)=>{\n            // even if we aren't ending early we can't proceed further if the types aren't correct\n            if (initialErrors.length) {\n                return next(initialErrors, value);\n            }\n            this.runTests({\n                path,\n                value,\n                originalValue,\n                options,\n                tests: this.tests\n            }, panic, next);\n        });\n    }\n    /**\n   * Executes a set of validations, either schema, produced Tests or a nested\n   * schema validate result.\n   */ runTests(runOptions, panic, next) {\n        let fired = false;\n        let { tests, value, originalValue, path, options } = runOptions;\n        let panicOnce = (arg)=>{\n            if (fired) return;\n            fired = true;\n            panic(arg, value);\n        };\n        let nextOnce = (arg)=>{\n            if (fired) return;\n            fired = true;\n            next(arg, value);\n        };\n        let count = tests.length;\n        let nestedErrors = [];\n        if (!count) return nextOnce([]);\n        let args = {\n            value,\n            originalValue,\n            path,\n            options,\n            schema: this\n        };\n        for(let i = 0; i < tests.length; i++){\n            const test = tests[i];\n            test(args, panicOnce, function finishTestRun(err) {\n                if (err) {\n                    Array.isArray(err) ? nestedErrors.push(...err) : nestedErrors.push(err);\n                }\n                if (--count <= 0) {\n                    nextOnce(nestedErrors);\n                }\n            });\n        }\n    }\n    asNestedTest({ key, index, parent, parentPath, originalParent, options }) {\n        const k = key != null ? key : index;\n        if (k == null) {\n            throw TypeError(\"Must include `key` or `index` for nested validations\");\n        }\n        const isIndex = typeof k === \"number\";\n        let value = parent[k];\n        const testOptions = Object.assign({}, options, {\n            // Nested validations fields are always strict:\n            //    1. parent isn't strict so the casting will also have cast inner values\n            //    2. parent is strict in which case the nested values weren't cast either\n            strict: true,\n            parent,\n            value,\n            originalValue: originalParent[k],\n            // FIXME: tests depend on `index` being passed around deeply,\n            //   we should not let the options.key/index bleed through\n            key: undefined,\n            // index: undefined,\n            [isIndex ? \"index\" : \"key\"]: k,\n            path: isIndex || k.includes(\".\") ? `${parentPath || \"\"}[${isIndex ? k : `\"${k}\"`}]` : (parentPath ? `${parentPath}.` : \"\") + key\n        });\n        return (_, panic, next)=>this.resolve(testOptions)._validate(value, testOptions, panic, next);\n    }\n    validate(value, options) {\n        var _options$disableStack2;\n        let schema = this.resolve(Object.assign({}, options, {\n            value\n        }));\n        let disableStackTrace = (_options$disableStack2 = options == null ? void 0 : options.disableStackTrace) != null ? _options$disableStack2 : schema.spec.disableStackTrace;\n        return new Promise((resolve, reject)=>schema._validate(value, options, (error, parsed)=>{\n                if (ValidationError.isError(error)) error.value = parsed;\n                reject(error);\n            }, (errors, validated)=>{\n                if (errors.length) reject(new ValidationError(errors, validated, undefined, undefined, disableStackTrace));\n                else resolve(validated);\n            }));\n    }\n    validateSync(value, options) {\n        var _options$disableStack3;\n        let schema = this.resolve(Object.assign({}, options, {\n            value\n        }));\n        let result;\n        let disableStackTrace = (_options$disableStack3 = options == null ? void 0 : options.disableStackTrace) != null ? _options$disableStack3 : schema.spec.disableStackTrace;\n        schema._validate(value, Object.assign({}, options, {\n            sync: true\n        }), (error, parsed)=>{\n            if (ValidationError.isError(error)) error.value = parsed;\n            throw error;\n        }, (errors, validated)=>{\n            if (errors.length) throw new ValidationError(errors, value, undefined, undefined, disableStackTrace);\n            result = validated;\n        });\n        return result;\n    }\n    isValid(value, options) {\n        return this.validate(value, options).then(()=>true, (err)=>{\n            if (ValidationError.isError(err)) return false;\n            throw err;\n        });\n    }\n    isValidSync(value, options) {\n        try {\n            this.validateSync(value, options);\n            return true;\n        } catch (err) {\n            if (ValidationError.isError(err)) return false;\n            throw err;\n        }\n    }\n    _getDefault(options) {\n        let defaultValue = this.spec.default;\n        if (defaultValue == null) {\n            return defaultValue;\n        }\n        return typeof defaultValue === \"function\" ? defaultValue.call(this, options) : clone(defaultValue);\n    }\n    getDefault(options) {\n        let schema = this.resolve(options || {});\n        return schema._getDefault(options);\n    }\n    default(def) {\n        if (arguments.length === 0) {\n            return this._getDefault();\n        }\n        let next = this.clone({\n            default: def\n        });\n        return next;\n    }\n    strict(isStrict = true) {\n        return this.clone({\n            strict: isStrict\n        });\n    }\n    nullability(nullable, message) {\n        const next = this.clone({\n            nullable\n        });\n        next.internalTests.nullable = createValidation({\n            message,\n            name: \"nullable\",\n            test (value) {\n                return value === null ? this.schema.spec.nullable : true;\n            }\n        });\n        return next;\n    }\n    optionality(optional, message) {\n        const next = this.clone({\n            optional\n        });\n        next.internalTests.optionality = createValidation({\n            message,\n            name: \"optionality\",\n            test (value) {\n                return value === undefined ? this.schema.spec.optional : true;\n            }\n        });\n        return next;\n    }\n    optional() {\n        return this.optionality(true);\n    }\n    defined(message = mixed.defined) {\n        return this.optionality(false, message);\n    }\n    nullable() {\n        return this.nullability(true);\n    }\n    nonNullable(message = mixed.notNull) {\n        return this.nullability(false, message);\n    }\n    required(message = mixed.required) {\n        return this.clone().withMutation((next)=>next.nonNullable(message).defined(message));\n    }\n    notRequired() {\n        return this.clone().withMutation((next)=>next.nullable().optional());\n    }\n    transform(fn) {\n        let next = this.clone();\n        next.transforms.push(fn);\n        return next;\n    }\n    /**\n   * Adds a test function to the schema's queue of tests.\n   * tests can be exclusive or non-exclusive.\n   *\n   * - exclusive tests, will replace any existing tests of the same name.\n   * - non-exclusive: can be stacked\n   *\n   * If a non-exclusive test is added to a schema with an exclusive test of the same name\n   * the exclusive test is removed and further tests of the same name will be stacked.\n   *\n   * If an exclusive test is added to a schema with non-exclusive tests of the same name\n   * the previous tests are removed and further tests of the same name will replace each other.\n   */ test(...args) {\n        let opts;\n        if (args.length === 1) {\n            if (typeof args[0] === \"function\") {\n                opts = {\n                    test: args[0]\n                };\n            } else {\n                opts = args[0];\n            }\n        } else if (args.length === 2) {\n            opts = {\n                name: args[0],\n                test: args[1]\n            };\n        } else {\n            opts = {\n                name: args[0],\n                message: args[1],\n                test: args[2]\n            };\n        }\n        if (opts.message === undefined) opts.message = mixed.default;\n        if (typeof opts.test !== \"function\") throw new TypeError(\"`test` is a required parameters\");\n        let next = this.clone();\n        let validate = createValidation(opts);\n        let isExclusive = opts.exclusive || opts.name && next.exclusiveTests[opts.name] === true;\n        if (opts.exclusive) {\n            if (!opts.name) throw new TypeError(\"Exclusive tests must provide a unique `name` identifying the test\");\n        }\n        if (opts.name) next.exclusiveTests[opts.name] = !!opts.exclusive;\n        next.tests = next.tests.filter((fn)=>{\n            if (fn.OPTIONS.name === opts.name) {\n                if (isExclusive) return false;\n                if (fn.OPTIONS.test === validate.OPTIONS.test) return false;\n            }\n            return true;\n        });\n        next.tests.push(validate);\n        return next;\n    }\n    when(keys, options) {\n        if (!Array.isArray(keys) && typeof keys !== \"string\") {\n            options = keys;\n            keys = \".\";\n        }\n        let next = this.clone();\n        let deps = toArray(keys).map((key)=>new Reference(key));\n        deps.forEach((dep)=>{\n            // @ts-ignore readonly array\n            if (dep.isSibling) next.deps.push(dep.key);\n        });\n        next.conditions.push(typeof options === \"function\" ? new Condition(deps, options) : Condition.fromOptions(deps, options));\n        return next;\n    }\n    typeError(message) {\n        let next = this.clone();\n        next.internalTests.typeError = createValidation({\n            message,\n            name: \"typeError\",\n            skipAbsent: true,\n            test (value) {\n                if (!this.schema._typeCheck(value)) return this.createError({\n                    params: {\n                        type: this.schema.type\n                    }\n                });\n                return true;\n            }\n        });\n        return next;\n    }\n    oneOf(enums, message = mixed.oneOf) {\n        let next = this.clone();\n        enums.forEach((val)=>{\n            next._whitelist.add(val);\n            next._blacklist.delete(val);\n        });\n        next.internalTests.whiteList = createValidation({\n            message,\n            name: \"oneOf\",\n            skipAbsent: true,\n            test (value) {\n                let valids = this.schema._whitelist;\n                let resolved = valids.resolveAll(this.resolve);\n                return resolved.includes(value) ? true : this.createError({\n                    params: {\n                        values: Array.from(valids).join(\", \"),\n                        resolved\n                    }\n                });\n            }\n        });\n        return next;\n    }\n    notOneOf(enums, message = mixed.notOneOf) {\n        let next = this.clone();\n        enums.forEach((val)=>{\n            next._blacklist.add(val);\n            next._whitelist.delete(val);\n        });\n        next.internalTests.blacklist = createValidation({\n            message,\n            name: \"notOneOf\",\n            test (value) {\n                let invalids = this.schema._blacklist;\n                let resolved = invalids.resolveAll(this.resolve);\n                if (resolved.includes(value)) return this.createError({\n                    params: {\n                        values: Array.from(invalids).join(\", \"),\n                        resolved\n                    }\n                });\n                return true;\n            }\n        });\n        return next;\n    }\n    strip(strip = true) {\n        let next = this.clone();\n        next.spec.strip = strip;\n        return next;\n    }\n    /**\n   * Return a serialized description of the schema including validations, flags, types etc.\n   *\n   * @param options Provide any needed context for resolving runtime schema alterations (lazy, when conditions, etc).\n   */ describe(options) {\n        const next = (options ? this.resolve(options) : this).clone();\n        const { label, meta, optional, nullable } = next.spec;\n        const description = {\n            meta,\n            label,\n            optional,\n            nullable,\n            default: next.getDefault(options),\n            type: next.type,\n            oneOf: next._whitelist.describe(),\n            notOneOf: next._blacklist.describe(),\n            tests: next.tests.map((fn)=>({\n                    name: fn.OPTIONS.name,\n                    params: fn.OPTIONS.params\n                })).filter((n, idx, list)=>list.findIndex((c)=>c.name === n.name) === idx)\n        };\n        return description;\n    }\n}\n// @ts-expect-error\nSchema.prototype.__isYupSchema__ = true;\nfor (const method of [\n    \"validate\",\n    \"validateSync\"\n])Schema.prototype[`${method}At`] = function(path, value, options = {}) {\n    const { parent, parentPath, schema } = getIn(this, path, value, options.context);\n    return schema[method](parent && parent[parentPath], Object.assign({}, options, {\n        parent,\n        path\n    }));\n};\nfor (const alias of [\n    \"equals\",\n    \"is\"\n])Schema.prototype[alias] = Schema.prototype.oneOf;\nfor (const alias of [\n    \"not\",\n    \"nope\"\n])Schema.prototype[alias] = Schema.prototype.notOneOf;\nconst returnsTrue = ()=>true;\nfunction create$8(spec) {\n    return new MixedSchema(spec);\n}\nclass MixedSchema extends Schema {\n    constructor(spec){\n        super(typeof spec === \"function\" ? {\n            type: \"mixed\",\n            check: spec\n        } : Object.assign({\n            type: \"mixed\",\n            check: returnsTrue\n        }, spec));\n    }\n}\ncreate$8.prototype = MixedSchema.prototype;\nfunction create$7() {\n    return new BooleanSchema();\n}\nclass BooleanSchema extends Schema {\n    constructor(){\n        super({\n            type: \"boolean\",\n            check (v) {\n                if (v instanceof Boolean) v = v.valueOf();\n                return typeof v === \"boolean\";\n            }\n        });\n        this.withMutation(()=>{\n            this.transform((value, _raw, ctx)=>{\n                if (ctx.spec.coerce && !ctx.isType(value)) {\n                    if (/^(true|1)$/i.test(String(value))) return true;\n                    if (/^(false|0)$/i.test(String(value))) return false;\n                }\n                return value;\n            });\n        });\n    }\n    isTrue(message = boolean.isValue) {\n        return this.test({\n            message,\n            name: \"is-value\",\n            exclusive: true,\n            params: {\n                value: \"true\"\n            },\n            test (value) {\n                return isAbsent(value) || value === true;\n            }\n        });\n    }\n    isFalse(message = boolean.isValue) {\n        return this.test({\n            message,\n            name: \"is-value\",\n            exclusive: true,\n            params: {\n                value: \"false\"\n            },\n            test (value) {\n                return isAbsent(value) || value === false;\n            }\n        });\n    }\n    default(def) {\n        return super.default(def);\n    }\n    defined(msg) {\n        return super.defined(msg);\n    }\n    optional() {\n        return super.optional();\n    }\n    required(msg) {\n        return super.required(msg);\n    }\n    notRequired() {\n        return super.notRequired();\n    }\n    nullable() {\n        return super.nullable();\n    }\n    nonNullable(msg) {\n        return super.nonNullable(msg);\n    }\n    strip(v) {\n        return super.strip(v);\n    }\n}\ncreate$7.prototype = BooleanSchema.prototype;\n/**\n * This file is a modified version of the file from the following repository:\n * Date.parse with progressive enhancement for ISO 8601 <https://github.com/csnover/js-iso8601>\n * NON-CONFORMANT EDITION.\n * © 2011 Colin Snover <http://zetafleet.com>\n * Released under MIT license.\n */ // prettier-ignore\n//                1 YYYY                2 MM        3 DD              4 HH     5 mm        6 ss           7 msec         8 Z 9 ±   10 tzHH    11 tzmm\nconst isoReg = /^(\\d{4}|[+-]\\d{6})(?:-?(\\d{2})(?:-?(\\d{2}))?)?(?:[ T]?(\\d{2}):?(\\d{2})(?::?(\\d{2})(?:[,.](\\d{1,}))?)?(?:(Z)|([+-])(\\d{2})(?::?(\\d{2}))?)?)?$/;\nfunction parseIsoDate(date) {\n    const struct = parseDateStruct(date);\n    if (!struct) return Date.parse ? Date.parse(date) : Number.NaN;\n    // timestamps without timezone identifiers should be considered local time\n    if (struct.z === undefined && struct.plusMinus === undefined) {\n        return new Date(struct.year, struct.month, struct.day, struct.hour, struct.minute, struct.second, struct.millisecond).valueOf();\n    }\n    let totalMinutesOffset = 0;\n    if (struct.z !== \"Z\" && struct.plusMinus !== undefined) {\n        totalMinutesOffset = struct.hourOffset * 60 + struct.minuteOffset;\n        if (struct.plusMinus === \"+\") totalMinutesOffset = 0 - totalMinutesOffset;\n    }\n    return Date.UTC(struct.year, struct.month, struct.day, struct.hour, struct.minute + totalMinutesOffset, struct.second, struct.millisecond);\n}\nfunction parseDateStruct(date) {\n    var _regexResult$7$length, _regexResult$;\n    const regexResult = isoReg.exec(date);\n    if (!regexResult) return null;\n    // use of toNumber() avoids NaN timestamps caused by “undefined”\n    // values being passed to Date constructor\n    return {\n        year: toNumber(regexResult[1]),\n        month: toNumber(regexResult[2], 1) - 1,\n        day: toNumber(regexResult[3], 1),\n        hour: toNumber(regexResult[4]),\n        minute: toNumber(regexResult[5]),\n        second: toNumber(regexResult[6]),\n        millisecond: regexResult[7] ? // allow arbitrary sub-second precision beyond milliseconds\n        toNumber(regexResult[7].substring(0, 3)) : 0,\n        precision: (_regexResult$7$length = (_regexResult$ = regexResult[7]) == null ? void 0 : _regexResult$.length) != null ? _regexResult$7$length : undefined,\n        z: regexResult[8] || undefined,\n        plusMinus: regexResult[9] || undefined,\n        hourOffset: toNumber(regexResult[10]),\n        minuteOffset: toNumber(regexResult[11])\n    };\n}\nfunction toNumber(str, defaultValue = 0) {\n    return Number(str) || defaultValue;\n}\n// Taken from HTML spec: https://html.spec.whatwg.org/multipage/input.html#valid-e-mail-address\nlet rEmail = // eslint-disable-next-line\n/^[a-zA-Z0-9.!#$%&'*+\\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;\nlet rUrl = // eslint-disable-next-line\n/^((https?|ftp):)?\\/\\/(((([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(%[\\da-f]{2})|[!\\$&'\\(\\)\\*\\+,;=]|:)*@)?(((\\d|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5]))|((([a-z]|\\d|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(([a-z]|\\d|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])*([a-z]|\\d|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])))\\.)+(([a-z]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(([a-z]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])*([a-z]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])))\\.?)(:\\d*)?)(\\/((([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(%[\\da-f]{2})|[!\\$&'\\(\\)\\*\\+,;=]|:|@)+(\\/(([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(%[\\da-f]{2})|[!\\$&'\\(\\)\\*\\+,;=]|:|@)*)*)?)?(\\?((([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(%[\\da-f]{2})|[!\\$&'\\(\\)\\*\\+,;=]|:|@)|[\\uE000-\\uF8FF]|\\/|\\?)*)?(\\#((([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(%[\\da-f]{2})|[!\\$&'\\(\\)\\*\\+,;=]|:|@)|\\/|\\?)*)?$/i;\n// eslint-disable-next-line\nlet rUUID = /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;\nlet yearMonthDay = \"^\\\\d{4}-\\\\d{2}-\\\\d{2}\";\nlet hourMinuteSecond = \"\\\\d{2}:\\\\d{2}:\\\\d{2}\";\nlet zOrOffset = \"(([+-]\\\\d{2}(:?\\\\d{2})?)|Z)\";\nlet rIsoDateTime = new RegExp(`${yearMonthDay}T${hourMinuteSecond}(\\\\.\\\\d+)?${zOrOffset}$`);\nlet isTrimmed = (value)=>isAbsent(value) || value === value.trim();\nlet objStringTag = ({}).toString();\nfunction create$6() {\n    return new StringSchema();\n}\nclass StringSchema extends Schema {\n    constructor(){\n        super({\n            type: \"string\",\n            check (value) {\n                if (value instanceof String) value = value.valueOf();\n                return typeof value === \"string\";\n            }\n        });\n        this.withMutation(()=>{\n            this.transform((value, _raw, ctx)=>{\n                if (!ctx.spec.coerce || ctx.isType(value)) return value;\n                // don't ever convert arrays\n                if (Array.isArray(value)) return value;\n                const strValue = value != null && value.toString ? value.toString() : value;\n                // no one wants plain objects converted to [Object object]\n                if (strValue === objStringTag) return value;\n                return strValue;\n            });\n        });\n    }\n    required(message) {\n        return super.required(message).withMutation((schema)=>schema.test({\n                message: message || mixed.required,\n                name: \"required\",\n                skipAbsent: true,\n                test: (value)=>!!value.length\n            }));\n    }\n    notRequired() {\n        return super.notRequired().withMutation((schema)=>{\n            schema.tests = schema.tests.filter((t)=>t.OPTIONS.name !== \"required\");\n            return schema;\n        });\n    }\n    length(length, message = string.length) {\n        return this.test({\n            message,\n            name: \"length\",\n            exclusive: true,\n            params: {\n                length\n            },\n            skipAbsent: true,\n            test (value) {\n                return value.length === this.resolve(length);\n            }\n        });\n    }\n    min(min, message = string.min) {\n        return this.test({\n            message,\n            name: \"min\",\n            exclusive: true,\n            params: {\n                min\n            },\n            skipAbsent: true,\n            test (value) {\n                return value.length >= this.resolve(min);\n            }\n        });\n    }\n    max(max, message = string.max) {\n        return this.test({\n            name: \"max\",\n            exclusive: true,\n            message,\n            params: {\n                max\n            },\n            skipAbsent: true,\n            test (value) {\n                return value.length <= this.resolve(max);\n            }\n        });\n    }\n    matches(regex, options) {\n        let excludeEmptyString = false;\n        let message;\n        let name;\n        if (options) {\n            if (typeof options === \"object\") {\n                ({ excludeEmptyString = false, message, name } = options);\n            } else {\n                message = options;\n            }\n        }\n        return this.test({\n            name: name || \"matches\",\n            message: message || string.matches,\n            params: {\n                regex\n            },\n            skipAbsent: true,\n            test: (value)=>value === \"\" && excludeEmptyString || value.search(regex) !== -1\n        });\n    }\n    email(message = string.email) {\n        return this.matches(rEmail, {\n            name: \"email\",\n            message,\n            excludeEmptyString: true\n        });\n    }\n    url(message = string.url) {\n        return this.matches(rUrl, {\n            name: \"url\",\n            message,\n            excludeEmptyString: true\n        });\n    }\n    uuid(message = string.uuid) {\n        return this.matches(rUUID, {\n            name: \"uuid\",\n            message,\n            excludeEmptyString: false\n        });\n    }\n    datetime(options) {\n        let message = \"\";\n        let allowOffset;\n        let precision;\n        if (options) {\n            if (typeof options === \"object\") {\n                ({ message = \"\", allowOffset = false, precision = undefined } = options);\n            } else {\n                message = options;\n            }\n        }\n        return this.matches(rIsoDateTime, {\n            name: \"datetime\",\n            message: message || string.datetime,\n            excludeEmptyString: true\n        }).test({\n            name: \"datetime_offset\",\n            message: message || string.datetime_offset,\n            params: {\n                allowOffset\n            },\n            skipAbsent: true,\n            test: (value)=>{\n                if (!value || allowOffset) return true;\n                const struct = parseDateStruct(value);\n                if (!struct) return false;\n                return !!struct.z;\n            }\n        }).test({\n            name: \"datetime_precision\",\n            message: message || string.datetime_precision,\n            params: {\n                precision\n            },\n            skipAbsent: true,\n            test: (value)=>{\n                if (!value || precision == undefined) return true;\n                const struct = parseDateStruct(value);\n                if (!struct) return false;\n                return struct.precision === precision;\n            }\n        });\n    }\n    //-- transforms --\n    ensure() {\n        return this.default(\"\").transform((val)=>val === null ? \"\" : val);\n    }\n    trim(message = string.trim) {\n        return this.transform((val)=>val != null ? val.trim() : val).test({\n            message,\n            name: \"trim\",\n            test: isTrimmed\n        });\n    }\n    lowercase(message = string.lowercase) {\n        return this.transform((value)=>!isAbsent(value) ? value.toLowerCase() : value).test({\n            message,\n            name: \"string_case\",\n            exclusive: true,\n            skipAbsent: true,\n            test: (value)=>isAbsent(value) || value === value.toLowerCase()\n        });\n    }\n    uppercase(message = string.uppercase) {\n        return this.transform((value)=>!isAbsent(value) ? value.toUpperCase() : value).test({\n            message,\n            name: \"string_case\",\n            exclusive: true,\n            skipAbsent: true,\n            test: (value)=>isAbsent(value) || value === value.toUpperCase()\n        });\n    }\n}\ncreate$6.prototype = StringSchema.prototype;\n//\n// String Interfaces\n//\nlet isNaN$1 = (value)=>value != +value;\nfunction create$5() {\n    return new NumberSchema();\n}\nclass NumberSchema extends Schema {\n    constructor(){\n        super({\n            type: \"number\",\n            check (value) {\n                if (value instanceof Number) value = value.valueOf();\n                return typeof value === \"number\" && !isNaN$1(value);\n            }\n        });\n        this.withMutation(()=>{\n            this.transform((value, _raw, ctx)=>{\n                if (!ctx.spec.coerce) return value;\n                let parsed = value;\n                if (typeof parsed === \"string\") {\n                    parsed = parsed.replace(/\\s/g, \"\");\n                    if (parsed === \"\") return NaN;\n                    // don't use parseFloat to avoid positives on alpha-numeric strings\n                    parsed = +parsed;\n                }\n                // null -> NaN isn't useful; treat all nulls as null and let it fail on\n                // nullability check vs TypeErrors\n                if (ctx.isType(parsed) || parsed === null) return parsed;\n                return parseFloat(parsed);\n            });\n        });\n    }\n    min(min, message = number.min) {\n        return this.test({\n            message,\n            name: \"min\",\n            exclusive: true,\n            params: {\n                min\n            },\n            skipAbsent: true,\n            test (value) {\n                return value >= this.resolve(min);\n            }\n        });\n    }\n    max(max, message = number.max) {\n        return this.test({\n            message,\n            name: \"max\",\n            exclusive: true,\n            params: {\n                max\n            },\n            skipAbsent: true,\n            test (value) {\n                return value <= this.resolve(max);\n            }\n        });\n    }\n    lessThan(less, message = number.lessThan) {\n        return this.test({\n            message,\n            name: \"max\",\n            exclusive: true,\n            params: {\n                less\n            },\n            skipAbsent: true,\n            test (value) {\n                return value < this.resolve(less);\n            }\n        });\n    }\n    moreThan(more, message = number.moreThan) {\n        return this.test({\n            message,\n            name: \"min\",\n            exclusive: true,\n            params: {\n                more\n            },\n            skipAbsent: true,\n            test (value) {\n                return value > this.resolve(more);\n            }\n        });\n    }\n    positive(msg = number.positive) {\n        return this.moreThan(0, msg);\n    }\n    negative(msg = number.negative) {\n        return this.lessThan(0, msg);\n    }\n    integer(message = number.integer) {\n        return this.test({\n            name: \"integer\",\n            message,\n            skipAbsent: true,\n            test: (val)=>Number.isInteger(val)\n        });\n    }\n    truncate() {\n        return this.transform((value)=>!isAbsent(value) ? value | 0 : value);\n    }\n    round(method) {\n        var _method;\n        let avail = [\n            \"ceil\",\n            \"floor\",\n            \"round\",\n            \"trunc\"\n        ];\n        method = ((_method = method) == null ? void 0 : _method.toLowerCase()) || \"round\";\n        // this exists for symemtry with the new Math.trunc\n        if (method === \"trunc\") return this.truncate();\n        if (avail.indexOf(method.toLowerCase()) === -1) throw new TypeError(\"Only valid options for round() are: \" + avail.join(\", \"));\n        return this.transform((value)=>!isAbsent(value) ? Math[method](value) : value);\n    }\n}\ncreate$5.prototype = NumberSchema.prototype;\n//\n// Number Interfaces\n//\nlet invalidDate = new Date(\"\");\nlet isDate = (obj)=>Object.prototype.toString.call(obj) === \"[object Date]\";\nfunction create$4() {\n    return new DateSchema();\n}\nclass DateSchema extends Schema {\n    constructor(){\n        super({\n            type: \"date\",\n            check (v) {\n                return isDate(v) && !isNaN(v.getTime());\n            }\n        });\n        this.withMutation(()=>{\n            this.transform((value, _raw, ctx)=>{\n                // null -> InvalidDate isn't useful; treat all nulls as null and let it fail on\n                // nullability check vs TypeErrors\n                if (!ctx.spec.coerce || ctx.isType(value) || value === null) return value;\n                value = parseIsoDate(value);\n                // 0 is a valid timestamp equivalent to 1970-01-01T00:00:00Z(unix epoch) or before.\n                return !isNaN(value) ? new Date(value) : DateSchema.INVALID_DATE;\n            });\n        });\n    }\n    prepareParam(ref, name) {\n        let param;\n        if (!Reference.isRef(ref)) {\n            let cast = this.cast(ref);\n            if (!this._typeCheck(cast)) throw new TypeError(`\\`${name}\\` must be a Date or a value that can be \\`cast()\\` to a Date`);\n            param = cast;\n        } else {\n            param = ref;\n        }\n        return param;\n    }\n    min(min, message = date.min) {\n        let limit = this.prepareParam(min, \"min\");\n        return this.test({\n            message,\n            name: \"min\",\n            exclusive: true,\n            params: {\n                min\n            },\n            skipAbsent: true,\n            test (value) {\n                return value >= this.resolve(limit);\n            }\n        });\n    }\n    max(max, message = date.max) {\n        let limit = this.prepareParam(max, \"max\");\n        return this.test({\n            message,\n            name: \"max\",\n            exclusive: true,\n            params: {\n                max\n            },\n            skipAbsent: true,\n            test (value) {\n                return value <= this.resolve(limit);\n            }\n        });\n    }\n}\nDateSchema.INVALID_DATE = invalidDate;\ncreate$4.prototype = DateSchema.prototype;\ncreate$4.INVALID_DATE = invalidDate;\n// @ts-expect-error\nfunction sortFields(fields, excludedEdges = []) {\n    let edges = [];\n    let nodes = new Set();\n    let excludes = new Set(excludedEdges.map(([a, b])=>`${a}-${b}`));\n    function addNode(depPath, key) {\n        let node = (0,property_expr__WEBPACK_IMPORTED_MODULE_0__.split)(depPath)[0];\n        nodes.add(node);\n        if (!excludes.has(`${key}-${node}`)) edges.push([\n            key,\n            node\n        ]);\n    }\n    for (const key of Object.keys(fields)){\n        let value = fields[key];\n        nodes.add(key);\n        if (Reference.isRef(value) && value.isSibling) addNode(value.path, key);\n        else if (isSchema(value) && \"deps\" in value) value.deps.forEach((path)=>addNode(path, key));\n    }\n    return toposort__WEBPACK_IMPORTED_MODULE_2___default().array(Array.from(nodes), edges).reverse();\n}\nfunction findIndex(arr, err) {\n    let idx = Infinity;\n    arr.some((key, ii)=>{\n        var _err$path;\n        if ((_err$path = err.path) != null && _err$path.includes(key)) {\n            idx = ii;\n            return true;\n        }\n    });\n    return idx;\n}\nfunction sortByKeyOrder(keys) {\n    return (a, b)=>{\n        return findIndex(keys, a) - findIndex(keys, b);\n    };\n}\nconst parseJson = (value, _, ctx)=>{\n    if (typeof value !== \"string\") {\n        return value;\n    }\n    let parsed = value;\n    try {\n        parsed = JSON.parse(value);\n    } catch (err) {\n    /* */ }\n    return ctx.isType(parsed) ? parsed : value;\n};\n// @ts-ignore\nfunction deepPartial(schema) {\n    if (\"fields\" in schema) {\n        const partial = {};\n        for (const [key, fieldSchema] of Object.entries(schema.fields)){\n            partial[key] = deepPartial(fieldSchema);\n        }\n        return schema.setFields(partial);\n    }\n    if (schema.type === \"array\") {\n        const nextArray = schema.optional();\n        if (nextArray.innerType) nextArray.innerType = deepPartial(nextArray.innerType);\n        return nextArray;\n    }\n    if (schema.type === \"tuple\") {\n        return schema.optional().clone({\n            types: schema.spec.types.map(deepPartial)\n        });\n    }\n    if (\"optional\" in schema) {\n        return schema.optional();\n    }\n    return schema;\n}\nconst deepHas = (obj, p)=>{\n    const path = [\n        ...(0,property_expr__WEBPACK_IMPORTED_MODULE_0__.normalizePath)(p)\n    ];\n    if (path.length === 1) return path[0] in obj;\n    let last = path.pop();\n    let parent = (0,property_expr__WEBPACK_IMPORTED_MODULE_0__.getter)((0,property_expr__WEBPACK_IMPORTED_MODULE_0__.join)(path), true)(obj);\n    return !!(parent && last in parent);\n};\nlet isObject = (obj)=>Object.prototype.toString.call(obj) === \"[object Object]\";\nfunction unknown(ctx, value) {\n    let known = Object.keys(ctx.fields);\n    return Object.keys(value).filter((key)=>known.indexOf(key) === -1);\n}\nconst defaultSort = sortByKeyOrder([]);\nfunction create$3(spec) {\n    return new ObjectSchema(spec);\n}\nclass ObjectSchema extends Schema {\n    constructor(spec){\n        super({\n            type: \"object\",\n            check (value) {\n                return isObject(value) || typeof value === \"function\";\n            }\n        });\n        this.fields = Object.create(null);\n        this._sortErrors = defaultSort;\n        this._nodes = [];\n        this._excludedEdges = [];\n        this.withMutation(()=>{\n            if (spec) {\n                this.shape(spec);\n            }\n        });\n    }\n    _cast(_value, options = {}) {\n        var _options$stripUnknown;\n        let value = super._cast(_value, options);\n        //should ignore nulls here\n        if (value === undefined) return this.getDefault(options);\n        if (!this._typeCheck(value)) return value;\n        let fields = this.fields;\n        let strip = (_options$stripUnknown = options.stripUnknown) != null ? _options$stripUnknown : this.spec.noUnknown;\n        let props = [].concat(this._nodes, Object.keys(value).filter((v)=>!this._nodes.includes(v)));\n        let intermediateValue = {}; // is filled during the transform below\n        let innerOptions = Object.assign({}, options, {\n            parent: intermediateValue,\n            __validating: options.__validating || false\n        });\n        let isChanged = false;\n        for (const prop of props){\n            let field = fields[prop];\n            let exists = prop in value;\n            if (field) {\n                let fieldValue;\n                let inputValue = value[prop];\n                // safe to mutate since this is fired in sequence\n                innerOptions.path = (options.path ? `${options.path}.` : \"\") + prop;\n                field = field.resolve({\n                    value: inputValue,\n                    context: options.context,\n                    parent: intermediateValue\n                });\n                let fieldSpec = field instanceof Schema ? field.spec : undefined;\n                let strict = fieldSpec == null ? void 0 : fieldSpec.strict;\n                if (fieldSpec != null && fieldSpec.strip) {\n                    isChanged = isChanged || prop in value;\n                    continue;\n                }\n                fieldValue = !options.__validating || !strict ? // TODO: use _cast, this is double resolving\n                field.cast(value[prop], innerOptions) : value[prop];\n                if (fieldValue !== undefined) {\n                    intermediateValue[prop] = fieldValue;\n                }\n            } else if (exists && !strip) {\n                intermediateValue[prop] = value[prop];\n            }\n            if (exists !== prop in intermediateValue || intermediateValue[prop] !== value[prop]) {\n                isChanged = true;\n            }\n        }\n        return isChanged ? intermediateValue : value;\n    }\n    _validate(_value, options = {}, panic, next) {\n        let { from = [], originalValue = _value, recursive = this.spec.recursive } = options;\n        options.from = [\n            {\n                schema: this,\n                value: originalValue\n            },\n            ...from\n        ];\n        // this flag is needed for handling `strict` correctly in the context of\n        // validation vs just casting. e.g strict() on a field is only used when validating\n        options.__validating = true;\n        options.originalValue = originalValue;\n        super._validate(_value, options, panic, (objectErrors, value)=>{\n            if (!recursive || !isObject(value)) {\n                next(objectErrors, value);\n                return;\n            }\n            originalValue = originalValue || value;\n            let tests = [];\n            for (let key of this._nodes){\n                let field = this.fields[key];\n                if (!field || Reference.isRef(field)) {\n                    continue;\n                }\n                tests.push(field.asNestedTest({\n                    options,\n                    key,\n                    parent: value,\n                    parentPath: options.path,\n                    originalParent: originalValue\n                }));\n            }\n            this.runTests({\n                tests,\n                value,\n                originalValue,\n                options\n            }, panic, (fieldErrors)=>{\n                next(fieldErrors.sort(this._sortErrors).concat(objectErrors), value);\n            });\n        });\n    }\n    clone(spec) {\n        const next = super.clone(spec);\n        next.fields = Object.assign({}, this.fields);\n        next._nodes = this._nodes;\n        next._excludedEdges = this._excludedEdges;\n        next._sortErrors = this._sortErrors;\n        return next;\n    }\n    concat(schema) {\n        let next = super.concat(schema);\n        let nextFields = next.fields;\n        for (let [field, schemaOrRef] of Object.entries(this.fields)){\n            const target = nextFields[field];\n            nextFields[field] = target === undefined ? schemaOrRef : target;\n        }\n        return next.withMutation((s)=>// XXX: excludes here is wrong\n            s.setFields(nextFields, [\n                ...this._excludedEdges,\n                ...schema._excludedEdges\n            ]));\n    }\n    _getDefault(options) {\n        if (\"default\" in this.spec) {\n            return super._getDefault(options);\n        }\n        // if there is no default set invent one\n        if (!this._nodes.length) {\n            return undefined;\n        }\n        let dft = {};\n        this._nodes.forEach((key)=>{\n            var _innerOptions;\n            const field = this.fields[key];\n            let innerOptions = options;\n            if ((_innerOptions = innerOptions) != null && _innerOptions.value) {\n                innerOptions = Object.assign({}, innerOptions, {\n                    parent: innerOptions.value,\n                    value: innerOptions.value[key]\n                });\n            }\n            dft[key] = field && \"getDefault\" in field ? field.getDefault(innerOptions) : undefined;\n        });\n        return dft;\n    }\n    setFields(shape, excludedEdges) {\n        let next = this.clone();\n        next.fields = shape;\n        next._nodes = sortFields(shape, excludedEdges);\n        next._sortErrors = sortByKeyOrder(Object.keys(shape));\n        // XXX: this carries over edges which may not be what you want\n        if (excludedEdges) next._excludedEdges = excludedEdges;\n        return next;\n    }\n    shape(additions, excludes = []) {\n        return this.clone().withMutation((next)=>{\n            let edges = next._excludedEdges;\n            if (excludes.length) {\n                if (!Array.isArray(excludes[0])) excludes = [\n                    excludes\n                ];\n                edges = [\n                    ...next._excludedEdges,\n                    ...excludes\n                ];\n            }\n            // XXX: excludes here is wrong\n            return next.setFields(Object.assign(next.fields, additions), edges);\n        });\n    }\n    partial() {\n        const partial = {};\n        for (const [key, schema] of Object.entries(this.fields)){\n            partial[key] = \"optional\" in schema && schema.optional instanceof Function ? schema.optional() : schema;\n        }\n        return this.setFields(partial);\n    }\n    deepPartial() {\n        const next = deepPartial(this);\n        return next;\n    }\n    pick(keys) {\n        const picked = {};\n        for (const key of keys){\n            if (this.fields[key]) picked[key] = this.fields[key];\n        }\n        return this.setFields(picked, this._excludedEdges.filter(([a, b])=>keys.includes(a) && keys.includes(b)));\n    }\n    omit(keys) {\n        const remaining = [];\n        for (const key of Object.keys(this.fields)){\n            if (keys.includes(key)) continue;\n            remaining.push(key);\n        }\n        return this.pick(remaining);\n    }\n    from(from, to, alias) {\n        let fromGetter = (0,property_expr__WEBPACK_IMPORTED_MODULE_0__.getter)(from, true);\n        return this.transform((obj)=>{\n            if (!obj) return obj;\n            let newObj = obj;\n            if (deepHas(obj, from)) {\n                newObj = Object.assign({}, obj);\n                if (!alias) delete newObj[from];\n                newObj[to] = fromGetter(obj);\n            }\n            return newObj;\n        });\n    }\n    /** Parse an input JSON string to an object */ json() {\n        return this.transform(parseJson);\n    }\n    /**\n   * Similar to `noUnknown` but only validates that an object is the right shape without stripping the unknown keys\n   */ exact(message) {\n        return this.test({\n            name: \"exact\",\n            exclusive: true,\n            message: message || object.exact,\n            test (value) {\n                if (value == null) return true;\n                const unknownKeys = unknown(this.schema, value);\n                return unknownKeys.length === 0 || this.createError({\n                    params: {\n                        properties: unknownKeys.join(\", \")\n                    }\n                });\n            }\n        });\n    }\n    stripUnknown() {\n        return this.clone({\n            noUnknown: true\n        });\n    }\n    noUnknown(noAllow = true, message = object.noUnknown) {\n        if (typeof noAllow !== \"boolean\") {\n            message = noAllow;\n            noAllow = true;\n        }\n        let next = this.test({\n            name: \"noUnknown\",\n            exclusive: true,\n            message: message,\n            test (value) {\n                if (value == null) return true;\n                const unknownKeys = unknown(this.schema, value);\n                return !noAllow || unknownKeys.length === 0 || this.createError({\n                    params: {\n                        unknown: unknownKeys.join(\", \")\n                    }\n                });\n            }\n        });\n        next.spec.noUnknown = noAllow;\n        return next;\n    }\n    unknown(allow = true, message = object.noUnknown) {\n        return this.noUnknown(!allow, message);\n    }\n    transformKeys(fn) {\n        return this.transform((obj)=>{\n            if (!obj) return obj;\n            const result = {};\n            for (const key of Object.keys(obj))result[fn(key)] = obj[key];\n            return result;\n        });\n    }\n    camelCase() {\n        return this.transformKeys(tiny_case__WEBPACK_IMPORTED_MODULE_1__.camelCase);\n    }\n    snakeCase() {\n        return this.transformKeys(tiny_case__WEBPACK_IMPORTED_MODULE_1__.snakeCase);\n    }\n    constantCase() {\n        return this.transformKeys((key)=>(0,tiny_case__WEBPACK_IMPORTED_MODULE_1__.snakeCase)(key).toUpperCase());\n    }\n    describe(options) {\n        const next = (options ? this.resolve(options) : this).clone();\n        const base = super.describe(options);\n        base.fields = {};\n        for (const [key, value] of Object.entries(next.fields)){\n            var _innerOptions2;\n            let innerOptions = options;\n            if ((_innerOptions2 = innerOptions) != null && _innerOptions2.value) {\n                innerOptions = Object.assign({}, innerOptions, {\n                    parent: innerOptions.value,\n                    value: innerOptions.value[key]\n                });\n            }\n            base.fields[key] = value.describe(innerOptions);\n        }\n        return base;\n    }\n}\ncreate$3.prototype = ObjectSchema.prototype;\nfunction create$2(type) {\n    return new ArraySchema(type);\n}\nclass ArraySchema extends Schema {\n    constructor(type){\n        super({\n            type: \"array\",\n            spec: {\n                types: type\n            },\n            check (v) {\n                return Array.isArray(v);\n            }\n        });\n        // `undefined` specifically means uninitialized, as opposed to \"no subtype\"\n        this.innerType = void 0;\n        this.innerType = type;\n    }\n    _cast(_value, _opts) {\n        const value = super._cast(_value, _opts);\n        // should ignore nulls here\n        if (!this._typeCheck(value) || !this.innerType) {\n            return value;\n        }\n        let isChanged = false;\n        const castArray = value.map((v, idx)=>{\n            const castElement = this.innerType.cast(v, Object.assign({}, _opts, {\n                path: `${_opts.path || \"\"}[${idx}]`\n            }));\n            if (castElement !== v) {\n                isChanged = true;\n            }\n            return castElement;\n        });\n        return isChanged ? castArray : value;\n    }\n    _validate(_value, options = {}, panic, next) {\n        var _options$recursive;\n        // let sync = options.sync;\n        // let path = options.path;\n        let innerType = this.innerType;\n        // let endEarly = options.abortEarly ?? this.spec.abortEarly;\n        let recursive = (_options$recursive = options.recursive) != null ? _options$recursive : this.spec.recursive;\n        options.originalValue != null ? options.originalValue : _value;\n        super._validate(_value, options, panic, (arrayErrors, value)=>{\n            var _options$originalValu2;\n            if (!recursive || !innerType || !this._typeCheck(value)) {\n                next(arrayErrors, value);\n                return;\n            }\n            // #950 Ensure that sparse array empty slots are validated\n            let tests = new Array(value.length);\n            for(let index = 0; index < value.length; index++){\n                var _options$originalValu;\n                tests[index] = innerType.asNestedTest({\n                    options,\n                    index,\n                    parent: value,\n                    parentPath: options.path,\n                    originalParent: (_options$originalValu = options.originalValue) != null ? _options$originalValu : _value\n                });\n            }\n            this.runTests({\n                value,\n                tests,\n                originalValue: (_options$originalValu2 = options.originalValue) != null ? _options$originalValu2 : _value,\n                options\n            }, panic, (innerTypeErrors)=>next(innerTypeErrors.concat(arrayErrors), value));\n        });\n    }\n    clone(spec) {\n        const next = super.clone(spec);\n        // @ts-expect-error readonly\n        next.innerType = this.innerType;\n        return next;\n    }\n    /** Parse an input JSON string to an object */ json() {\n        return this.transform(parseJson);\n    }\n    concat(schema) {\n        let next = super.concat(schema);\n        // @ts-expect-error readonly\n        next.innerType = this.innerType;\n        if (schema.innerType) // @ts-expect-error readonly\n        next.innerType = next.innerType ? // @ts-expect-error Lazy doesn't have concat and will break\n        next.innerType.concat(schema.innerType) : schema.innerType;\n        return next;\n    }\n    of(schema) {\n        // FIXME: this should return a new instance of array without the default to be\n        let next = this.clone();\n        if (!isSchema(schema)) throw new TypeError(\"`array.of()` sub-schema must be a valid yup schema not: \" + printValue(schema));\n        // @ts-expect-error readonly\n        next.innerType = schema;\n        next.spec = Object.assign({}, next.spec, {\n            types: schema\n        });\n        return next;\n    }\n    length(length, message = array.length) {\n        return this.test({\n            message,\n            name: \"length\",\n            exclusive: true,\n            params: {\n                length\n            },\n            skipAbsent: true,\n            test (value) {\n                return value.length === this.resolve(length);\n            }\n        });\n    }\n    min(min, message) {\n        message = message || array.min;\n        return this.test({\n            message,\n            name: \"min\",\n            exclusive: true,\n            params: {\n                min\n            },\n            skipAbsent: true,\n            // FIXME(ts): Array<typeof T>\n            test (value) {\n                return value.length >= this.resolve(min);\n            }\n        });\n    }\n    max(max, message) {\n        message = message || array.max;\n        return this.test({\n            message,\n            name: \"max\",\n            exclusive: true,\n            params: {\n                max\n            },\n            skipAbsent: true,\n            test (value) {\n                return value.length <= this.resolve(max);\n            }\n        });\n    }\n    ensure() {\n        return this.default(()=>[]).transform((val, original)=>{\n            // We don't want to return `null` for nullable schema\n            if (this._typeCheck(val)) return val;\n            return original == null ? [] : [].concat(original);\n        });\n    }\n    compact(rejector) {\n        let reject = !rejector ? (v)=>!!v : (v, i, a)=>!rejector(v, i, a);\n        return this.transform((values)=>values != null ? values.filter(reject) : values);\n    }\n    describe(options) {\n        const next = (options ? this.resolve(options) : this).clone();\n        const base = super.describe(options);\n        if (next.innerType) {\n            var _innerOptions;\n            let innerOptions = options;\n            if ((_innerOptions = innerOptions) != null && _innerOptions.value) {\n                innerOptions = Object.assign({}, innerOptions, {\n                    parent: innerOptions.value,\n                    value: innerOptions.value[0]\n                });\n            }\n            base.innerType = next.innerType.describe(innerOptions);\n        }\n        return base;\n    }\n}\ncreate$2.prototype = ArraySchema.prototype;\n// @ts-ignore\nfunction create$1(schemas) {\n    return new TupleSchema(schemas);\n}\nclass TupleSchema extends Schema {\n    constructor(schemas){\n        super({\n            type: \"tuple\",\n            spec: {\n                types: schemas\n            },\n            check (v) {\n                const types = this.spec.types;\n                return Array.isArray(v) && v.length === types.length;\n            }\n        });\n        this.withMutation(()=>{\n            this.typeError(tuple.notType);\n        });\n    }\n    _cast(inputValue, options) {\n        const { types } = this.spec;\n        const value = super._cast(inputValue, options);\n        if (!this._typeCheck(value)) {\n            return value;\n        }\n        let isChanged = false;\n        const castArray = types.map((type, idx)=>{\n            const castElement = type.cast(value[idx], Object.assign({}, options, {\n                path: `${options.path || \"\"}[${idx}]`\n            }));\n            if (castElement !== value[idx]) isChanged = true;\n            return castElement;\n        });\n        return isChanged ? castArray : value;\n    }\n    _validate(_value, options = {}, panic, next) {\n        let itemTypes = this.spec.types;\n        super._validate(_value, options, panic, (tupleErrors, value)=>{\n            var _options$originalValu2;\n            // intentionally not respecting recursive\n            if (!this._typeCheck(value)) {\n                next(tupleErrors, value);\n                return;\n            }\n            let tests = [];\n            for (let [index, itemSchema] of itemTypes.entries()){\n                var _options$originalValu;\n                tests[index] = itemSchema.asNestedTest({\n                    options,\n                    index,\n                    parent: value,\n                    parentPath: options.path,\n                    originalParent: (_options$originalValu = options.originalValue) != null ? _options$originalValu : _value\n                });\n            }\n            this.runTests({\n                value,\n                tests,\n                originalValue: (_options$originalValu2 = options.originalValue) != null ? _options$originalValu2 : _value,\n                options\n            }, panic, (innerTypeErrors)=>next(innerTypeErrors.concat(tupleErrors), value));\n        });\n    }\n    describe(options) {\n        const next = (options ? this.resolve(options) : this).clone();\n        const base = super.describe(options);\n        base.innerType = next.spec.types.map((schema, index)=>{\n            var _innerOptions;\n            let innerOptions = options;\n            if ((_innerOptions = innerOptions) != null && _innerOptions.value) {\n                innerOptions = Object.assign({}, innerOptions, {\n                    parent: innerOptions.value,\n                    value: innerOptions.value[index]\n                });\n            }\n            return schema.describe(innerOptions);\n        });\n        return base;\n    }\n}\ncreate$1.prototype = TupleSchema.prototype;\nfunction create(builder) {\n    return new Lazy(builder);\n}\nfunction catchValidationError(fn) {\n    try {\n        return fn();\n    } catch (err) {\n        if (ValidationError.isError(err)) return Promise.reject(err);\n        throw err;\n    }\n}\nclass Lazy {\n    constructor(builder){\n        this.type = \"lazy\";\n        this.__isYupSchema__ = true;\n        this.spec = void 0;\n        this._resolve = (value, options = {})=>{\n            let schema = this.builder(value, options);\n            if (!isSchema(schema)) throw new TypeError(\"lazy() functions must return a valid schema\");\n            if (this.spec.optional) schema = schema.optional();\n            return schema.resolve(options);\n        };\n        this.builder = builder;\n        this.spec = {\n            meta: undefined,\n            optional: false\n        };\n    }\n    clone(spec) {\n        const next = new Lazy(this.builder);\n        next.spec = Object.assign({}, this.spec, spec);\n        return next;\n    }\n    optionality(optional) {\n        const next = this.clone({\n            optional\n        });\n        return next;\n    }\n    optional() {\n        return this.optionality(true);\n    }\n    resolve(options) {\n        return this._resolve(options.value, options);\n    }\n    cast(value, options) {\n        return this._resolve(value, options).cast(value, options);\n    }\n    asNestedTest(config) {\n        let { key, index, parent, options } = config;\n        let value = parent[index != null ? index : key];\n        return this._resolve(value, Object.assign({}, options, {\n            value,\n            parent\n        })).asNestedTest(config);\n    }\n    validate(value, options) {\n        return catchValidationError(()=>this._resolve(value, options).validate(value, options));\n    }\n    validateSync(value, options) {\n        return this._resolve(value, options).validateSync(value, options);\n    }\n    validateAt(path, value, options) {\n        return catchValidationError(()=>this._resolve(value, options).validateAt(path, value, options));\n    }\n    validateSyncAt(path, value, options) {\n        return this._resolve(value, options).validateSyncAt(path, value, options);\n    }\n    isValid(value, options) {\n        try {\n            return this._resolve(value, options).isValid(value, options);\n        } catch (err) {\n            if (ValidationError.isError(err)) {\n                return Promise.resolve(false);\n            }\n            throw err;\n        }\n    }\n    isValidSync(value, options) {\n        return this._resolve(value, options).isValidSync(value, options);\n    }\n    describe(options) {\n        return options ? this.resolve(options).describe(options) : {\n            type: \"lazy\",\n            meta: this.spec.meta,\n            label: undefined\n        };\n    }\n    meta(...args) {\n        if (args.length === 0) return this.spec.meta;\n        let next = this.clone();\n        next.spec.meta = Object.assign(next.spec.meta || {}, args[0]);\n        return next;\n    }\n}\nfunction setLocale(custom) {\n    Object.keys(custom).forEach((type)=>{\n        // @ts-ignore\n        Object.keys(custom[type]).forEach((method)=>{\n            // @ts-ignore\n            locale[type][method] = custom[type][method];\n        });\n    });\n}\nfunction addMethod(schemaType, name, fn) {\n    if (!schemaType || !isSchema(schemaType.prototype)) throw new TypeError(\"You must provide a yup schema constructor function\");\n    if (typeof name !== \"string\") throw new TypeError(\"A Method name must be provided\");\n    if (typeof fn !== \"function\") throw new TypeError(\"Method function must be provided\");\n    schemaType.prototype[name] = fn;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/yup/index.esm.js\n");

/***/ })

};
;