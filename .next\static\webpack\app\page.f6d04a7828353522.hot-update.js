"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ClientForm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ClientForm */ \"(app-pages-browser)/./src/components/ClientForm.tsx\");\n/* harmony import */ var _components_InvoiceForm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/InvoiceForm */ \"(app-pages-browser)/./src/components/InvoiceForm.tsx\");\n/* harmony import */ var _components_InvoicePreview__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/InvoicePreview */ \"(app-pages-browser)/./src/components/InvoicePreview.tsx\");\n/* harmony import */ var _components_InvoicesList__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/InvoicesList */ \"(app-pages-browser)/./src/components/InvoicesList.tsx\");\n/* harmony import */ var _store_invoiceStore__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/store/invoiceStore */ \"(app-pages-browser)/./src/store/invoiceStore.ts\");\n/* harmony import */ var _utils_pdfGenerator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/pdfGenerator */ \"(app-pages-browser)/./src/utils/pdfGenerator.ts\");\n/* harmony import */ var _utils_consoleFilter__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/consoleFilter */ \"(app-pages-browser)/./src/utils/consoleFilter.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"home\");\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"client\");\n    const [currentClient, setCurrentClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentInvoice, setCurrentInvoice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { saveInvoice } = (0,_store_invoiceStore__WEBPACK_IMPORTED_MODULE_6__.useInvoiceStore)();\n    // تطبيق مرشح Console لإخفاء الرسائل غير المرغوبة\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        (0,_utils_consoleFilter__WEBPACK_IMPORTED_MODULE_8__.setupConsoleFilter)();\n        (0,_utils_consoleFilter__WEBPACK_IMPORTED_MODULE_8__.logSystemInfo)();\n    }, []);\n    const handleClientSubmit = (client)=>{\n        setCurrentClient(client);\n        setCurrentStep(\"invoice\");\n    };\n    const handleInvoiceSubmit = (invoice)=>{\n        console.log(\"\\uD83D\\uDE80 Starting invoice submission process...\");\n        // إنشاء ID فريد مع ترقيم تسلسلي\n        const generateSequentialId = ()=>{\n            const now = new Date();\n            const month = now.getMonth() + 1;\n            const year = now.getFullYear();\n            const monthYearKey = \"\".concat(month, \"-\").concat(year);\n            // قراءة العدادات المحفوظة\n            let counters = {};\n            try {\n                const stored = localStorage.getItem(\"invoiceCounters\");\n                counters = stored ? JSON.parse(stored) : {};\n            } catch (error) {\n                console.error(\"❌ Error reading invoice counters:\", error);\n                counters = {};\n            }\n            // الحصول على العداد الحالي للشهر والسنة\n            const currentCounter = counters[monthYearKey] || 0;\n            const newCounter = currentCounter + 1;\n            // تحديث العداد\n            counters[monthYearKey] = newCounter;\n            // حفظ العدادات المحدثة\n            try {\n                localStorage.setItem(\"invoiceCounters\", JSON.stringify(counters));\n                console.log(\"\\uD83D\\uDCCA Updated counter for \".concat(monthYearKey, \": \").concat(newCounter));\n            } catch (error) {\n                console.error(\"❌ Error saving invoice counters:\", error);\n            }\n            // إنشاء ID بالتنسيق المطلوب\n            const sequentialNumber = String(newCounter).padStart(3, \"0\");\n            const invoiceId = \"INV-\".concat(month, \"-\").concat(year, \"-\").concat(sequentialNumber);\n            console.log(\"\\uD83C\\uDD94 Generated sequential ID: \".concat(invoiceId));\n            return invoiceId;\n        };\n        const invoiceWithId = {\n            ...invoice,\n            id: invoice.id || generateSequentialId(),\n            createdAt: invoice.createdAt || new Date().toISOString()\n        };\n        console.log(\"\\uD83D\\uDCC4 Invoice prepared with ID:\", invoiceWithId.id);\n        // تعيين الفاتورة الحالية\n        setCurrentInvoice(invoiceWithId);\n        // حفظ الفاتورة في المتجر\n        const store = _store_invoiceStore__WEBPACK_IMPORTED_MODULE_6__.useInvoiceStore.getState();\n        store.setCurrentInvoice(invoiceWithId);\n        // حفظ الفاتورة (ستتعامل دالة saveInvoice مع التحقق من التكرار)\n        store.saveInvoice();\n        // التحقق من النتيجة\n        setTimeout(()=>{\n            try {\n                const currentInvoices = JSON.parse(localStorage.getItem(\"invoices\") || \"[]\");\n                console.log(\"\\uD83D\\uDCCA Final check - Total invoices in localStorage:\", currentInvoices.length);\n                const savedInvoice = currentInvoices.find((inv)=>inv.id === invoiceWithId.id);\n                if (savedInvoice) {\n                    console.log(\"✅ Invoice successfully saved and verified:\", invoiceWithId.id);\n                } else {\n                    console.error(\"❌ Invoice not found in localStorage after save attempt\");\n                }\n            } catch (error) {\n                console.error(\"❌ Error verifying save:\", error);\n            }\n        }, 100);\n        setCurrentStep(\"preview\");\n    };\n    const handleEditInvoice = ()=>{\n        setCurrentStep(\"invoice\");\n    };\n    const handleExportPDF = async ()=>{\n        if (currentInvoice) {\n            try {\n                await (0,_utils_pdfGenerator__WEBPACK_IMPORTED_MODULE_7__.generateInvoicePDF)(currentInvoice);\n            } catch (error) {\n                alert(\"حدث خطأ أثناء تصدير PDF: \" + error.message);\n            }\n        }\n    };\n    const handleStartOver = ()=>{\n        setCurrentClient(null);\n        setCurrentInvoice(null);\n        setCurrentStep(\"client\");\n        setCurrentPage(\"home\");\n    };\n    const getStepTitle = (step)=>{\n        switch(step){\n            case \"client\":\n                return \"إدخال بيانات العميل\";\n            case \"invoice\":\n                return \"إنشاء الفاتورة\";\n            case \"preview\":\n                return \"معاينة الفاتورة\";\n            default:\n                return \"\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row justify-between items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white bg-opacity-20 p-2 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-8 h-8\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl sm:text-3xl font-bold\",\n                                                children: \"نظام إدارة الفواتير\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-100 text-sm hidden sm:block\",\n                                                children: \"إدارة شاملة للفواتير والخدمات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"flex gap-2 sm:gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setCurrentPage(\"home\"),\n                                        className: \"px-3 sm:px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 \".concat(currentPage === \"home\" ? \"bg-white text-blue-600 shadow-md\" : \"bg-blue-500 hover:bg-blue-400 text-white hover:shadow-md\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M10 2L3 9v9a1 1 0 001 1h3a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1h3a1 1 0 001-1V9l-7-7z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"إنشاء فاتورة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sm:hidden\",\n                                                children: \"إنشاء\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setCurrentPage(\"invoices\"),\n                                        className: \"px-3 sm:px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 \".concat(currentPage === \"invoices\" ? \"bg-white text-blue-600 shadow-md\" : \"bg-blue-500 hover:bg-blue-400 text-white hover:shadow-md\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M4 5a2 2 0 012-2v1a1 1 0 001 1h6a1 1 0 001-1V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"إدارة الفواتير\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sm:hidden\",\n                                                children: \"إدارة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleStartOver,\n                                        className: \"bg-green-500 hover:bg-green-400 px-3 sm:px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 hover:shadow-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"بداية جديدة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sm:hidden\",\n                                                children: \"جديد\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/diagnostics\",\n                                        className: \"bg-orange-500 hover:bg-orange-400 px-3 sm:px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 hover:shadow-md text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"تشخيص\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sm:hidden\",\n                                                children: \"تشخيص\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this),\n            currentPage === \"home\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-l from-gray-50 to-white shadow-sm border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 sm:space-x-12 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center transition-all duration-500 \".concat(currentStep === \"client\" ? \"text-blue-600 scale-105\" : currentStep === \"invoice\" || currentStep === \"preview\" ? \"text-green-600\" : \"text-gray-400\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 rounded-full flex items-center justify-center text-white text-sm font-bold transition-all duration-500 shadow-lg hover:shadow-xl \".concat(currentStep === \"client\" ? \"bg-gradient-to-br from-blue-500 to-blue-700 ring-4 ring-blue-200 animate-pulse\" : currentStep === \"invoice\" || currentStep === \"preview\" ? \"bg-gradient-to-br from-green-500 to-green-700 ring-2 ring-green-200\" : \"bg-gray-400\"),\n                                                    children: currentStep === \"invoice\" || currentStep === \"preview\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6 animate-bounceIn\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 244,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mt-3 font-semibold text-sm sm:text-base text-center\",\n                                                    children: \"بيانات العميل\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 21\n                                                }, this),\n                                                currentStep === \"client\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-1 text-xs text-blue-500 animate-pulse\",\n                                                    children: \"جاري التعبئة...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 max-w-24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-1 rounded-full transition-all duration-700 \".concat(currentStep === \"invoice\" || currentStep === \"preview\" ? \"bg-gradient-to-l from-green-400 to-green-500\" : \"bg-gray-300\"),\n                                            children: currentStep === \"invoice\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-full bg-gradient-to-l from-blue-400 to-blue-500 rounded-full animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center transition-all duration-500 \".concat(currentStep === \"invoice\" ? \"text-blue-600 scale-105\" : currentStep === \"preview\" ? \"text-green-600\" : \"text-gray-400\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 rounded-full flex items-center justify-center text-white text-sm font-bold transition-all duration-500 shadow-lg hover:shadow-xl \".concat(currentStep === \"invoice\" ? \"bg-gradient-to-br from-blue-500 to-blue-700 ring-4 ring-blue-200 animate-pulse\" : currentStep === \"preview\" ? \"bg-gradient-to-br from-green-500 to-green-700 ring-2 ring-green-200\" : \"bg-gray-400\"),\n                                                    children: currentStep === \"preview\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6 animate-bounceIn\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mt-3 font-semibold text-sm sm:text-base text-center\",\n                                                    children: \"إنشاء الفاتورة\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 21\n                                                }, this),\n                                                currentStep === \"invoice\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-1 text-xs text-blue-500 animate-pulse\",\n                                                    children: \"جاري التعبئة...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 max-w-24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-1 rounded-full transition-all duration-700 \".concat(currentStep === \"preview\" ? \"bg-gradient-to-l from-green-400 to-green-500\" : \"bg-gray-300\"),\n                                            children: currentStep === \"preview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-full bg-gradient-to-l from-blue-400 to-blue-500 rounded-full animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center transition-all duration-500 \".concat(currentStep === \"preview\" ? \"text-blue-600 scale-105\" : \"text-gray-400\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 rounded-full flex items-center justify-center text-white text-sm font-bold transition-all duration-500 shadow-lg hover:shadow-xl \".concat(currentStep === \"preview\" ? \"bg-gradient-to-br from-blue-500 to-blue-700 ring-4 ring-blue-200 animate-pulse\" : \"bg-gray-400\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M10 12a2 2 0 100-4 2 2 0 000 4z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 313,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 314,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mt-3 font-semibold text-sm sm:text-base text-center\",\n                                                    children: \"معاينة وتصدير\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 21\n                                                }, this),\n                                                currentStep === \"preview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-1 text-xs text-blue-500 animate-pulse\",\n                                                    children: \"جاري المعاينة...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-full px-4 py-2 shadow-sm border border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-blue-500 rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600\",\n                                            children: [\n                                                \"التقدم: \",\n                                                currentStep === \"client\" ? \"33%\" : currentStep === \"invoice\" ? \"66%\" : \"100%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 327,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 224,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"min-h-screen bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8\",\n                    children: [\n                        currentPage === \"home\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-8 sm:mb-10\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-l from-white to-gray-50 rounded-xl shadow-sm p-6 sm:p-8 border border-gray-200 hover:shadow-md transition-shadow duration-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-4 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 rounded-full shadow-md \".concat(currentStep === \"client\" ? \"bg-blue-600\" : currentStep === \"invoice\" ? \"bg-green-600\" : \"bg-purple-600\"),\n                                                        children: [\n                                                            currentStep === \"client\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-6 h-6 text-white\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 355,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 354,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            currentStep === \"invoice\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-6 h-6 text-white\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 360,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 359,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            currentStep === \"preview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-6 h-6 text-white\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M10 12a2 2 0 100-4 2 2 0 000 4z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 365,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 366,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 364,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                className: \"text-2xl sm:text-3xl font-bold text-gray-800 mb-2\",\n                                                                children: getStepTitle(currentStep)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 371,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 text-sm sm:text-base leading-relaxed\",\n                                                                children: [\n                                                                    currentStep === \"client\" && \"أدخل بيانات العميل الأساسية للبدء في إنشاء الفاتورة الجديدة\",\n                                                                    currentStep === \"invoice\" && \"اختر الخدمات المطلوبة وأدخل تفاصيل كل خدمة بدقة\",\n                                                                    currentStep === \"preview\" && \"راجع تفاصيل الفاتورة وقم بتصديرها أو تعديلها حسب الحاجة\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 372,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 p-4 rounded-lg border-l-4 \".concat(currentStep === \"client\" ? \"bg-blue-50 border-blue-400\" : currentStep === \"invoice\" ? \"bg-green-50 border-green-400\" : \"bg-purple-50 border-purple-400\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5 mt-0.5 flex-shrink-0 \".concat(currentStep === \"client\" ? \"text-blue-600\" : currentStep === \"invoice\" ? \"text-green-600\" : \"text-purple-600\"),\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 390,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 386,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm\",\n                                                            children: [\n                                                                currentStep === \"client\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium text-blue-800 mb-1\",\n                                                                            children: \"نصائح مفيدة:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 395,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                            className: \"text-blue-700 space-y-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• تأكد من صحة رقم الهاتف للتواصل\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 397,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• اختر الخدمات المطلوبة بدقة\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 398,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• يمكن ترك الحقول الاختيارية فارغة\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 399,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 396,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 394,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                currentStep === \"invoice\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium text-green-800 mb-1\",\n                                                                            children: \"نصائح مفيدة:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 405,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                            className: \"text-green-700 space-y-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• أدخل تفاصيل كل خدمة بدقة\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 407,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• تحقق من الأسعار والكميات\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 408,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• يتم حساب المجموع تلقائياً\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 409,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 406,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 404,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                currentStep === \"preview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium text-purple-800 mb-1\",\n                                                                            children: \"نصائح مفيدة:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 415,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                            className: \"text-purple-700 space-y-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• راجع جميع البيانات قبل التصدير\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 417,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• يمكن طباعة الفاتورة مباشرة\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 418,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• الفاتورة محفوظة تلقائياً\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 419,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 416,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 414,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 392,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        currentStep === \"client\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-slideInRight\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientForm__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                onSubmit: handleClientSubmit\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 19\n                                        }, this),\n                                        currentStep === \"invoice\" && currentClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-slideInLeft\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_InvoiceForm__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                client: currentClient,\n                                                onSubmit: handleInvoiceSubmit\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 438,\n                                            columnNumber: 19\n                                        }, this),\n                                        currentStep === \"preview\" && currentInvoice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-slideInUp\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_InvoicePreview__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                invoice: currentInvoice,\n                                                onEdit: handleEditInvoice,\n                                                onExportPDF: handleExportPDF\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 430,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true),\n                        currentPage === \"invoices\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-fadeIn\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_InvoicesList__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 461,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 460,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 343,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 342,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-gradient-to-r from-gray-800 to-gray-900 text-white mt-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center md:text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center md:justify-start gap-3 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-blue-600 p-2 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 476,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 475,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 474,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-bold\",\n                                                    children: \"نظام إدارة الفواتير\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 text-sm leading-relaxed\",\n                                            children: \"نظام شامل ومتطور لإدارة فواتير الخدمات مع دعم كامل للغة العربية وتصدير PDF احترافي\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 472,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center md:text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold mb-4 text-blue-400\",\n                                            children: \"الخدمات المدعومة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-sm text-gray-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 491,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"الاستشارات والتصميم\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 490,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 495,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"الباترون والطباعة\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 494,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 499,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"التصنيع والعينات\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 498,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 503,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"الشحن والتسويق\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 502,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 507,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"التصوير والمنتجات\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 506,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 489,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 487,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center md:text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold mb-4 text-green-400\",\n                                            children: \"المميزات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 515,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-sm text-gray-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-green-400\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 519,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 518,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"ترقيم تسلسلي للفواتير\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 517,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-green-400\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 525,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 524,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"تصدير PDF احترافي\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 523,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-green-400\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 531,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 530,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"حساب المدفوعات التلقائي\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 529,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-green-400\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 537,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 536,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"واجهة عربية متجاوبة\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 535,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-green-400\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 543,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 542,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"إدارة شاملة للعملاء\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 541,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 516,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 514,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 470,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-700 mt-8 pt-6 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 text-sm\",\n                                    children: \"\\xa9 2025 نظام إدارة الفواتير. جميع الحقوق محفوظة.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 552,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 text-xs mt-2\",\n                                    children: \"تم التطوير بأحدث التقنيات لضمان الأداء والموثوقية\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 555,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 551,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 469,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 468,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\infapapp\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 151,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"iTdcMIUfaCtORPB+2LN80eyasrs=\", false, function() {\n    return [\n        _store_invoiceStore__WEBPACK_IMPORTED_MODULE_6__.useInvoiceStore\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});