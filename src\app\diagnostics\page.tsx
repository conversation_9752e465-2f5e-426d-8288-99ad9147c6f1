'use client';

import React from 'react';
import StorageDiagnostics from '@/components/StorageDiagnostics';

export default function DiagnosticsPage() {
  return (
    <div className="min-h-screen bg-gray-100 py-8">
      <div className="container mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">صفحة تشخيص النظام</h1>
          <p className="text-gray-600">أداة شاملة لتشخيص وإصلاح مشاكل قاعدة البيانات المحلية</p>
        </div>
        
        <StorageDiagnostics />
        
        <div className="mt-8 text-center">
          <a 
            href="/"
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors inline-flex items-center gap-2"
          >
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd"/>
            </svg>
            العودة للصفحة الرئيسية
          </a>
        </div>
      </div>
    </div>
  );
}
