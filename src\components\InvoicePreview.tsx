'use client';

import React, { useState, useEffect } from 'react';
import { Invoice } from '@/types';
import { useSettingsStore } from '@/store/settingsStore';
import { generateInvoicePDF, generateInvoicePDFFromHTML } from '@/utils/pdfGenerator';

interface InvoicePreviewProps {
  invoice: Invoice;
  onEdit: () => void;
  onExportPDF: () => void;
}

// دالة عرض رسائل التأكيد المحسنة
const showSuccessMessage = (message: string) => {
  // إنشاء عنصر toast مؤقت
  const toast = document.createElement('div');
  toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300';
  toast.innerHTML = `
    <div class="flex items-center gap-2">
      <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
      </svg>
      <span>${message}</span>
    </div>
  `;

  document.body.appendChild(toast);

  // إزالة الرسالة بعد 3 ثوان
  setTimeout(() => {
    toast.style.transform = 'translateX(100%)';
    setTimeout(() => {
      if (document.body.contains(toast)) {
        document.body.removeChild(toast);
      }
    }, 300);
  }, 3000);
};

const showErrorMessage = (message: string) => {
  const toast = document.createElement('div');
  toast.className = 'fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300';
  toast.innerHTML = `
    <div class="flex items-center gap-2">
      <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
      </svg>
      <span>${message}</span>
    </div>
  `;

  document.body.appendChild(toast);

  setTimeout(() => {
    toast.style.transform = 'translateX(100%)';
    setTimeout(() => {
      if (document.body.contains(toast)) {
        document.body.removeChild(toast);
      }
    }, 300);
  }, 4000);
};

const InvoicePreview: React.FC<InvoicePreviewProps> = ({
  invoice,
  onEdit,
  onExportPDF
}) => {
  const [showCompanySettings, setShowCompanySettings] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const { settings, loadSettings } = useSettingsStore();

  // Load company settings on component mount
  useEffect(() => {
    loadSettings();
  }, [loadSettings]);

  // دالة تصدير PDF محسنة مع رسائل التأكيد
  const handleExportPDF = async () => {
    setIsExporting(true);
    try {
      // إضافة كلاس التحسين للعنصر
      const element = document.getElementById('invoice-content');
      if (element) {
        element.classList.add('pdf-optimized');
      }

      // محاولة الطريقة المحسنة أولاً
      await generateInvoicePDF(invoice);
      showSuccessMessage('تم تصدير PDF بنجاح! 🎉');

      // إزالة كلاس التحسين
      if (element) {
        element.classList.remove('pdf-optimized');
      }
    } catch (error) {
      console.error('PDF Export Error:', error);

      // محاولة الطريقة البديلة
      try {
        await generateInvoicePDFFromHTML(invoice);
        showSuccessMessage('تم تصدير PDF بنجاح! 🎉');
      } catch (fallbackError) {
        console.error('Fallback PDF Export Error:', fallbackError);
        showErrorMessage('فشل في تصدير PDF. يرجى المحاولة مرة أخرى.');
      }
    } finally {
      setIsExporting(false);
    }
  };
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-EG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      calendar: 'gregory', // التقويم الميلادي
    });
  };

  const formatCurrency = (amount: number | string | undefined | null) => {
    // التحقق من صحة القيمة وتحويلها لرقم
    const numericAmount = typeof amount === 'number' ? amount : parseFloat(String(amount || 0));

    // التحقق من أن النتيجة رقم صحيح
    if (isNaN(numericAmount)) {
      return '0.00 جنيه';
    }

    return `${numericAmount.toFixed(2)} جنيه`;
  };

  const getServiceName = (serviceType: string) => {
    const serviceNames: { [key: string]: string } = {
      consultation: 'استشارات',
      pattern: 'باترون',
      pattern_printing: 'طباعة باترون',
      manufacturing: 'تصنيع',
      samples: 'عينات',
      shipping: 'شحن/تعبئة',
      marketing: 'تسويق',
      photography: 'تصوير',
      products: 'منتجات',
      raw_purchases: 'مشتريات خام',
    };
    return serviceNames[serviceType] || serviceType;
  };

  const getPaymentMethodName = (method: string) => {
    const methods: { [key: string]: string } = {
      cash: 'نقدي',
      card: 'بطاقة',
      transfer: 'تحويل',
      installment: 'تقسيط',
    };
    return methods[method] || method;
  };

  const getContactMethodName = (method: string) => {
    const methods: { [key: string]: string } = {
      whatsapp: 'واتساب',
      meta: 'ميتا',
      meeting: 'مقابلة',
      phone: 'هاتف',
    };
    return methods[method] || method;
  };

  // Generate client code based on name and phone
  const generateClientCode = (name: string, phone: string) => {
    const nameCode = name.substring(0, 3).toUpperCase();
    const phoneCode = phone.substring(-4);
    return `${nameCode}-${phoneCode}`;
  };

  // دالة فتح صفحة الطباعة في تبويب جديد محسنة
  const openPrintView = (invoice: Invoice) => {
    try {
      // إنشاء HTML للفاتورة مع تنسيق الطباعة
      const printHTML = createPrintableInvoiceHTML(invoice);

      // فتح نافذة جديدة
      const printWindow = window.open('', '_blank');
      if (printWindow) {
        printWindow.document.write(printHTML);
        printWindow.document.close();

        // انتظار تحميل المحتوى ثم فتح حوار الطباعة
        printWindow.onload = () => {
          setTimeout(() => {
            printWindow.print();
            showSuccessMessage('تم فتح صفحة الطباعة بنجاح! 🖨️');
          }, 500);
        };
      } else {
        showErrorMessage('فشل في فتح نافذة الطباعة. يرجى السماح بالنوافذ المنبثقة.');
      }
    } catch (error) {
      showErrorMessage('حدث خطأ أثناء تحضير صفحة الطباعة.');
      console.error('Print Error:', error);
    }
  };

  // دالة إنشاء HTML للطباعة
  const createPrintableInvoiceHTML = (invoice: Invoice): string => {
    const logoSrc = settings.companyLogo || '';

    return `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة ${invoice.id} - ${invoice.client.name}</title>
    <style>
        @page {
            size: A4;
            margin: 15mm;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            line-height: 1.4;
            color: #333;
            background: white;
        }

        .invoice-container {
            max-width: 100%;
            margin: 0 auto;
            background: white;
            padding: 20px;
        }

        /* Header Section */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(to left, #f0f8ff, white);
            border: 2px solid #2196f3;
            border-radius: 10px;
        }

        .invoice-info {
            flex: 1;
            text-align: right;
        }

        .invoice-info h2 {
            color: #2196f3;
            font-size: 24px;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .info-box {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border: 1px solid #ddd;
            max-width: 400px;
        }

        .info-row {
            margin-bottom: 8px;
            font-size: 14px;
        }

        .info-label {
            font-weight: bold;
            color: #555;
        }

        .info-value {
            color: #333;
        }

        .logo-section {
            flex-shrink: 0;
            text-align: center;
            margin-left: 30px;
        }

        .logo-section img {
            max-width: 180px;
            max-height: 180px;
            object-fit: contain;
        }

        .company-description {
            margin-top: 15px;
            font-size: 13px;
            color: #666;
            max-width: 200px;
            line-height: 1.5;
        }

        /* Services Table */
        .services-section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #2196f3;
        }

        .services-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            border: 1px solid #ddd;
            margin: 15px 0;
            table-layout: fixed;
        }

        .services-table th {
            background-color: #2196f3;
            color: white;
            padding: 12px 8px;
            text-align: center;
            font-weight: bold;
            border: 1px solid #ddd;
            font-size: 13px;
        }

        .services-table td {
            padding: 10px 8px;
            border: 1px solid #ddd;
            text-align: center;
            vertical-align: middle;
            word-wrap: break-word;
            font-size: 12px;
            line-height: 1.3;
        }

        .services-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        .services-table tr:nth-child(odd) {
            background-color: white;
        }

        .service-type {
            color: #2196f3;
            font-weight: bold;
        }

        .service-details {
            text-align: right;
            white-space: normal;
        }

        .service-price {
            font-weight: bold;
            color: #2196f3;
        }

        .service-total {
            font-weight: bold;
            color: #4caf50;
        }

        /* Total Summary */
        .total-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(to right, #e8f5e8, #f0f8f0);
            padding: 20px;
            border-radius: 10px;
            border: 2px solid #4caf50;
            margin-bottom: 30px;
        }

        .total-details {
            text-align: right;
        }

        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            min-width: 300px;
        }

        .total-label {
            font-weight: bold;
            color: #555;
            margin-left: 20px;
        }

        .total-value {
            font-weight: bold;
        }

        .grand-total {
            font-size: 20px;
            color: #4caf50;
        }

        .paid-amount {
            font-size: 16px;
            color: #2196f3;
        }

        .remaining-amount {
            font-size: 16px;
        }

        .remaining-positive {
            color: #f44336;
        }

        .remaining-zero {
            color: #4caf50;
        }

        .payment-stamp {
            background: linear-gradient(135deg, #4caf50, #45a049);
            color: white;
            padding: 15px;
            border-radius: 15px;
            text-align: center;
            transform: rotate(3deg);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            min-width: 120px;
        }

        .stamp-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stamp-status {
            font-size: 12px;
            font-weight: bold;
        }

        /* Footer */
        .footer {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-top: 40px;
            padding: 20px 0;
            border-top: 2px solid #2196f3;
            min-height: 120px;
        }

        .footer-left {
            width: 45%;
            text-align: left;
        }

        .contact-info {
            font-size: 12px;
            color: #666;
            line-height: 1.8;
            margin-bottom: 10px;
            text-align: left;
        }

        .contact-info strong {
            color: #333;
            font-size: 13px;
        }

        .footer-right {
            width: 50%;
            text-align: right;
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            justify-content: flex-start;
        }

        .company-stamp {
            position: relative;
            transform: rotate(-8deg);
            opacity: 0.9;
            border-radius: 50%;
            padding: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            width: 120px;
            height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
        }

        .company-stamp img {
            width: 100px;
            height: 100px;
            object-fit: contain;
            border-radius: 50%;
        }

        .company-stamp .stamp-content {
            background: rgba(33, 150, 243, 0.1);
            border: 2px solid #2196f3;
            border-radius: 50%;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .stamp-content {
            text-align: center;
            color: #2196f3;
            font-weight: bold;
            font-size: 14px;
            line-height: 1.2;
        }

        .stamp-title {
            font-size: 16px;
            margin-bottom: 5px;
        }

        .stamp-subtitle {
            font-size: 12px;
            opacity: 0.8;
        }

        .headquarters-info {
            font-size: 12px;
            color: #555;
            text-align: right;
            line-height: 1.6;
        }

        .headquarters-info strong {
            color: #333;
            font-size: 13px;
        }

        .thank-you {
            text-align: center;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #ddd;
            color: #666;
            font-size: 14px;
        }

        /* Print specific styles */
        @media print {
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                margin: 0;
                padding: 0;
            }

            .invoice-container {
                padding: 0;
                margin: 0;
                max-width: 100%;
            }

            .header {
                break-inside: avoid;
                page-break-inside: avoid;
                margin-bottom: 20px;
            }

            .services-section {
                break-inside: avoid;
                page-break-inside: avoid;
                margin-bottom: 20px;
            }

            .services-table {
                break-inside: avoid;
                page-break-inside: avoid;
            }

            .total-section {
                break-inside: avoid;
                page-break-inside: avoid;
                margin-bottom: 20px;
            }

            .footer {
                break-inside: avoid;
                page-break-inside: avoid;
                position: relative;
                clear: both;
                margin-top: 30px;
            }

            .footer-left,
            .footer-right {
                float: none;
                display: inline-block;
                vertical-align: top;
            }

            .company-stamp {
                transform: rotate(-5deg);
            }

            .thank-you {
                break-inside: avoid;
                page-break-inside: avoid;
                margin-top: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <!-- Header Section -->
        <div class="header">
            <div class="invoice-info">
                <h2>فاتورة خدمات</h2>
                <div class="info-box">
                    <div class="info-row">
                        <span class="info-label">رقم الفاتورة: </span>
                        <span class="info-value">${invoice.id}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">التاريخ: </span>
                        <span class="info-value">${formatDate(invoice.createdAt)}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">اسم العميل: </span>
                        <span class="info-value">${invoice.client.name}</span>
                    </div>
                    ${invoice.client.businessName ? `
                    <div class="info-row">
                        <span class="info-label">الاسم التجاري: </span>
                        <span class="info-value">${invoice.client.businessName}</span>
                    </div>
                    ` : ''}
                    <div class="info-row">
                        <span class="info-label">رقم الهاتف: </span>
                        <span class="info-value">${invoice.client.phone}</span>
                    </div>
                    ${invoice.client.province ? `
                    <div class="info-row">
                        <span class="info-label">المحافظة: </span>
                        <span class="info-value">${invoice.client.province}</span>
                    </div>
                    ` : ''}
                    <div class="info-row">
                        <span class="info-label">كود العميل: </span>
                        <span class="info-value">${invoice.client.clientCode || generateClientCode(invoice.client.name, invoice.client.phone)}</span>
                    </div>
                    <div class="info-row" style="border-top: 1px solid #ddd; padding-top: 8px; margin-top: 8px;">
                        <span class="info-label">وسيلة التواصل: </span>
                        <span class="info-value">${getContactMethodName(invoice.client.contactMethod)}</span>
                    </div>
                </div>
            </div>

            <div class="logo-section">
                ${logoSrc ? `<img src="${logoSrc}" alt="شعار الشركة">` : `
                <div style="width: 180px; height: 180px; border: 2px dashed #ccc; display: flex; align-items: center; justify-content: center; background: #f9f9f9;">
                    <span style="color: #999;">شعار الشركة</span>
                </div>
                `}
                <div class="company-description">
                    ${settings.description || 'شركة متخصصة في جميع خدمات صناعة الملابس الجاهزة'}
                </div>
            </div>
        </div>

        <!-- Services Section -->
        <div class="services-section">
            <h3 class="section-title">تفاصيل الخدمات والمنتجات</h3>

            <table class="services-table">
                <thead>
                    <tr>
                        <th style="width: 50px;">م</th>
                        <th style="width: 120px;">نوع الخدمة</th>
                        <th style="width: 200px;">التفاصيل</th>
                        <th style="width: 100px;">الكمية/المقاس</th>
                        <th style="width: 120px;">السعر</th>
                        <th style="width: 120px;">المجموع</th>
                    </tr>
                </thead>
                <tbody>
                    ${invoice.services.map((service, index) => {
                      // Calculate service details (same logic as in component)
                      let serviceTotal = 0;
                      let serviceDetails = '';
                      let quantity = '';
                      let unitPrice = 0;

                      if (service.serviceType === 'consultation') {
                        const consultationService = service as any;
                        serviceTotal = consultationService.cost || 0;
                        serviceDetails = `${consultationService.topic || 'استشارة'} - ${consultationService.hours || 0} ساعة`;
                        quantity = `${consultationService.hours || 0} ساعة`;
                        unitPrice = consultationService.cost || 0;
                      } else if (service.serviceType === 'pattern') {
                        const patternService = service as any;
                        serviceTotal = patternService.models?.reduce((sum: number, model: any) => sum + (model.finalAmount || 0), 0) || 0;
                        serviceDetails = `${patternService.models?.length || 0} نموذج باترون`;
                        quantity = `${patternService.models?.length || 0} نموذج`;
                        unitPrice = serviceTotal / (patternService.models?.length || 1);
                      } else if (service.serviceType === 'pattern_printing') {
                        const printingService = service as any;
                        serviceTotal = printingService.files?.reduce((sum: number, file: any) => sum + (file.cost || 0), 0) || 0;
                        serviceDetails = `طباعة ${printingService.files?.length || 0} ملف`;
                        quantity = `${printingService.files?.reduce((sum: number, file: any) => sum + (file.meters || 0), 0) || 0} متر`;
                        unitPrice = serviceTotal;
                      } else if (service.serviceType === 'shipping') {
                        const shippingService = service as any;
                        serviceTotal = shippingService.total || 0;
                        serviceDetails = `شحن ${shippingService.item || 'منتج'}`;
                        quantity = `${shippingService.quantity || 1}`;
                        unitPrice = shippingService.unitPrice || 0;
                      } else if (service.serviceType === 'products') {
                        const productsService = service as any;
                        serviceTotal = productsService.items?.reduce((sum: number, item: any) => sum + (item.total || 0), 0) || 0;
                        serviceDetails = `${productsService.items?.length || 0} منتج`;
                        quantity = `${productsService.items?.reduce((sum: number, item: any) => sum + (item.quantity || 0), 0) || 0}`;
                        unitPrice = serviceTotal / (productsService.items?.reduce((sum: number, item: any) => sum + (item.quantity || 0), 0) || 1);
                      } else {
                        serviceTotal = (service as any).cost || (service as any).total || 0;
                        serviceDetails = getServiceName(service.serviceType);
                        quantity = '1';
                        unitPrice = serviceTotal;
                      }

                      return `
                        <tr>
                            <td>${index + 1}</td>
                            <td class="service-type">${getServiceName(service.serviceType)}</td>
                            <td class="service-details">${serviceDetails}</td>
                            <td>${quantity}</td>
                            <td class="service-price">${formatCurrency(unitPrice)}</td>
                            <td class="service-total">${formatCurrency(serviceTotal)}</td>
                        </tr>
                      `;
                    }).join('')}
                </tbody>
            </table>
        </div>

        <!-- Total Summary -->
        <div class="total-section">
            <div class="total-details">
                <div class="total-row">
                    <span class="total-label">المجموع الكلي:</span>
                    <span class="total-value grand-total">${formatCurrency(invoice.total)}</span>
                </div>
                <div class="total-row">
                    <span class="total-label">المبلغ المدفوع:</span>
                    <span class="total-value paid-amount">${formatCurrency(invoice.paidAmount || 0)}</span>
                </div>
                <div class="total-row">
                    <span class="total-label">المبلغ المتبقي:</span>
                    <span class="total-value remaining-amount ${(invoice.remainingAmount || 0) > 0 ? 'remaining-positive' : 'remaining-zero'}">
                        ${formatCurrency(invoice.remainingAmount || 0)}
                    </span>
                </div>
                <div class="total-row" style="border-top: 1px solid #4caf50; padding-top: 8px; margin-top: 8px;">
                    <span class="total-label">طريقة الدفع:</span>
                    <span class="total-value">${getPaymentMethodName(invoice.paymentMethod)}</span>
                </div>
            </div>

            <div class="payment-stamp">
                <div class="stamp-title">OKA Group</div>
                <div class="stamp-status">${invoice.paymentStatus || 'غير محدد'}</div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <!-- Left side: Contact Information -->
            <div class="footer-left">
                <div class="contact-info">
                    <div><strong>معلومات التواصل:</strong></div>
                    <div>📧 البريد الإلكتروني: <EMAIL></div>
                    <div>📞 للشكاوى والاقتراحات: 0114954118</div>
                </div>
            </div>

            <!-- Right side: Headquarters + Company Stamp -->
            <div class="footer-right">
                <div class="headquarters-info">
                    <div><strong>المقر الإداري:</strong></div>
                    <div>73 ش 6 اكتوبر الجراش جسر السويس</div>
                </div>

                <div class="company-stamp">
                    ${settings.companyStamp ? `
                    <img
                        src="${settings.companyStamp}"
                        alt="ختم الشركة"
                        style="width: 100px; height: 100px; object-fit: contain;"
                    >
                    ` : `
                    <div class="stamp-content">
                        <div class="stamp-title">OKA</div>
                        <div class="stamp-subtitle">GROUP</div>
                    </div>
                    `}
                </div>
            </div>
        </div>

        <div class="thank-you">
            شكراً لتعاملكم معنا
        </div>
    </div>
</body>
</html>
    `;
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* أزرار التحكم */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <h1 className="text-2xl sm:text-3xl font-bold text-gray-800">معاينة الفاتورة</h1>
        <div className="flex flex-wrap gap-3">
          <button
            onClick={() => setShowCompanySettings(true)}
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2"
          >
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd"/>
            </svg>
            إعدادات الشركة
          </button>
          <button onClick={onEdit} className="btn-secondary">
            <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"/>
            </svg>
            تعديل
          </button>
          <button
            onClick={handleExportPDF}
            disabled={isExporting}
            className={`btn-success ${isExporting ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            {isExporting ? (
              <>
                <svg className="w-4 h-4 mr-2 animate-spin" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4 2a2 2 0 00-2 2v11a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm0 2h12v11H4V4z" clipRule="evenodd"/>
                </svg>
                جاري التصدير...
              </>
            ) : (
              <>
                <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd"/>
                </svg>
                تصدير PDF
              </>
            )}
          </button>
          <button
            onClick={() => openPrintView(invoice)}
            className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2"
          >
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M5 4v3H4a2 2 0 00-2 2v3a2 2 0 002 2h1v2a2 2 0 002 2h6a2 2 0 002-2v-2h1a2 2 0 002-2V9a2 2 0 00-2-2h-1V4a2 2 0 00-2-2H7a2 2 0 00-2 2zm8 0H7v3h6V4zm0 8H7v4h6v-4z" clipRule="evenodd"/>
            </svg>
            طباعة
          </button>
        </div>
      </div>

      {/* محتوى الفاتورة */}
      <div
        id="invoice-content"
        className="bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden print-break-inside-avoid"
        style={{
          fontFamily: 'Arial, sans-serif',
          lineHeight: '1.4',
          position: 'relative',
          zIndex: 1,
          direction: 'rtl',
          width: '100%',
          maxWidth: '794px',
          margin: '0 auto'
        }}
      >
        {/* Header Section - New Layout */}
        <div className="bg-gradient-to-l from-blue-50 to-white p-6 border-b-2 border-blue-200 print-header print-break-inside-avoid">
          <div className="flex justify-between items-start">

            {/* Right side - Invoice Information Column (Arabic RTL) */}
            <div className="w-1/2 text-right space-y-3">
              <h2 className="text-2xl font-bold text-blue-600 mb-4">فاتورة خدمات</h2>

              <div className="bg-white p-3 rounded-lg shadow-sm border border-gray-200 max-w-sm">
                <div className="space-y-1 text-sm">
                  <div className="text-right">
                    <span className="font-semibold text-gray-700">رقم الفاتورة: </span>
                    <span className="text-blue-600 font-bold">{invoice.id}</span>
                  </div>

                  <div className="text-right">
                    <span className="font-semibold text-gray-700">التاريخ: </span>
                    <span className="text-gray-800">{formatDate(invoice.createdAt)}</span>
                  </div>

                  <div className="text-right">
                    <span className="font-semibold text-gray-700">اسم العميل: </span>
                    <span className="text-gray-800">{invoice.client.name}</span>
                  </div>

                  {invoice.client.businessName && (
                    <div className="text-right">
                      <span className="font-semibold text-gray-700">الاسم التجاري: </span>
                      <span className="text-gray-800">{invoice.client.businessName}</span>
                    </div>
                  )}

                  <div className="text-right">
                    <span className="font-semibold text-gray-700">رقم الهاتف: </span>
                    <span className="text-gray-800">{invoice.client.phone}</span>
                  </div>

                  {invoice.client.province && (
                    <div className="text-right">
                      <span className="font-semibold text-gray-700">المحافظة: </span>
                      <span className="text-gray-800">{invoice.client.province}</span>
                    </div>
                  )}

                  <div className="text-right">
                    <span className="font-semibold text-gray-700">كود العميل: </span>
                    <span className="text-blue-600 font-bold">{invoice.client.clientCode || generateClientCode(invoice.client.name, invoice.client.phone)}</span>
                  </div>

                  <div className="border-t border-gray-200 pt-2 mt-2">
                    <div className="text-right">
                      <span className="font-semibold text-gray-700">وسيلة التواصل: </span>
                      <span className="text-blue-600 font-bold">{getContactMethodName(invoice.client.contactMethod)}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Left side - Company Logo + Description */}
            <div className="flex-shrink-0 ml-8 text-center">
              <div className="mb-4">
                {settings.companyLogo ? (
                  <img
                    src={settings.companyLogo}
                    alt="شعار الشركة"
                    className="w-48 h-48 object-contain mx-auto"
                  />
                ) : (
                  <div className="w-48 h-48 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50 mx-auto">
                    <div className="text-center text-gray-400">
                      <svg className="w-20 h-20 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd"/>
                      </svg>
                      <p className="text-sm">شعار الشركة</p>
                    </div>
                  </div>
                )}
              </div>

              {/* Company Description */}
              <div className="max-w-xs mx-auto">
                <p className="text-sm text-gray-600 leading-relaxed font-medium">
                  {settings.description}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Content Section - Table Layout */}
        <div className="p-6">

          {/* Services Table */}
          <div className="mb-8">
            <h3 className="text-xl font-bold text-gray-800 mb-4 border-b-2 border-blue-200 pb-2">تفاصيل الخدمات والمنتجات</h3>

            <div className="overflow-x-auto mb-6 print-break-inside-avoid">
              <table
                className="w-full bg-white text-sm print-table"
                style={{
                  borderCollapse: 'separate',
                  borderSpacing: '0',
                  border: '1px solid #ddd',
                  margin: '20px 0',
                  tableLayout: 'fixed'
                }}
              >
                <thead>
                  <tr style={{ backgroundColor: '#2196f3', color: 'white' }}>
                    <th
                      className="border border-gray-300 text-center font-semibold"
                      style={{
                        width: '50px',
                        padding: '12px 8px',
                        border: '1px solid #ddd'
                      }}
                    >
                      م
                    </th>
                    <th
                      className="border border-gray-300 text-center font-semibold"
                      style={{
                        width: '120px',
                        padding: '12px 8px',
                        border: '1px solid #ddd'
                      }}
                    >
                      نوع الخدمة
                    </th>
                    <th
                      className="border border-gray-300 text-center font-semibold"
                      style={{
                        width: '200px',
                        padding: '12px 8px',
                        border: '1px solid #ddd'
                      }}
                    >
                      التفاصيل
                    </th>
                    <th
                      className="border border-gray-300 text-center font-semibold"
                      style={{
                        width: '100px',
                        padding: '12px 8px',
                        border: '1px solid #ddd'
                      }}
                    >
                      الكمية/المقاس
                    </th>
                    <th
                      className="border border-gray-300 text-center font-semibold"
                      style={{
                        width: '120px',
                        padding: '12px 8px',
                        border: '1px solid #ddd'
                      }}
                    >
                      السعر
                    </th>
                    <th
                      className="border border-gray-300 text-center font-semibold"
                      style={{
                        width: '120px',
                        padding: '12px 8px',
                        border: '1px solid #ddd'
                      }}
                    >
                      المجموع
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {invoice.services.map((service, index) => {
                    // Calculate service total based on service type
                    let serviceTotal = 0;
                    let serviceDetails = '';
                    let quantity = '';
                    let unitPrice = 0;

                    if (service.serviceType === 'consultation') {
                      const consultationService = service as any;
                      serviceTotal = consultationService.cost || 0;
                      serviceDetails = `${consultationService.topic || 'استشارة'} - ${consultationService.hours || 0} ساعة`;
                      quantity = `${consultationService.hours || 0} ساعة`;
                      unitPrice = consultationService.cost || 0;
                    } else if (service.serviceType === 'pattern') {
                      const patternService = service as any;
                      serviceTotal = patternService.models?.reduce((sum: number, model: any) => sum + (model.finalAmount || 0), 0) || 0;
                      serviceDetails = `${patternService.models?.length || 0} نموذج باترون`;
                      quantity = `${patternService.models?.length || 0} نموذج`;
                      unitPrice = serviceTotal / (patternService.models?.length || 1);
                    } else if (service.serviceType === 'pattern_printing') {
                      const printingService = service as any;
                      serviceTotal = printingService.files?.reduce((sum: number, file: any) => sum + (file.cost || 0), 0) || 0;
                      serviceDetails = `طباعة ${printingService.files?.length || 0} ملف`;
                      quantity = `${printingService.files?.reduce((sum: number, file: any) => sum + (file.meters || 0), 0) || 0} متر`;
                      unitPrice = serviceTotal;
                    } else if (service.serviceType === 'shipping') {
                      const shippingService = service as any;
                      serviceTotal = shippingService.total || 0;
                      serviceDetails = `شحن ${shippingService.item || 'منتج'}`;
                      quantity = `${shippingService.quantity || 1}`;
                      unitPrice = shippingService.unitPrice || 0;
                    } else if (service.serviceType === 'products') {
                      const productsService = service as any;
                      serviceTotal = productsService.items?.reduce((sum: number, item: any) => sum + (item.total || 0), 0) || 0;
                      serviceDetails = `${productsService.items?.length || 0} منتج`;
                      quantity = `${productsService.items?.reduce((sum: number, item: any) => sum + (item.quantity || 0), 0) || 0}`;
                      unitPrice = serviceTotal / (productsService.items?.reduce((sum: number, item: any) => sum + (item.quantity || 0), 0) || 1);
                    } else {
                      // Default for other service types
                      serviceTotal = (service as any).cost || (service as any).total || 0;
                      serviceDetails = getServiceName(service.serviceType);
                      quantity = '1';
                      unitPrice = serviceTotal;
                    }

                    return (
                      <tr
                        key={service.id}
                        style={{
                          backgroundColor: index % 2 === 0 ? '#f9f9f9' : 'white'
                        }}
                      >
                        <td
                          style={{
                            padding: '10px 8px',
                            border: '1px solid #ddd',
                            textAlign: 'center',
                            verticalAlign: 'middle',
                            wordWrap: 'break-word',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap'
                          }}
                        >
                          {index + 1}
                        </td>
                        <td
                          style={{
                            padding: '10px 8px',
                            border: '1px solid #ddd',
                            textAlign: 'center',
                            verticalAlign: 'middle',
                            wordWrap: 'break-word',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap',
                            color: '#2196f3',
                            fontWeight: 'bold'
                          }}
                        >
                          {getServiceName(service.serviceType)}
                        </td>
                        <td
                          style={{
                            padding: '10px 8px',
                            border: '1px solid #ddd',
                            textAlign: 'right',
                            verticalAlign: 'middle',
                            wordWrap: 'break-word',
                            overflow: 'hidden',
                            whiteSpace: 'normal',
                            maxWidth: '200px'
                          }}
                        >
                          {serviceDetails}
                        </td>
                        <td
                          style={{
                            padding: '10px 8px',
                            border: '1px solid #ddd',
                            textAlign: 'center',
                            verticalAlign: 'middle',
                            wordWrap: 'break-word',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap'
                          }}
                        >
                          {quantity}
                        </td>
                        <td
                          style={{
                            padding: '10px 8px',
                            border: '1px solid #ddd',
                            textAlign: 'center',
                            verticalAlign: 'middle',
                            wordWrap: 'break-word',
                            overflow: 'hidden',
                            whiteSpace: 'normal',
                            fontWeight: 'bold',
                            color: '#2196f3'
                          }}
                        >
                          {formatCurrency(unitPrice)}
                        </td>
                        <td
                          style={{
                            padding: '10px 8px',
                            border: '1px solid #ddd',
                            textAlign: 'center',
                            verticalAlign: 'middle',
                            wordWrap: 'break-word',
                            overflow: 'hidden',
                            whiteSpace: 'normal',
                            fontWeight: 'bold',
                            color: '#4caf50'
                          }}
                        >
                          {formatCurrency(serviceTotal)}
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>

          {/* Total Summary Section - Right after table */}
          <div className="mb-8 flex justify-between items-center bg-gradient-to-r from-green-50 to-green-100 p-6 rounded-lg border-2 border-green-200">
            {/* Total Details - Right Side */}
            <div className="text-right space-y-2">
              <div className="flex justify-between items-center gap-8">
                <span className="text-lg font-semibold text-gray-700">المجموع الكلي:</span>
                <span className="text-2xl font-bold text-green-600">{formatCurrency(invoice.total)}</span>
              </div>
              <div className="flex justify-between items-center gap-8">
                <span className="text-lg font-semibold text-gray-700">المبلغ المدفوع:</span>
                <span className="text-xl font-bold text-blue-600">{formatCurrency(invoice.paidAmount || 0)}</span>
              </div>
              <div className="flex justify-between items-center gap-8">
                <span className="text-lg font-semibold text-gray-700">المبلغ المتبقي:</span>
                <span className={`text-xl font-bold ${(invoice.remainingAmount || 0) > 0 ? 'text-red-600' : 'text-green-600'}`}>
                  {formatCurrency(invoice.remainingAmount || 0)}
                </span>
              </div>
              <div className="pt-2 border-t border-green-300">
                <span className="text-lg font-semibold text-gray-700">طريقة الدفع: </span>
                <span className="text-lg font-bold text-gray-800">{getPaymentMethodName(invoice.paymentMethod)}</span>
              </div>
            </div>

            {/* Modern Payment Status Badge - Left Side */}
            <div className="flex-shrink-0 ml-8">
              <div className={`relative overflow-hidden rounded-2xl p-6 text-center shadow-xl transform rotate-3 ${
                invoice.paymentStatus === 'مدفوع بالكامل' ? 'bg-gradient-to-br from-green-400 to-green-600' :
                invoice.paymentStatus === 'مدفوع جزئياً' ? 'bg-gradient-to-br from-yellow-400 to-orange-500' :
                'bg-gradient-to-br from-red-400 to-red-600'
              }`}>
                <div className="absolute inset-0 bg-white opacity-10"></div>
                <div className="relative z-10">
                  <div className="text-white font-bold text-lg mb-1">OKA Group</div>
                  <div className="text-white font-bold text-sm">
                    {invoice.paymentStatus || 'غير محدد'}
                  </div>
                  <div className="mt-2">
                    <svg className="w-6 h-6 text-white mx-auto" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"/>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Additional Information - Shipping and Notes Only */}
          {(invoice.requiresShipping || invoice.shippingAddress || invoice.notes) && (
            <div className="mb-8">
              <div className="bg-gradient-to-r from-yellow-50 to-yellow-100 p-6 rounded-lg border border-yellow-200">
                <h4 className="text-lg font-bold text-yellow-700 mb-4">معلومات إضافية</h4>
                <div className="space-y-3">
                  {invoice.requiresShipping && (
                    <div className="flex justify-between items-center">
                      <span className="font-semibold text-gray-700">يتطلب شحن:</span>
                      <span className="text-green-600 font-semibold">نعم</span>
                    </div>
                  )}

                  {invoice.shippingAddress && (
                    <div className="pt-2 border-t border-yellow-200">
                      <span className="font-semibold text-gray-700 block mb-1">عنوان الشحن:</span>
                      <span className="text-gray-800 text-sm">{invoice.shippingAddress}</span>
                    </div>
                  )}

                  {invoice.notes && (
                    <div className="pt-2 border-t border-yellow-200">
                      <span className="font-semibold text-gray-700 block mb-1">ملاحظات:</span>
                      <span className="text-gray-800 text-sm">{invoice.notes}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer Section - Enhanced Layout */}
        <div className="bg-gradient-to-r from-gray-100 to-gray-50 border-t-2 border-gray-300 p-6 relative">
          {/* Company Stamp - Overlaid like Photoshop layer */}
          <div className="absolute top-4 right-1/3 transform translate-x-1/2 z-10">
            {settings.companyStamp ? (
              <img
                src={settings.companyStamp}
                alt="ختم الشركة"
                className="w-40 h-40 object-contain transform rotate-12 opacity-90 drop-shadow-lg"
                style={{
                  filter: 'drop-shadow(2px 4px 6px rgba(0, 0, 0, 0.3))',
                }}
              />
            ) : (
              <div className="w-40 h-40 border-2 border-dashed border-gray-400 rounded-full flex items-center justify-center bg-gray-50 transform rotate-12 opacity-70">
                <div className="text-center text-gray-400">
                  <svg className="w-16 h-16 mx-auto mb-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z" clipRule="evenodd"/>
                  </svg>
                  <p className="text-sm">ختم الشركة</p>
                </div>
              </div>
            )}
          </div>

          <div className="flex justify-between items-start relative z-0">
            {/* Right side - Company Information (1/3 of page) */}
            <div className="w-1/3 text-right space-y-4 pr-4">
              <div className="space-y-3">
                {/* Company Address */}
                <div>
                  <span className="text-sm font-semibold text-gray-700">المقر الإداري: </span>
                  <span className="text-sm text-gray-600">{settings.address}</span>
                </div>

                {/* Company Slogan */}
                <div>
                  <span className="text-lg font-bold text-blue-600">{settings.slogan}</span>
                </div>

                {/* Wish Message */}
                <div>
                  <span className="text-sm text-gray-600 italic">{settings.wishMessage}</span>
                </div>
              </div>
            </div>

            {/* Left side - Contact Information (1/3 of page) */}
            <div className="w-1/3 text-left space-y-4 pl-4">
              <div className="space-y-3">
                <h4 className="text-lg font-bold text-gray-700 mb-3">معلومات التواصل</h4>

                {/* Email */}
                <div className="flex items-start gap-2">
                  <svg className="w-4 h-4 text-blue-600 flex-shrink-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
                  </svg>
                  <div>
                    <span className="text-sm font-semibold text-gray-700">البريد الإلكتروني: </span>
                    <span className="text-sm text-blue-600">{settings.email}</span>
                  </div>
                </div>

                {/* Phone */}
                <div className="flex items-start gap-2">
                  <svg className="w-4 h-4 text-green-600 flex-shrink-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
                  </svg>
                  <div>
                    <span className="text-sm font-semibold text-gray-700">للشكاوى والاقتراحات: </span>
                    <span className="text-sm text-green-600 font-bold">{settings.complaintsPhone}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Bottom Border */}
          <div className="border-t-2 border-blue-300 mt-6 pt-4">
            <div className="text-center">
              <p className="text-sm text-gray-600">شكراً لتعاملكم معنا</p>
            </div>
          </div>
        </div>
      </div>

      {/* Company Settings Modal */}
      {showCompanySettings && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            {/* Header */}
            <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-6 rounded-t-lg">
              <div className="flex justify-between items-center">
                <h2 className="text-2xl font-bold">إعدادات الشركة</h2>
                <button
                  onClick={() => setShowCompanySettings(false)}
                  className="text-white hover:text-gray-200 transition-colors"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="p-6">
              <div className="space-y-6">
                {/* Company Name */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">اسم الشركة</label>
                  <input
                    type="text"
                    value={settings.companyName}
                    onChange={(e) => {
                      const { updateSettings } = useSettingsStore.getState();
                      updateSettings({ companyName: e.target.value });
                    }}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                    placeholder="أدخل اسم الشركة"
                  />
                </div>

                {/* Email */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">البريد الإلكتروني</label>
                  <input
                    type="email"
                    value={settings.email}
                    onChange={(e) => {
                      const { updateSettings } = useSettingsStore.getState();
                      updateSettings({ email: e.target.value });
                    }}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                    placeholder="أدخل البريد الإلكتروني"
                  />
                </div>

                {/* Complaints Phone */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">هاتف الشكاوى والاقتراحات</label>
                  <input
                    type="tel"
                    value={settings.complaintsPhone}
                    onChange={(e) => {
                      const { updateSettings } = useSettingsStore.getState();
                      updateSettings({ complaintsPhone: e.target.value });
                    }}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                    placeholder="أدخل رقم هاتف الشكاوى"
                  />
                </div>

                {/* Address */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">عنوان المقر الإداري</label>
                  <textarea
                    value={settings.address}
                    onChange={(e) => {
                      const { updateSettings } = useSettingsStore.getState();
                      updateSettings({ address: e.target.value });
                    }}
                    rows={3}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none"
                    placeholder="أدخل عنوان المقر الإداري"
                  />
                </div>

                {/* Slogan */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">شعار الشركة</label>
                  <input
                    type="text"
                    value={settings.slogan}
                    onChange={(e) => {
                      const { updateSettings } = useSettingsStore.getState();
                      updateSettings({ slogan: e.target.value });
                    }}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                    placeholder="أدخل شعار الشركة"
                  />
                </div>

                {/* Wish Message */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">رسالة التمنيات</label>
                  <input
                    type="text"
                    value={settings.wishMessage}
                    onChange={(e) => {
                      const { updateSettings } = useSettingsStore.getState();
                      updateSettings({ wishMessage: e.target.value });
                    }}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                    placeholder="أدخل رسالة التمنيات"
                  />
                </div>

                {/* Company Description */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">وصف الشركة</label>
                  <textarea
                    value={settings.description}
                    onChange={(e) => {
                      const { updateSettings } = useSettingsStore.getState();
                      updateSettings({ description: e.target.value });
                    }}
                    rows={2}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none"
                    placeholder="أدخل وصف الشركة"
                  />
                </div>

                {/* Company Logo Upload */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-3">شعار الشركة</label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors">
                    {settings.companyLogo ? (
                      <div className="space-y-4">
                        <img
                          src={settings.companyLogo}
                          alt="شعار الشركة"
                          className="w-32 h-32 object-contain mx-auto border border-gray-200 rounded-lg bg-white p-2"
                        />
                        <button
                          onClick={() => {
                            const input = document.createElement('input');
                            input.type = 'file';
                            input.accept = 'image/*';
                            input.onchange = (e) => {
                              const file = (e.target as HTMLInputElement).files?.[0];
                              if (file) {
                                const reader = new FileReader();
                                reader.onload = (e) => {
                                  const { updateSettings } = useSettingsStore.getState();
                                  updateSettings({ companyLogo: e.target?.result as string });
                                };
                                reader.readAsDataURL(file);
                              }
                            };
                            input.click();
                          }}
                          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                        >
                          تغيير الشعار
                        </button>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <svg className="w-16 h-16 text-gray-400 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd"/>
                        </svg>
                        <p className="text-gray-600">اضغط لرفع شعار الشركة</p>
                        <button
                          onClick={() => {
                            const input = document.createElement('input');
                            input.type = 'file';
                            input.accept = 'image/*';
                            input.onchange = (e) => {
                              const file = (e.target as HTMLInputElement).files?.[0];
                              if (file) {
                                const reader = new FileReader();
                                reader.onload = (e) => {
                                  const { updateSettings } = useSettingsStore.getState();
                                  updateSettings({ companyLogo: e.target?.result as string });
                                };
                                reader.readAsDataURL(file);
                              }
                            };
                            input.click();
                          }}
                          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                        >
                          رفع الشعار
                        </button>
                      </div>
                    )}
                  </div>
                </div>

                {/* Company Stamp Upload */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-3">ختم الشركة</label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-green-400 transition-colors">
                    {settings.companyStamp ? (
                      <div className="space-y-4">
                        <img
                          src={settings.companyStamp}
                          alt="ختم الشركة"
                          className="w-24 h-24 object-contain mx-auto border border-gray-200 rounded-lg bg-white p-2"
                        />
                        <button
                          onClick={() => {
                            const input = document.createElement('input');
                            input.type = 'file';
                            input.accept = 'image/*';
                            input.onchange = (e) => {
                              const file = (e.target as HTMLInputElement).files?.[0];
                              if (file) {
                                const reader = new FileReader();
                                reader.onload = (e) => {
                                  const { updateSettings } = useSettingsStore.getState();
                                  updateSettings({ companyStamp: e.target?.result as string });
                                };
                                reader.readAsDataURL(file);
                              }
                            };
                            input.click();
                          }}
                          className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors"
                        >
                          تغيير الختم
                        </button>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <svg className="w-12 h-12 text-gray-400 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z" clipRule="evenodd"/>
                        </svg>
                        <p className="text-gray-600">اضغط لرفع ختم الشركة</p>
                        <button
                          onClick={() => {
                            const input = document.createElement('input');
                            input.type = 'file';
                            input.accept = 'image/*';
                            input.onchange = (e) => {
                              const file = (e.target as HTMLInputElement).files?.[0];
                              if (file) {
                                const reader = new FileReader();
                                reader.onload = (e) => {
                                  const { updateSettings } = useSettingsStore.getState();
                                  updateSettings({ companyStamp: e.target?.result as string });
                                };
                                reader.readAsDataURL(file);
                              }
                            };
                            input.click();
                          }}
                          className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors"
                        >
                          رفع الختم
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="bg-gray-50 px-6 py-4 rounded-b-lg border-t">
              <div className="flex justify-between items-center">
                <p className="text-sm text-gray-600">
                  سيتم حفظ الإعدادات تلقائياً عند التغيير
                </p>
                <button
                  onClick={() => setShowCompanySettings(false)}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors font-medium"
                >
                  إغلاق
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default InvoicePreview;
