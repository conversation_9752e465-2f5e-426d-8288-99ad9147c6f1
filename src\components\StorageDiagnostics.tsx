'use client';

import React, { useState, useEffect } from 'react';
import { diagnoseStorageIssue, repairStorageData, checkStorageHealth } from '@/utils/cleanupStorage';

interface DiagnosticsResult {
  timestamp: string;
  issues: string[];
  recommendations: string[];
  data: any;
}

const StorageDiagnostics: React.FC = () => {
  const [diagnostics, setDiagnostics] = useState<DiagnosticsResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [repairResult, setRepairResult] = useState<any>(null);

  // تشغيل التشخيص تلقائياً عند تحميل المكون
  useEffect(() => {
    runDiagnostics();
  }, []);

  const runDiagnostics = () => {
    setIsLoading(true);
    try {
      const result = diagnoseStorageIssue();
      setDiagnostics(result);
      console.log('🔍 Storage Diagnostics Result:', result);
    } catch (error) {
      console.error('❌ Error running diagnostics:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const runRepair = () => {
    setIsLoading(true);
    try {
      const result = repairStorageData();
      setRepairResult(result);
      console.log('🔧 Storage Repair Result:', result);
      
      // إعادة تشغيل التشخيص بعد الإصلاح
      setTimeout(() => {
        runDiagnostics();
      }, 500);
    } catch (error) {
      console.error('❌ Error running repair:', error);
      setRepairResult({
        success: false,
        message: 'فشل في تشغيل عملية الإصلاح',
        error: error
      });
    } finally {
      setIsLoading(false);
    }
  };

  const createTestData = () => {
    try {
      const testInvoices = [
        {
          id: 'INV-TEST-001',
          client: {
            name: 'عميل تجريبي',
            phone: '01234567890',
            contactMethod: 'whatsapp',
            clientCode: 'TEST001'
          },
          services: [
            {
              type: 'consultation',
              description: 'استشارة تجريبية',
              price: 100,
              quantity: 1,
              total: 100
            }
          ],
          total: 100,
          paidAmount: 50,
          remainingAmount: 50,
          paymentMethod: 'cash',
          paymentStatus: 'مدفوع جزئياً',
          createdAt: new Date().toISOString()
        }
      ];

      localStorage.setItem('invoices', JSON.stringify(testInvoices));
      
      // إنشاء إعدادات تجريبية
      const testSettings = {
        companyName: 'شركة تجريبية',
        email: '<EMAIL>',
        complaintsPhone: '01234567890',
        address: 'عنوان تجريبي'
      };

      localStorage.setItem('company-settings', JSON.stringify(testSettings));

      alert('تم إنشاء بيانات تجريبية بنجاح!');
      runDiagnostics();
    } catch (error) {
      console.error('❌ Error creating test data:', error);
      alert('فشل في إنشاء البيانات التجريبية');
    }
  };

  const clearAllData = () => {
    if (confirm('هل أنت متأكد من حذف جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
      try {
        localStorage.removeItem('invoices');
        localStorage.removeItem('company-settings');
        localStorage.removeItem('invoiceCounters');
        alert('تم حذف جميع البيانات');
        runDiagnostics();
      } catch (error) {
        console.error('❌ Error clearing data:', error);
        alert('فشل في حذف البيانات');
      }
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg" dir="rtl">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-800 mb-4">تشخيص قاعدة البيانات المحلية</h2>
        
        <div className="flex gap-3 mb-6">
          <button
            onClick={runDiagnostics}
            disabled={isLoading}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors disabled:opacity-50"
          >
            {isLoading ? 'جاري التشخيص...' : 'إعادة التشخيص'}
          </button>
          
          <button
            onClick={runRepair}
            disabled={isLoading}
            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors disabled:opacity-50"
          >
            {isLoading ? 'جاري الإصلاح...' : 'إصلاح البيانات'}
          </button>
          
          <button
            onClick={createTestData}
            className="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg transition-colors"
          >
            إنشاء بيانات تجريبية
          </button>
          
          <button
            onClick={clearAllData}
            className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
          >
            حذف جميع البيانات
          </button>
        </div>
      </div>

      {/* نتائج التشخيص */}
      {diagnostics && (
        <div className="mb-6">
          <h3 className="text-xl font-bold text-gray-700 mb-3">نتائج التشخيص</h3>
          
          {/* المشاكل المكتشفة */}
          {diagnostics.issues.length > 0 ? (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
              <h4 className="font-bold text-red-700 mb-2">المشاكل المكتشفة:</h4>
              <ul className="list-disc list-inside text-red-600">
                {diagnostics.issues.map((issue, index) => (
                  <li key={index}>{issue}</li>
                ))}
              </ul>
            </div>
          ) : (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
              <p className="text-green-700 font-semibold">✅ لم يتم اكتشاف أي مشاكل</p>
            </div>
          )}

          {/* التوصيات */}
          {diagnostics.recommendations.length > 0 && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
              <h4 className="font-bold text-blue-700 mb-2">التوصيات:</h4>
              <ul className="list-disc list-inside text-blue-600">
                {diagnostics.recommendations.map((rec, index) => (
                  <li key={index}>{rec}</li>
                ))}
              </ul>
            </div>
          )}

          {/* بيانات التشخيص */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <h4 className="font-bold text-gray-700 mb-2">معلومات التخزين:</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-semibold">عدد الفواتير:</span> {diagnostics.data.invoicesCount || 0}
              </div>
              <div>
                <span className="font-semibold">حجم التخزين:</span> {diagnostics.data.totalStorageSize || 'غير محدد'}
              </div>
              <div>
                <span className="font-semibold">عدد مفاتيح التخزين:</span> {diagnostics.data.storageLength || 0}
              </div>
              <div>
                <span className="font-semibold">وقت التشخيص:</span> {new Date(diagnostics.timestamp).toLocaleString('ar-EG')}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* نتائج الإصلاح */}
      {repairResult && (
        <div className="mb-6">
          <h3 className="text-xl font-bold text-gray-700 mb-3">نتائج الإصلاح</h3>
          <div className={`border rounded-lg p-4 ${repairResult.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
            <p className={`font-semibold mb-2 ${repairResult.success ? 'text-green-700' : 'text-red-700'}`}>
              {repairResult.message}
            </p>
            {repairResult.repairs && repairResult.repairs.length > 0 && (
              <div>
                <h4 className="font-bold text-gray-700 mb-1">الإصلاحات المطبقة:</h4>
                <ul className="list-disc list-inside text-sm text-gray-600">
                  {repairResult.repairs.map((repair: string, index: number) => (
                    <li key={index}>{repair}</li>
                  ))}
                </ul>
              </div>
            )}
            {repairResult.finalInvoicesCount !== undefined && (
              <p className="text-sm text-gray-600 mt-2">
                عدد الفواتير النهائي: {repairResult.finalInvoicesCount}
              </p>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default StorageDiagnostics;
