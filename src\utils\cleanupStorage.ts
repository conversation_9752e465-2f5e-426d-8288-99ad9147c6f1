// أداة تنظيف التخزين المحلي
export const cleanupLocalStorage = () => {
  try {
    // تنظيف البيانات التجريبية والمكررة
    const invoices = JSON.parse(localStorage.getItem('invoices') || '[]');
    
    // إزالة الفواتير المكررة
    const uniqueInvoices = invoices.filter((invoice: any, index: number, self: any[]) =>
      index === self.findIndex(inv => inv.id === invoice.id)
    );
    
    // حفظ البيانات المنظفة
    localStorage.setItem('invoices', JSON.stringify(uniqueInvoices));
    
    // تنظيف أي بيانات قديمة أو غير مستخدمة
    const keysToRemove = [
      'test-invoices',
      'demo-data',
      'backup-invoices',
      'temp-invoice',
      'draft-invoice'
    ];
    
    keysToRemove.forEach(key => {
      if (localStorage.getItem(key)) {
        localStorage.removeItem(key);
      }
    });
    
    return {
      success: true,
      message: `تم تنظيف التخزين بنجاح. الفواتير المحفوظة: ${uniqueInvoices.length}`,
      invoicesCount: uniqueInvoices.length
    };
  } catch (error) {
    return {
      success: false,
      message: 'فشل في تنظيف التخزين',
      error: error
    };
  }
};

// دالة إعادة تعيين كامل للتخزين (للاستخدام في حالات الطوارئ)
export const resetLocalStorage = () => {
  try {
    // حفظ نسخة احتياطية قبل الحذف
    const backup = {
      invoices: localStorage.getItem('invoices'),
      settings: localStorage.getItem('company-settings'),
      timestamp: new Date().toISOString()
    };
    
    // حفظ النسخة الاحتياطية
    localStorage.setItem('backup-' + Date.now(), JSON.stringify(backup));
    
    // مسح جميع البيانات
    localStorage.clear();
    
    return {
      success: true,
      message: 'تم إعادة تعيين التخزين مع حفظ نسخة احتياطية',
      backup: backup
    };
  } catch (error) {
    return {
      success: false,
      message: 'فشل في إعادة تعيين التخزين',
      error: error
    };
  }
};

// دالة فحص حالة التخزين
export const checkStorageHealth = () => {
  try {
    const invoices = JSON.parse(localStorage.getItem('invoices') || '[]');
    const settings = JSON.parse(localStorage.getItem('company-settings') || '{}');

    // فحص الفواتير المكررة
    const duplicates = invoices.length - new Set(invoices.map((inv: any) => inv.id)).size;

    // فحص حجم التخزين
    const storageSize = new Blob([JSON.stringify(invoices)]).size;

    return {
      invoicesCount: invoices.length,
      duplicatesCount: duplicates,
      storageSize: Math.round(storageSize / 1024) + ' KB',
      hasSettings: Object.keys(settings).length > 0,
      health: duplicates === 0 ? 'جيد' : 'يحتاج تنظيف'
    };
  } catch (error) {
    return {
      error: 'فشل في فحص حالة التخزين',
      health: 'خطأ'
    };
  }
};

// دالة تشخيص شاملة لمشكلة localStorage
export const diagnoseStorageIssue = () => {
  const diagnosis = {
    timestamp: new Date().toISOString(),
    issues: [] as string[],
    recommendations: [] as string[],
    data: {} as any
  };

  try {
    // 1. فحص توفر localStorage
    if (typeof Storage === "undefined") {
      diagnosis.issues.push("localStorage غير مدعوم في هذا المتصفح");
      diagnosis.recommendations.push("استخدم متصفح حديث يدعم localStorage");
      return diagnosis;
    }

    // 2. فحص البيانات الحالية
    const invoicesRaw = localStorage.getItem('invoices');
    const settingsRaw = localStorage.getItem('company-settings');
    const countersRaw = localStorage.getItem('invoiceCounters');

    diagnosis.data = {
      invoicesRaw: invoicesRaw ? invoicesRaw.substring(0, 100) + '...' : null,
      settingsRaw: settingsRaw ? settingsRaw.substring(0, 100) + '...' : null,
      countersRaw: countersRaw ? countersRaw.substring(0, 100) + '...' : null,
      storageKeys: Object.keys(localStorage),
      storageLength: localStorage.length
    };

    // 3. فحص صحة البيانات
    let invoices = [];
    let settings = {};
    let counters = {};

    try {
      invoices = JSON.parse(invoicesRaw || '[]');
      if (!Array.isArray(invoices)) {
        diagnosis.issues.push("بيانات الفواتير ليست في صيغة مصفوفة صحيحة");
        invoices = [];
      }
    } catch (error) {
      diagnosis.issues.push("فشل في تحليل بيانات الفواتير JSON");
      diagnosis.recommendations.push("إعادة تعيين بيانات الفواتير");
    }

    try {
      settings = JSON.parse(settingsRaw || '{}');
      if (typeof settings !== 'object') {
        diagnosis.issues.push("بيانات الإعدادات ليست في صيغة كائن صحيحة");
        settings = {};
      }
    } catch (error) {
      diagnosis.issues.push("فشل في تحليل بيانات الإعدادات JSON");
    }

    try {
      counters = JSON.parse(countersRaw || '{}');
    } catch (error) {
      diagnosis.issues.push("فشل في تحليل بيانات العدادات JSON");
    }

    // 4. فحص تكامل البيانات
    diagnosis.data.invoicesCount = invoices.length;
    diagnosis.data.settingsKeys = Object.keys(settings);
    diagnosis.data.countersKeys = Object.keys(counters);

    // فحص الفواتير المكررة
    const uniqueIds = new Set(invoices.map((inv: any) => inv.id));
    const duplicatesCount = invoices.length - uniqueIds.size;
    if (duplicatesCount > 0) {
      diagnosis.issues.push(`يوجد ${duplicatesCount} فاتورة مكررة`);
      diagnosis.recommendations.push("تشغيل عملية تنظيف البيانات");
    }

    // فحص صحة هيكل الفواتير
    const invalidInvoices = invoices.filter((inv: any) =>
      !inv.id || !inv.client || !inv.services || !Array.isArray(inv.services)
    );
    if (invalidInvoices.length > 0) {
      diagnosis.issues.push(`يوجد ${invalidInvoices.length} فاتورة بهيكل غير صحيح`);
      diagnosis.recommendations.push("إصلاح أو حذف الفواتير التالفة");
    }

    // 5. فحص حجم التخزين
    const totalSize = JSON.stringify(localStorage).length;
    const maxSize = 5 * 1024 * 1024; // 5MB تقريباً
    if (totalSize > maxSize * 0.8) {
      diagnosis.issues.push("حجم التخزين قريب من الحد الأقصى");
      diagnosis.recommendations.push("تنظيف البيانات القديمة أو غير المستخدمة");
    }

    diagnosis.data.totalStorageSize = Math.round(totalSize / 1024) + ' KB';

    // 6. التوصيات العامة
    if (diagnosis.issues.length === 0) {
      diagnosis.recommendations.push("البيانات تبدو سليمة");
    } else {
      diagnosis.recommendations.push("تشغيل أدوات الإصلاح المتاحة");
    }

    return diagnosis;

  } catch (error) {
    diagnosis.issues.push(`خطأ في التشخيص: ${error}`);
    return diagnosis;
  }
};

// دالة إصلاح البيانات التالفة
export const repairStorageData = () => {
  try {
    const diagnosis = diagnoseStorageIssue();
    const repairs = [];

    // إصلاح بيانات الفواتير
    let invoices = [];
    try {
      const invoicesRaw = localStorage.getItem('invoices');
      if (invoicesRaw) {
        invoices = JSON.parse(invoicesRaw);
        if (!Array.isArray(invoices)) {
          invoices = [];
          repairs.push("تم إعادة تعيين بيانات الفواتير إلى مصفوفة فارغة");
        }
      }
    } catch (error) {
      invoices = [];
      repairs.push("تم إنشاء مصفوفة فواتير جديدة بسبب تلف البيانات");
    }

    // إزالة الفواتير المكررة
    const uniqueInvoices = invoices.filter((invoice: any, index: number, self: any[]) =>
      index === self.findIndex(inv => inv.id === invoice.id)
    );

    if (uniqueInvoices.length !== invoices.length) {
      repairs.push(`تم إزالة ${invoices.length - uniqueInvoices.length} فاتورة مكررة`);
      invoices = uniqueInvoices;
    }

    // إصلاح الفواتير التالفة
    const validInvoices = invoices.filter((inv: any) => {
      if (!inv.id || !inv.client || !inv.services || !Array.isArray(inv.services)) {
        return false;
      }
      return true;
    });

    if (validInvoices.length !== invoices.length) {
      repairs.push(`تم إزالة ${invoices.length - validInvoices.length} فاتورة تالفة`);
      invoices = validInvoices;
    }

    // حفظ البيانات المصلحة
    localStorage.setItem('invoices', JSON.stringify(invoices));

    // إصلاح الإعدادات
    let settings = {};
    try {
      const settingsRaw = localStorage.getItem('company-settings');
      if (settingsRaw) {
        settings = JSON.parse(settingsRaw);
        if (typeof settings !== 'object') {
          settings = {};
          repairs.push("تم إعادة تعيين إعدادات الشركة");
        }
      }
    } catch (error) {
      settings = {};
      repairs.push("تم إنشاء إعدادات شركة جديدة");
    }

    localStorage.setItem('company-settings', JSON.stringify(settings));

    return {
      success: true,
      message: "تم إصلاح البيانات بنجاح",
      repairs: repairs,
      finalInvoicesCount: invoices.length
    };

  } catch (error) {
    return {
      success: false,
      message: "فشل في إصلاح البيانات",
      error: error
    };
  }
};
