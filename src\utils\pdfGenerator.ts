import { jsPDF } from 'jspdf';
import html2canvas from 'html2canvas';
import { Invoice } from '@/types';

// دالة تنسيق العملة المحسنة
const formatCurrency = (amount: number | string): string => {
  const numericAmount = typeof amount === 'number' ? amount : parseFloat(String(amount || 0));

  if (isNaN(numericAmount)) {
    return '0.00 جنيه';
  }

  return new Intl.NumberFormat('ar-EG', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(numericAmount) + ' جنيه';
};

// دالة الحصول على اسم الخدمة
const getServiceName = (serviceType: string): string => {
  const serviceNames: Record<string, string> = {
    consultation: 'استشارة',
    pattern: 'باترون',
    pattern_printing: 'طباعة باترون',
    manufacturing: 'تصنيع',
    samples: 'عينات',
    shipping: 'شحن',
    marketing: 'تسويق',
    photography: 'تصوير',
    products: 'منتجات',
    raw_purchases: 'مشتريات خام'
  };

  return serviceNames[serviceType] || serviceType;
};

// دالة الحصول على اسم طريقة الدفع
const getPaymentMethodName = (method: string): string => {
  const methods: Record<string, string> = {
    cash: 'نقدي',
    bank_transfer: 'تحويل بنكي',
    credit_card: 'بطاقة ائتمان',
    installments: 'أقساط'
  };

  return methods[method] || method;
};

// دالة الحصول على اسم وسيلة التواصل
const getContactMethodName = (method: string): string => {
  const methods: Record<string, string> = {
    phone: 'هاتف',
    whatsapp: 'واتساب',
    email: 'بريد إلكتروني',
    facebook: 'فيسبوك',
    instagram: 'انستغرام',
    direct: 'مباشر',
    meta: 'ميتا',
    meeting: 'لقاء مباشر'
  };

  return methods[method] || method;
};

// دالة تنسيق التاريخ
const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('ar-EG', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

// دالة حساب تفاصيل الخدمة
const calculateServiceDetails = (service: any) => {
  let serviceTotal = 0;
  let serviceDetails = '';
  let quantity = '';
  let unitPrice = 0;

  switch (service.serviceType) {
    case 'consultation':
      serviceTotal = service.cost || 0;
      serviceDetails = `${service.topic || 'استشارة'} - ${service.hours || 0} ساعة`;
      quantity = `${service.hours || 0} ساعة`;
      unitPrice = service.cost || 0;
      break;

    case 'pattern':
      serviceTotal = service.models?.reduce((sum: number, model: any) => sum + (model.finalAmount || 0), 0) || 0;
      serviceDetails = `${service.models?.length || 0} نموذج باترون`;
      quantity = `${service.models?.length || 0} نموذج`;
      unitPrice = serviceTotal / (service.models?.length || 1);
      break;

    case 'pattern_printing':
      serviceTotal = service.files?.reduce((sum: number, file: any) => sum + (file.cost || 0), 0) || 0;
      serviceDetails = `طباعة ${service.files?.length || 0} ملف`;
      quantity = `${service.files?.reduce((sum: number, file: any) => sum + (file.meters || 0), 0) || 0} متر`;
      unitPrice = serviceTotal;
      break;

    case 'shipping':
      serviceTotal = service.total || 0;
      serviceDetails = `شحن ${service.item || 'منتج'}`;
      quantity = `${service.quantity || 1}`;
      unitPrice = service.unitPrice || 0;
      break;

    case 'products':
      serviceTotal = service.items?.reduce((sum: number, item: any) => sum + (item.total || 0), 0) || 0;
      serviceDetails = `${service.items?.length || 0} منتج`;
      quantity = `${service.items?.reduce((sum: number, item: any) => sum + (item.quantity || 0), 0) || 0}`;
      unitPrice = serviceTotal / (service.items?.reduce((sum: number, item: any) => sum + (item.quantity || 0), 0) || 1);
      break;

    case 'manufacturing':
      serviceTotal = service.models?.reduce((sum: number, model: any) =>
        sum + ((model.patternPrice || 0) + (model.samplePrice || 0) + (model.manufacturingPrice || 0)) * (model.quantity || 1), 0) || 0;
      serviceDetails = `تصنيع ${service.models?.length || 0} نموذج`;
      quantity = `${service.models?.reduce((sum: number, model: any) => sum + (model.quantity || 0), 0) || 0}`;
      unitPrice = serviceTotal / (service.models?.reduce((sum: number, model: any) => sum + (model.quantity || 0), 0) || 1);
      break;

    case 'photography':
      serviceTotal = service.cost || 0;
      serviceDetails = `تصوير ${service.location === 'indoor' ? 'داخلي' : 'خارجي'}${service.withModel ? ' مع موديل' : ''}`;
      quantity = '1 جلسة';
      unitPrice = service.cost || 0;
      break;

    case 'marketing':
      serviceTotal = service.cost || 0;
      serviceDetails = service.description || 'خدمات تسويقية';
      quantity = service.duration || '1 شهر';
      unitPrice = service.cost || 0;
      break;

    case 'samples':
      serviceTotal = service.cost || 0;
      serviceDetails = 'إنتاج عينات';
      quantity = '1';
      unitPrice = service.cost || 0;
      break;

    case 'raw_purchases':
      serviceTotal = service.materials?.reduce((sum: number, material: any) => sum + (material.total || 0), 0) || 0;
      serviceDetails = `${service.materials?.length || 0} مادة خام`;
      quantity = `${service.materials?.reduce((sum: number, material: any) => sum + (material.quantity || 0), 0) || 0}`;
      unitPrice = serviceTotal / (service.materials?.reduce((sum: number, material: any) => sum + (material.quantity || 0), 0) || 1);
      break;

    default:
      serviceTotal = service.cost || service.total || 0;
      serviceDetails = getServiceName(service.serviceType);
      quantity = '1';
      unitPrice = serviceTotal;
  }

  return {
    total: serviceTotal,
    details: serviceDetails,
    quantity,
    unitPrice
  };
};

// الدالة الرئيسية لتصدير PDF المحسنة
export const generateInvoicePDF = async (invoice: Invoice): Promise<void> => {
  try {
    // البحث عن عنصر المعاينة
    const element = document.getElementById('invoice-content');

    if (!element) {
      throw new Error('عنصر المعاينة غير موجود');
    }

    // التحقق من صحة العنصر
    const rect = element.getBoundingClientRect();
    if (rect.width === 0 || rect.height === 0) {
      throw new Error('العنصر غير مرئي أو لا يحتوي على محتوى');
    }

    await generatePDFFromPreview(element, invoice);
  } catch (error) {
    console.error('Error generating PDF:', error);
    throw new Error('فشل في إنشاء ملف PDF: ' + (error as Error).message);
  }
};

// دالة تصدير PDF محسنة من المعاينة
const generatePDFFromPreview = async (element: HTMLElement, invoice: Invoice): Promise<void> => {
  let canvas: HTMLCanvasElement | null = null;

  try {
    // تحسين العنصر للتصدير
    const originalStyle = element.style.cssText;
    element.style.cssText = `
      ${originalStyle}
      width: 794px !important;
      max-width: 794px !important;
      min-height: 1123px !important;
      background: white !important;
      font-family: Arial, sans-serif !important;
      direction: rtl !important;
      overflow: visible !important;
    `;

    // انتظار تحديث التخطيط
    await new Promise(resolve => setTimeout(resolve, 100));

    // إنشاء canvas محسن
    canvas = await html2canvas(element, {
      scale: 2,                    // جودة عالية
      useCORS: true,
      allowTaint: false,
      backgroundColor: '#ffffff',
      logging: false,
      width: 794,                  // عرض A4 بدقة 96 DPI
      height: 1123,               // ارتفاع A4 بدقة 96 DPI
      scrollX: 0,
      scrollY: 0,
      windowWidth: 794,
      windowHeight: 1123,
      foreignObjectRendering: false,
      imageTimeout: 15000,
      removeContainer: true
    });

    // استعادة الستايل الأصلي
    element.style.cssText = originalStyle;

    if (!canvas) {
      throw new Error('فشل في إنشاء Canvas');
    }

    // إنشاء PDF محسن
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'pt',                 // استخدام النقاط للدقة
      format: 'a4',
      compress: true,
      precision: 2
    });

    // تحويل Canvas إلى صورة عالية الجودة
    const imgData = canvas.toDataURL('image/jpeg', 0.95);

    // حساب الأبعاد بدقة
    const pdfWidth = 595.28;      // عرض A4 بالنقاط
    const pdfHeight = 841.89;     // ارتفاع A4 بالنقاط
    const margin = 40;            // هامش 40 نقطة
    const maxWidth = pdfWidth - (margin * 2);
    const maxHeight = pdfHeight - (margin * 2);

    // حساب النسبة والأبعاد النهائية
    const canvasRatio = canvas.width / canvas.height;
    const pdfRatio = maxWidth / maxHeight;

    let finalWidth, finalHeight;

    if (canvasRatio > pdfRatio) {
      finalWidth = maxWidth;
      finalHeight = maxWidth / canvasRatio;
    } else {
      finalHeight = maxHeight;
      finalWidth = maxHeight * canvasRatio;
    }

    // توسيط الصورة
    const x = margin + (maxWidth - finalWidth) / 2;
    const y = margin + (maxHeight - finalHeight) / 2;

    // إضافة الصورة للPDF
    pdf.addImage(imgData, 'JPEG', x, y, finalWidth, finalHeight, undefined, 'FAST');

    // إنشاء اسم الملف محسن
    const date = new Date().toISOString().split('T')[0];
    const sanitizedClientName = invoice.client.name
      .replace(/[^\u0600-\u06FF\w\s-]/g, '')  // السماح بالأحرف العربية
      .trim()
      .substring(0, 20);  // تحديد الطول

    const filename = `فاتورة_${sanitizedClientName}_${invoice.id}_${date}.pdf`;

    // حفظ الملف
    pdf.save(filename);

  } catch (error) {
    throw new Error('فشل في إنشاء PDF: ' + (error as Error).message);
  } finally {
    // تنظيف الذاكرة
    if (canvas) {
      canvas.remove();
      canvas = null;
    }
  }
};

// دالة إنشاء PDF من HTML مباشرة (بديل محسن)
export const generateInvoicePDFFromHTML = async (invoice: Invoice): Promise<void> => {
  try {
    // الحصول على إعدادات الشركة
    const settings = JSON.parse(localStorage.getItem('company-settings') || '{}');

    // إنشاء HTML محسن للطباعة
    const htmlContent = createPrintableInvoiceHTML(invoice, settings);

    // إنشاء نافذة جديدة للطباعة
    const printWindow = window.open('', '_blank', 'width=794,height=1123');
    if (!printWindow) {
      throw new Error('فشل في فتح نافذة الطباعة');
    }

    // كتابة المحتوى
    printWindow.document.write(htmlContent);
    printWindow.document.close();

    // انتظار تحميل المحتوى
    await new Promise((resolve) => {
      printWindow.onload = resolve;
      setTimeout(resolve, 1000); // fallback
    });

    // البحث عن العنصر في النافذة الجديدة
    const element = printWindow.document.querySelector('.invoice-container') as HTMLElement;
    if (!element) {
      printWindow.close();
      throw new Error('عنصر الفاتورة غير موجود');
    }

    // إنشاء canvas من النافذة الجديدة
    const canvas = await html2canvas(element, {
      scale: 2,
      useCORS: true,
      allowTaint: false,
      backgroundColor: '#ffffff',
      logging: false,
      width: 794,
      height: 1123,
      foreignObjectRendering: false,
      imageTimeout: 15000
    });

    // إغلاق النافذة
    printWindow.close();

    if (!canvas) {
      throw new Error('فشل في إنشاء Canvas');
    }

    // إنشاء PDF
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'pt',
      format: 'a4',
      compress: true
    });

    const imgData = canvas.toDataURL('image/jpeg', 0.95);
    pdf.addImage(imgData, 'JPEG', 0, 0, 595.28, 841.89, undefined, 'FAST');

    // حفظ الملف
    const date = new Date().toISOString().split('T')[0];
    const sanitizedClientName = invoice.client.name
      .replace(/[^\u0600-\u06FF\w\s-]/g, '')
      .trim()
      .substring(0, 20);

    const filename = `فاتورة_${sanitizedClientName}_${invoice.id}_${date}.pdf`;
    pdf.save(filename);

  } catch (error) {
    console.error('Error generating PDF from HTML:', error);
    throw new Error('فشل في إنشاء PDF: ' + (error as Error).message);
  }
};

// دالة إنشاء HTML محسن للطباعة
const createPrintableInvoiceHTML = (invoice: Invoice, settings: any): string => {
  const logoSrc = settings.companyLogo || '';
  const stampSrc = settings.companyStamp || '';

  return `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة ${invoice.client.name}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            font-size: 12pt;
            line-height: 1.4;
            color: #333;
            background: white;
            direction: rtl;
        }

        .invoice-container {
            width: 794px;
            min-height: 1123px;
            margin: 0 auto;
            padding: 40px;
            background: white;
            position: relative;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #2196f3;
        }

        .invoice-info {
            flex: 1;
            max-width: 350px;
        }

        .invoice-info h2 {
            font-size: 24pt;
            color: #2196f3;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .info-box {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 4px 0;
        }

        .info-label {
            font-weight: bold;
            color: #495057;
            min-width: 120px;
        }

        .info-value {
            color: #212529;
            font-weight: 500;
        }

        .logo-section {
            flex-shrink: 0;
            text-align: center;
            margin-left: 30px;
        }

        .logo-section img {
            width: 180px;
            height: 180px;
            object-fit: contain;
            border-radius: 8px;
        }

        .company-description {
            margin-top: 10px;
            font-size: 11pt;
            color: #666;
            max-width: 180px;
            line-height: 1.3;
        }

        .services-section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 16pt;
            font-weight: bold;
            color: #2196f3;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e9ecef;
        }

        .services-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 11pt;
        }

        .services-table th {
            background: #2196f3;
            color: white;
            padding: 12px 8px;
            text-align: center;
            font-weight: bold;
            border: 1px solid #1976d2;
        }

        .services-table td {
            padding: 10px 8px;
            text-align: center;
            border: 1px solid #ddd;
            vertical-align: middle;
        }

        .services-table tbody tr:nth-child(even) {
            background: #f8f9fa;
        }

        .services-table tbody tr:hover {
            background: #e3f2fd;
        }

        .service-type {
            font-weight: bold;
            color: #2196f3;
        }

        .service-details {
            text-align: right;
            padding-right: 12px;
        }

        .service-price,
        .service-total {
            font-weight: bold;
            color: #4caf50;
        }

        .total-section {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .total-details {
            flex: 1;
        }

        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            font-size: 13pt;
        }

        .total-label {
            font-weight: bold;
            color: #495057;
        }

        .total-value {
            font-weight: bold;
            color: #212529;
        }

        .grand-total {
            color: #2196f3;
            font-size: 16pt;
        }

        .paid-amount {
            color: #4caf50;
        }

        .remaining-amount {
            color: #ff9800;
        }

        .remaining-zero {
            color: #4caf50;
        }

        .payment-stamp {
            flex-shrink: 0;
            margin-left: 30px;
            text-align: center;
            background: #e3f2fd;
            padding: 15px;
            border-radius: 50%;
            width: 120px;
            height: 120px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            border: 3px solid #2196f3;
            transform: rotate(-5deg);
        }

        .stamp-title {
            font-size: 14pt;
            font-weight: bold;
            color: #2196f3;
            margin-bottom: 5px;
        }

        .stamp-status {
            font-size: 10pt;
            color: #1976d2;
            font-weight: bold;
        }

        .footer {
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            position: relative;
        }

        .footer-left {
            flex: 1;
        }

        .footer-right {
            flex: 1;
            text-align: left;
            position: relative;
        }

        .contact-info,
        .headquarters-info {
            font-size: 11pt;
            line-height: 1.5;
            color: #495057;
        }

        .contact-info div,
        .headquarters-info div {
            margin-bottom: 5px;
        }

        .company-stamp {
            position: absolute;
            bottom: 0;
            right: 0;
            transform: rotate(-8deg);
            opacity: 0.9;
        }

        .company-stamp img {
            width: 100px;
            height: 100px;
            object-fit: contain;
        }

        .stamp-content {
            background: rgba(33, 150, 243, 0.1);
            border: 2px solid #2196f3;
            border-radius: 50%;
            width: 100px;
            height: 100px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #2196f3;
        }

        .stamp-title {
            font-size: 14pt;
            margin-bottom: 2px;
        }

        .stamp-subtitle {
            font-size: 10pt;
        }

        .thank-you {
            text-align: center;
            margin-top: 30px;
            font-size: 14pt;
            font-weight: bold;
            color: #2196f3;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        @media print {
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            .invoice-container {
                width: 100%;
                max-width: none;
                margin: 0;
                padding: 20px;
            }

            .header,
            .services-section,
            .total-section,
            .footer {
                page-break-inside: avoid;
                break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <!-- Header Section -->
        <div class="header">
            <div class="invoice-info">
                <h2>فاتورة خدمات</h2>
                <div class="info-box">
                    <div class="info-row">
                        <span class="info-label">رقم الفاتورة:</span>
                        <span class="info-value">${invoice.id}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">التاريخ:</span>
                        <span class="info-value">${formatDate(invoice.createdAt)}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">اسم العميل:</span>
                        <span class="info-value">${invoice.client.name}</span>
                    </div>
                    ${invoice.client.businessName ? `
                    <div class="info-row">
                        <span class="info-label">الاسم التجاري:</span>
                        <span class="info-value">${invoice.client.businessName}</span>
                    </div>
                    ` : ''}
                    <div class="info-row">
                        <span class="info-label">رقم الهاتف:</span>
                        <span class="info-value">${invoice.client.phone}</span>
                    </div>
                    ${invoice.client.province ? `
                    <div class="info-row">
                        <span class="info-label">المحافظة:</span>
                        <span class="info-value">${invoice.client.province}</span>
                    </div>
                    ` : ''}
                    <div class="info-row">
                        <span class="info-label">كود العميل:</span>
                        <span class="info-value">${invoice.client.clientCode || 'تلقائي'}</span>
                    </div>
                    <div class="info-row" style="border-top: 1px solid #ddd; padding-top: 8px; margin-top: 8px;">
                        <span class="info-label">وسيلة التواصل:</span>
                        <span class="info-value">${getContactMethodName(invoice.client.contactMethod)}</span>
                    </div>
                </div>
            </div>

            <div class="logo-section">
                ${logoSrc ? `<img src="${logoSrc}" alt="شعار الشركة">` : `
                <div style="width: 180px; height: 180px; border: 2px dashed #ccc; display: flex; align-items: center; justify-content: center; background: #f9f9f9; border-radius: 8px;">
                    <span style="color: #999; font-size: 14pt;">شعار الشركة</span>
                </div>
                `}
                <div class="company-description">
                    ${settings.description || 'شركة متخصصة في جميع خدمات صناعة الملابس الجاهزة'}
                </div>
            </div>
        </div>

        <!-- Services Section -->
        <div class="services-section">
            <h3 class="section-title">تفاصيل الخدمات والمنتجات</h3>

            <table class="services-table">
                <thead>
                    <tr>
                        <th style="width: 50px;">م</th>
                        <th style="width: 120px;">نوع الخدمة</th>
                        <th style="width: 200px;">التفاصيل</th>
                        <th style="width: 100px;">الكمية/المقاس</th>
                        <th style="width: 120px;">السعر</th>
                        <th style="width: 120px;">المجموع</th>
                    </tr>
                </thead>
                <tbody>
                    ${invoice.services.map((service, index) => {
                      const serviceData = calculateServiceDetails(service);
                      return `
                        <tr>
                            <td>${index + 1}</td>
                            <td class="service-type">${getServiceName(service.serviceType)}</td>
                            <td class="service-details">${serviceData.details}</td>
                            <td>${serviceData.quantity}</td>
                            <td class="service-price">${formatCurrency(serviceData.unitPrice)}</td>
                            <td class="service-total">${formatCurrency(serviceData.total)}</td>
                        </tr>
                      `;
                    }).join('')}
                </tbody>
            </table>
        </div>

        <!-- Total Summary -->
        <div class="total-section">
            <div class="total-details">
                <div class="total-row">
                    <span class="total-label">المجموع الكلي:</span>
                    <span class="total-value grand-total">${formatCurrency(invoice.total)}</span>
                </div>
                <div class="total-row">
                    <span class="total-label">المبلغ المدفوع:</span>
                    <span class="total-value paid-amount">${formatCurrency(invoice.paidAmount || 0)}</span>
                </div>
                <div class="total-row">
                    <span class="total-label">المبلغ المتبقي:</span>
                    <span class="total-value remaining-amount ${(invoice.remainingAmount || 0) > 0 ? 'remaining-positive' : 'remaining-zero'}">
                        ${formatCurrency(invoice.remainingAmount || 0)}
                    </span>
                </div>
                <div class="total-row" style="border-top: 1px solid #4caf50; padding-top: 8px; margin-top: 8px;">
                    <span class="total-label">طريقة الدفع:</span>
                    <span class="total-value">${getPaymentMethodName(invoice.paymentMethod)}</span>
                </div>
            </div>

            <div class="payment-stamp">
                <div class="stamp-title">OKA Group</div>
                <div class="stamp-status">${invoice.paymentStatus || 'غير محدد'}</div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <div class="footer-left">
                <div class="contact-info">
                    <div><strong>معلومات التواصل:</strong></div>
                    <div>📧 البريد الإلكتروني: <EMAIL></div>
                    <div>📞 للشكاوى والاقتراحات: 0114954118</div>
                </div>
            </div>

            <div class="footer-right">
                <div class="headquarters-info">
                    <div><strong>المقر الإداري:</strong></div>
                    <div>73 ش 6 اكتوبر الجراش جسر السويس</div>
                </div>

                <div class="company-stamp">
                    ${stampSrc ? `
                    <img src="${stampSrc}" alt="ختم الشركة">
                    ` : `
                    <div class="stamp-content">
                        <div class="stamp-title">OKA</div>
                        <div class="stamp-subtitle">GROUP</div>
                    </div>
                    `}
                </div>
            </div>
        </div>

        <div class="thank-you">
            شكراً لتعاملكم معنا
        </div>
    </div>
</body>
</html>
  `;
};

// تصدير الدالة الرئيسية
export default generateInvoicePDF;
