<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تشخيص localStorage</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    </style>
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
        <h1 class="text-3xl font-bold text-gray-800 mb-6 text-center">اختبار تشخيص localStorage</h1>
        
        <div class="flex gap-3 mb-6 justify-center">
            <button onclick="runDiagnostics()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                تشغيل التشخيص
            </button>
            <button onclick="createTestData()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
                إنشاء بيانات تجريبية
            </button>
            <button onclick="repairData()" class="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg transition-colors">
                إصلاح البيانات
            </button>
            <button onclick="clearData()" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors">
                حذف البيانات
            </button>
        </div>

        <div id="results" class="space-y-4"></div>
    </div>

    <script>
        // دالة تشخيص localStorage
        function diagnoseStorageIssue() {
            const diagnosis = {
                timestamp: new Date().toISOString(),
                issues: [],
                recommendations: [],
                data: {}
            };

            try {
                // فحص توفر localStorage
                if (typeof Storage === "undefined") {
                    diagnosis.issues.push("localStorage غير مدعوم في هذا المتصفح");
                    diagnosis.recommendations.push("استخدم متصفح حديث يدعم localStorage");
                    return diagnosis;
                }

                // فحص البيانات الحالية
                const invoicesRaw = localStorage.getItem('invoices');
                const settingsRaw = localStorage.getItem('company-settings');
                const countersRaw = localStorage.getItem('invoiceCounters');

                diagnosis.data = {
                    invoicesRaw: invoicesRaw ? invoicesRaw.substring(0, 100) + '...' : null,
                    settingsRaw: settingsRaw ? settingsRaw.substring(0, 100) + '...' : null,
                    countersRaw: countersRaw ? countersRaw.substring(0, 100) + '...' : null,
                    storageKeys: Object.keys(localStorage),
                    storageLength: localStorage.length
                };

                // فحص صحة البيانات
                let invoices = [];
                let settings = {};

                try {
                    invoices = JSON.parse(invoicesRaw || '[]');
                    if (!Array.isArray(invoices)) {
                        diagnosis.issues.push("بيانات الفواتير ليست في صيغة مصفوفة صحيحة");
                        invoices = [];
                    }
                } catch (error) {
                    diagnosis.issues.push("فشل في تحليل بيانات الفواتير JSON");
                    diagnosis.recommendations.push("إعادة تعيين بيانات الفواتير");
                }

                try {
                    settings = JSON.parse(settingsRaw || '{}');
                    if (typeof settings !== 'object') {
                        diagnosis.issues.push("بيانات الإعدادات ليست في صيغة كائن صحيحة");
                        settings = {};
                    }
                } catch (error) {
                    diagnosis.issues.push("فشل في تحليل بيانات الإعدادات JSON");
                }

                // فحص تكامل البيانات
                diagnosis.data.invoicesCount = invoices.length;
                diagnosis.data.settingsKeys = Object.keys(settings);

                // فحص الفواتير المكررة
                const uniqueIds = new Set(invoices.map(inv => inv.id));
                const duplicatesCount = invoices.length - uniqueIds.size;
                if (duplicatesCount > 0) {
                    diagnosis.issues.push(`يوجد ${duplicatesCount} فاتورة مكررة`);
                    diagnosis.recommendations.push("تشغيل عملية تنظيف البيانات");
                }

                // فحص صحة هيكل الفواتير
                const invalidInvoices = invoices.filter(inv => 
                    !inv.id || !inv.client || !inv.services || !Array.isArray(inv.services)
                );
                if (invalidInvoices.length > 0) {
                    diagnosis.issues.push(`يوجد ${invalidInvoices.length} فاتورة بهيكل غير صحيح`);
                    diagnosis.recommendations.push("إصلاح أو حذف الفواتير التالفة");
                }

                // فحص حجم التخزين
                const totalSize = JSON.stringify(localStorage).length;
                diagnosis.data.totalStorageSize = Math.round(totalSize / 1024) + ' KB';

                // التوصيات العامة
                if (diagnosis.issues.length === 0) {
                    diagnosis.recommendations.push("البيانات تبدو سليمة");
                } else {
                    diagnosis.recommendations.push("تشغيل أدوات الإصلاح المتاحة");
                }

                return diagnosis;

            } catch (error) {
                diagnosis.issues.push(`خطأ في التشخيص: ${error}`);
                return diagnosis;
            }
        }

        // دالة إصلاح البيانات
        function repairStorageData() {
            try {
                const repairs = [];

                // إصلاح بيانات الفواتير
                let invoices = [];
                try {
                    const invoicesRaw = localStorage.getItem('invoices');
                    if (invoicesRaw) {
                        invoices = JSON.parse(invoicesRaw);
                        if (!Array.isArray(invoices)) {
                            invoices = [];
                            repairs.push("تم إعادة تعيين بيانات الفواتير إلى مصفوفة فارغة");
                        }
                    }
                } catch (error) {
                    invoices = [];
                    repairs.push("تم إنشاء مصفوفة فواتير جديدة بسبب تلف البيانات");
                }

                // إزالة الفواتير المكررة
                const uniqueInvoices = invoices.filter((invoice, index, self) =>
                    index === self.findIndex(inv => inv.id === invoice.id)
                );

                if (uniqueInvoices.length !== invoices.length) {
                    repairs.push(`تم إزالة ${invoices.length - uniqueInvoices.length} فاتورة مكررة`);
                    invoices = uniqueInvoices;
                }

                // إصلاح الفواتير التالفة
                const validInvoices = invoices.filter(inv => {
                    if (!inv.id || !inv.client || !inv.services || !Array.isArray(inv.services)) {
                        return false;
                    }
                    return true;
                });

                if (validInvoices.length !== invoices.length) {
                    repairs.push(`تم إزالة ${invoices.length - validInvoices.length} فاتورة تالفة`);
                    invoices = validInvoices;
                }

                // حفظ البيانات المصلحة
                localStorage.setItem('invoices', JSON.stringify(invoices));

                // إصلاح الإعدادات
                let settings = {};
                try {
                    const settingsRaw = localStorage.getItem('company-settings');
                    if (settingsRaw) {
                        settings = JSON.parse(settingsRaw);
                        if (typeof settings !== 'object') {
                            settings = {};
                            repairs.push("تم إعادة تعيين إعدادات الشركة");
                        }
                    }
                } catch (error) {
                    settings = {};
                    repairs.push("تم إنشاء إعدادات شركة جديدة");
                }

                localStorage.setItem('company-settings', JSON.stringify(settings));

                return {
                    success: true,
                    message: "تم إصلاح البيانات بنجاح",
                    repairs: repairs,
                    finalInvoicesCount: invoices.length
                };

            } catch (error) {
                return {
                    success: false,
                    message: "فشل في إصلاح البيانات",
                    error: error
                };
            }
        }

        // دالة عرض النتائج
        function displayResults(title, data, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const colors = {
                info: 'bg-blue-50 border-blue-200 text-blue-700',
                success: 'bg-green-50 border-green-200 text-green-700',
                warning: 'bg-yellow-50 border-yellow-200 text-yellow-700',
                error: 'bg-red-50 border-red-200 text-red-700'
            };

            const resultHtml = `
                <div class="border rounded-lg p-4 ${colors[type]}">
                    <h3 class="font-bold text-lg mb-2">${title}</h3>
                    <pre class="text-sm overflow-auto max-h-96">${JSON.stringify(data, null, 2)}</pre>
                </div>
            `;

            resultsDiv.innerHTML = resultHtml + resultsDiv.innerHTML;
        }

        // الدوال المرتبطة بالأزرار
        function runDiagnostics() {
            const diagnosis = diagnoseStorageIssue();
            console.log('🔍 Storage Diagnostics:', diagnosis);
            displayResults('نتائج التشخيص', diagnosis, diagnosis.issues.length > 0 ? 'warning' : 'success');
        }

        function createTestData() {
            try {
                const testInvoices = [
                    {
                        id: 'INV-TEST-001',
                        client: {
                            name: 'عميل تجريبي',
                            phone: '01234567890',
                            contactMethod: 'whatsapp',
                            clientCode: 'TEST001'
                        },
                        services: [
                            {
                                type: 'consultation',
                                description: 'استشارة تجريبية',
                                price: 100,
                                quantity: 1,
                                total: 100
                            }
                        ],
                        total: 100,
                        paidAmount: 50,
                        remainingAmount: 50,
                        paymentMethod: 'cash',
                        paymentStatus: 'مدفوع جزئياً',
                        createdAt: new Date().toISOString()
                    }
                ];

                localStorage.setItem('invoices', JSON.stringify(testInvoices));
                
                const testSettings = {
                    companyName: 'شركة تجريبية',
                    email: '<EMAIL>',
                    complaintsPhone: '01234567890',
                    address: 'عنوان تجريبي'
                };

                localStorage.setItem('company-settings', JSON.stringify(testSettings));

                displayResults('إنشاء البيانات التجريبية', { 
                    message: 'تم إنشاء البيانات التجريبية بنجاح',
                    invoices: testInvoices.length,
                    settings: Object.keys(testSettings).length
                }, 'success');
            } catch (error) {
                displayResults('خطأ في إنشاء البيانات', { error: error.message }, 'error');
            }
        }

        function repairData() {
            const result = repairStorageData();
            console.log('🔧 Storage Repair:', result);
            displayResults('نتائج الإصلاح', result, result.success ? 'success' : 'error');
        }

        function clearData() {
            if (confirm('هل أنت متأكد من حذف جميع البيانات؟')) {
                try {
                    localStorage.removeItem('invoices');
                    localStorage.removeItem('company-settings');
                    localStorage.removeItem('invoiceCounters');
                    displayResults('حذف البيانات', { message: 'تم حذف جميع البيانات بنجاح' }, 'success');
                } catch (error) {
                    displayResults('خطأ في حذف البيانات', { error: error.message }, 'error');
                }
            }
        }

        // تشغيل التشخيص تلقائياً عند تحميل الصفحة
        window.onload = function() {
            runDiagnostics();
        };
    </script>
</body>
</html>
